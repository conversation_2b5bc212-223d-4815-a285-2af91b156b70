{"name": "easy-sniper-bot", "version": "1.0.0", "main": "dist/server.js", "license": "MIT", "scripts": {"build": "webpack --config webpack.config.js", "tsc": "tsc", "start": "nodemon", "dev": "nodemon"}, "dependencies": {"@flashbots/ethers-provider-bundle": "^0.6.2", "@solana/spl-token": "^0.4.8", "@solana/web3.js": "^1.95.1", "@uniswap/sdk": "^3.0.3", "axios": "^1.7.2", "bs58": "^6.0.0", "connect-mongodb-session": "^2.2.0", "cross-fetch": "^4.0.0", "dotenv": "^16.4.5", "easy-sniper-bot": "file:", "ethers": "5.7.2", "express": "^4.17.1", "express-session": "^1.18.0", "fastestsmallesttextencoderdecoder": "^1.0.22", "jito-ts": "^4.1.1", "mongodb": "^6.8.0", "mongoose": "^8.5.1", "node-cron": "^3.0.3", "node-telegram-bot-api": "^0.66.0", "rpc-websockets": "^7.11.2", "socket.io": "^4.7.5", "ts-loader": "^9.5.1", "webpack": "^5.93.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "devDependencies": {"@types/express": "^4.17.13", "@types/express-session": "^1.18.0", "@types/node": "^22.0.0", "@types/node-cron": "^3.0.11", "@types/node-telegram-bot-api": "^0.64.7", "nodemon": "^2.0.15", "ts-node": "^10.9.2", "typescript": "^5.5.4"}}