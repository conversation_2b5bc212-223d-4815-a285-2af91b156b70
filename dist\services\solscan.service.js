"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTokenDetails = exports.sellSolTokenPercentage = exports.handleSolSellAll = exports.handleSpendsol = exports.handleSolChart = exports.handleSolRefresh = exports.askForSolToken = exports.askForSolContractAddress = exports.handleGenerateNewSolWallet = exports.mainSolMenu = exports.updateSolMessageWithTradeOptions = exports.updateSolMessageWithSellOptions = exports.createSolKeyboard = exports.getsolTradeOptionsKeyboard = exports.getSolSellOptionsKeyboard = exports.getSolBalance = exports.handleSOLScanContract = void 0;
exports.getSolTokenBalance = getSolTokenBalance;
exports.fetchAndDisplayActiveSolTrades = fetchAndDisplayActiveSolTrades;
exports.displayCurrentSolTrade = displayCurrentSolTrade;
const user_models_1 = __importDefault(require("../models/user.models"));
const trade_model_1 = __importDefault(require("../models/trade.model"));
const checkSolanaHoneyPot_service_1 = require("../services/checkSolanaHoneyPot.service");
const dexscreener_service_1 = require("../services/dexscreener.service");
const sol_dashboard_1 = require("../screens/dashboards/sol.dashboard");
const wallet_service_1 = require("./wallet.service");
const botConvo_1 = require("../botConvo");
const botConvo_2 = require("../botConvo");
const globalState_1 = require("../globalState");
const solsell_trader_1 = require("../executor/solsell.trader");
const web3_js_1 = require("@solana/web3.js");
let localState = {};
let messageIdStore = {};
const providerUrl = process.env.SOL_PROVIDER_URL;
if (!providerUrl) {
    throw new Error('PROVIDER_URL is not defined in the environment variables.');
}
const connection = new web3_js_1.Connection(providerUrl);
const getTokenTotalSupply = (connection, tokenMintAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const mintPublicKey = new web3_js_1.PublicKey(tokenMintAddress);
        const mintAccountInfo = yield connection.getParsedAccountInfo(mintPublicKey);
        if (!mintAccountInfo || !mintAccountInfo.value || mintAccountInfo.value.data instanceof Buffer) {
            throw new Error("Could not find mint account");
        }
        const mintInfo = mintAccountInfo.value.data.parsed.info;
        const totalSupply = mintInfo.supply;
        const decimals = mintInfo.decimals;
        const adjustedTotalSupply = totalSupply / Math.pow(10, decimals);
        return adjustedTotalSupply;
    }
    catch (error) {
        console.error(`Error fetching token total supply: ${error}`);
        throw error;
    }
});
const handleSOLScanContract = (bot, chat_id, userAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!userAddress) {
            throw new Error('Invalid contract address.');
        }
        const previousContractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (previousContractAddress && localState[previousContractAddress]) {
            delete localState[previousContractAddress];
        }
        localState[userAddress] = localState[userAddress] || {};
        (0, globalState_1.setCurrentContractAddress)(userAddress);
        const honeypotResult = yield (0, checkSolanaHoneyPot_service_1.checkIfHoneypot)(userAddress);
        const tokenInfo = yield (0, dexscreener_service_1.getTokenInfoFromDexscreener)(userAddress);
        const connection = new web3_js_1.Connection(process.env.SOL_PROVIDER_URL);
        const totalSupply = yield getTokenTotalSupply(connection, userAddress);
        if (totalSupply === null) {
            throw new Error('Failed to fetch total supply.');
        }
        const totalSupplyNumber = totalSupply;
        const marketCap = tokenInfo.priceUsd * totalSupplyNumber;
        // Utility function to format numbers for display
        const formatNumber = (num) => {
            if (num >= **********000) {
                return (num / **********000).toFixed(2) + 'T'; // Trillions
            }
            else if (num >= **********) {
                return (num / **********).toFixed(2) + 'B'; // Billions
            }
            else if (num >= 1000000) {
                return (num / 1000000).toFixed(2) + 'M'; // Millions
            }
            else if (num >= 1000) {
                return (num / 1000).toFixed(2) + 'K'; // Thousands
            }
            else {
                return num.toFixed(2); // Less than a thousand
            }
        };
        const formattedTotalSupply = formatNumber(totalSupplyNumber);
        const formattedMarketCap = formatNumber(marketCap);
        const formattedLiquidity = formatNumber(tokenInfo.liquidity.usd);
        const priceChangeEmoji = tokenInfo.priceChange.m5 > 0 ? '🟢🟢🟢🟢🟢🟢' : '🔴🔴🔴🔴🔴🔴';
        const roundedBuyTax = Math.ceil(parseFloat(honeypotResult.buyTax));
        const roundedSellTax = Math.ceil(parseFloat(honeypotResult.sellTax));
        const resultMessage = `
🪙 ${tokenInfo.symbol} (${tokenInfo.name}) || 📈 ${tokenInfo.priceChange.m5 > 0 ? '+' : ''}${tokenInfo.priceChange.m5}%  || 🏦 ${tokenInfo.dexId} || ${tokenInfo.chainId}

${priceChangeEmoji}  Price Change: ${tokenInfo.priceChange.m5 > 0 ? '+' : ''}${tokenInfo.priceChange.m5}%

CA: ${tokenInfo.address}
LP: ${tokenInfo.pairAddress}

💼 TOTAL SUPPLY: ${formattedTotalSupply} ${tokenInfo.symbol}
🏷️ MC: $${formattedMarketCap}
💧 LIQUIDITY: $${formattedLiquidity}
💵 PRICE: $${tokenInfo.priceUsd}

🔍 Honeypot/Rugpull Risk: ${honeypotResult.risk}
📉 Buy Tax: ${roundedBuyTax}%
📈 Sell Tax: ${roundedSellTax}%

⚠️⚠️⚠️⚠️ ${honeypotResult.riskDescriptions}
      `;
        const user = yield user_models_1.default.findOne({ chat_id }).exec();
        if (user) {
            user.solCurrentTokenName = tokenInfo.name;
            user.solCurrentContractAddress = userAddress;
            yield user.save();
        }
        localState[userAddress] = {
            totalSupply: formattedTotalSupply, // Keep as number
            marketCap: marketCap, // Keep as number
            checkHoneypot: honeypotResult,
            tokenInfo,
            resultMessage
        };
        const previousMessageId = messageIdStore[chat_id];
        if (previousMessageId) {
            yield bot.deleteMessage(chat_id, previousMessageId);
        }
        const sentMessage = yield bot.sendMessage(chat_id, resultMessage, {
            parse_mode: 'Markdown',
            reply_markup: (0, exports.createSolKeyboard)()
        });
        messageIdStore[chat_id] = sentMessage.message_id;
        return sentMessage.message_id;
    }
    catch (error) {
        console.error('Error scanning contract:', error);
        yield bot.sendMessage(chat_id, 'There was an error scanning the contract. Please try again later.');
    }
});
exports.handleSOLScanContract = handleSOLScanContract;
function getSolTokenBalance(connection, walletAddress, tokenMintAddress) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const accounts = yield connection.getParsedTokenAccountsByOwner(new web3_js_1.PublicKey(walletAddress), { mint: new web3_js_1.PublicKey(tokenMintAddress) });
            if (accounts.value.length === 0) {
                throw new Error('Token account not found');
            }
            const tokenAccount = accounts.value[0];
            const tokenAmount = tokenAccount.account.data.parsed.info.tokenAmount.uiAmount;
            console.log('Balance (using Solana-Web3.js): ', tokenAmount);
            return tokenAmount;
        }
        catch (error) {
            console.error('Error fetching token balance:', error);
            throw error;
        }
    });
}
// Function to get the sol balance
const getSolBalance = (address) => __awaiter(void 0, void 0, void 0, function* () {
    const providerUrl = process.env.SOL_PROVIDER_URL;
    if (!providerUrl) {
        throw new Error('Provider is not defined in the .env file');
    }
    const connection = new web3_js_1.Connection(providerUrl); // Use the providerUrl from the environment variable
    try {
        // Fetch the balance
        const publicKey = new web3_js_1.PublicKey(address);
        const balance = yield connection.getBalance(publicKey); // Directly get balance in lamports
        // Convert lamports to SOL
        const balanceSOL = balance / web3_js_1.LAMPORTS_PER_SOL;
        return balanceSOL.toFixed(6);
    }
    catch (error) {
        console.error('Error fetching balance:', error);
        throw new Error('Unable to fetch balance for Solana wallet.');
    }
});
exports.getSolBalance = getSolBalance;
// Create sell options keyboard
const getSolSellOptionsKeyboard = () => __awaiter(void 0, void 0, void 0, function* () {
    return [
        [
            { text: 'Sell All', callback_data: JSON.stringify({ command: 'sell_all_sol' }) },
            { text: 'Sell 25%', callback_data: JSON.stringify({ command: 'sell_25_sol' }) }
        ],
        [
            { text: 'Sell 50%', callback_data: JSON.stringify({ command: 'sell_50_sol' }) },
            { text: 'Sell 75%', callback_data: JSON.stringify({ command: 'sell_75_sol' }) }
        ],
        [
            { text: 'Back', callback_data: JSON.stringify({ command: 'back_sol' }) }
        ]
    ];
});
exports.getSolSellOptionsKeyboard = getSolSellOptionsKeyboard;
// Create trade options keyboard
const getsolTradeOptionsKeyboard = () => __awaiter(void 0, void 0, void 0, function* () {
    return [
        [
            { text: 'Trade Option 1', callback_data: JSON.stringify({ command: 'trade_option_1' }) },
            { text: 'Trade Option 2', callback_data: JSON.stringify({ command: 'trade_option_2' }) }
        ]
    ];
});
exports.getsolTradeOptionsKeyboard = getsolTradeOptionsKeyboard;
const createSolKeyboard = () => {
    return {
        inline_keyboard: [
            [
                { text: '◀️ Previous', callback_data: JSON.stringify({ command: 'previous_sol' }) },
                { text: '▶️ Next', callback_data: JSON.stringify({ command: 'next_sol' }) }
            ],
            [
                { text: '🔄 Refresh', callback_data: JSON.stringify({ command: 'refresh_sol' }) },
                { text: '💸 Sell', callback_data: JSON.stringify({ command: 'sell_sol' }) }
            ],
            [
                { text: '📈 Chart', callback_data: JSON.stringify({ command: 'chart_sol' }) },
                { text: '💸 Spend X sol', callback_data: JSON.stringify({ command: 'spend_sol' }) }
            ],
            [
                {
                    text: "* Dismiss message",
                    callback_data: JSON.stringify({
                        command: "dismiss_message",
                    }),
                },
            ]
            // [
            //   { text: 'Advanced Trade', callback_data: JSON.stringify({ command: 'snipetrade_dashboard_sol' }) }
            // ]
        ]
    };
};
exports.createSolKeyboard = createSolKeyboard;
// Update message with sell options
const updateSolMessageWithSellOptions = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const sellOptions = yield (0, exports.getSolSellOptionsKeyboard)();
        const contractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (!contractAddress) {
            console.error('No contract address found in global state.');
            return;
        }
        const tokenDetails = (0, exports.getTokenDetails)(contractAddress);
        if (!tokenDetails || !tokenDetails.resultMessage) {
            console.error('No result message found for the token address.');
            return;
        }
        yield bot.editMessageText(tokenDetails.resultMessage, {
            chat_id: chatId,
            message_id: messageId,
            parse_mode: 'Markdown',
            reply_markup: {
                inline_keyboard: sellOptions
            }
        });
    }
    catch (error) {
        console.error('Failed to update sell options:', error);
    }
});
exports.updateSolMessageWithSellOptions = updateSolMessageWithSellOptions;
const updateSolMessageWithTradeOptions = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const tradeOptions = yield (0, exports.getsolTradeOptionsKeyboard)();
        const contractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (!contractAddress) {
            console.error('No contract address found in global state.');
            return;
        }
        const tokenDetails = (0, exports.getTokenDetails)(contractAddress);
        if (!tokenDetails || !tokenDetails.resultMessage) {
            console.error('No result message found for the token address.');
            return;
        }
        // Update the message with the new trade options
        yield bot.editMessageText(tokenDetails.resultMessage, {
            chat_id: chatId,
            message_id: messageId,
            parse_mode: 'Markdown',
            reply_markup: {
                inline_keyboard: tradeOptions
            }
        });
    }
    catch (error) {
        console.error('Failed to update trade options:', error);
    }
});
exports.updateSolMessageWithTradeOptions = updateSolMessageWithTradeOptions;
const mainSolMenu = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const solOptions = (0, exports.createSolKeyboard)();
        const contractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (!contractAddress) {
            console.error('No contract address found in global state.');
            return;
        }
        const tokenDetails = (0, exports.getTokenDetails)(contractAddress);
        if (!tokenDetails || !tokenDetails.resultMessage) {
            console.error('No result message found for the token address.');
            return;
        }
        yield bot.editMessageText(tokenDetails.resultMessage, {
            chat_id: chatId,
            message_id: messageId,
            parse_mode: 'Markdown',
            reply_markup: {
                inline_keyboard: solOptions.inline_keyboard
            }
        });
    }
    catch (error) {
        console.error('Failed to update sol menu:', error);
        yield bot.sendMessage(chatId, 'Failed to update the menu. Please try again later.');
    }
});
exports.mainSolMenu = mainSolMenu;
const handleGenerateNewSolWallet = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId });
        if (!user) {
            yield bot.sendMessage(chatId, 'User not found.');
            return;
        }
        const newWallet = (0, wallet_service_1.generateSOLWallet)();
        yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { 'sol_wallet.address': newWallet.address, 'sol_wallet.private_key': newWallet.privateKey } });
        // Send confirmation message
        yield bot.sendMessage(chatId, `✅ You clicked "Generate New Wallet" in the sol blockchain.\nNew wallet address: \`\`\`${newWallet.address}\`\`\`\nNew wallet Private Key: \`\`\`${newWallet.privateKey}\`\`\``);
        yield (0, sol_dashboard_1.handleSolDashboard)(bot, chatId);
    }
    catch (error) {
        console.error('Failed to generate new wallet:', error);
        yield bot.sendMessage(chatId, 'There was an error generating a new wallet. Please try again later.');
    }
});
exports.handleGenerateNewSolWallet = handleGenerateNewSolWallet;
// Message ID storage
let promptMessageIdStore = {};
const askForSolContractAddress = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Save previous message IDs if any
        if (promptMessageIdStore[chatId]) {
            const ids = promptMessageIdStore[chatId];
            if (ids.promptId) {
                try {
                    yield bot.deleteMessage(chatId, ids.promptId);
                }
                catch (error) {
                    console.error('Failed to delete previous prompt message:', error);
                }
            }
            if (ids.replyId) {
                try {
                    yield bot.deleteMessage(chatId, ids.replyId);
                }
                catch (error) {
                    console.error('Failed to delete previous reply message:', error);
                }
            }
        }
        // Send message asking for contract address
        const sentMessage = yield bot.sendMessage(chatId, botConvo_1.INPUT_SOL_CONTRACT_ADDRESS, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        // Update the message ID store
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log('Received contract address:', msg.text); // Log the received text
                    // Save the reply message ID
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error('Invalid contract address.'));
                }
            }));
        });
    }
    catch (error) {
        console.error('Failed to ask for contract address:', error);
        throw error;
    }
});
exports.askForSolContractAddress = askForSolContractAddress;
const askForSolToken = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Save previous message IDs if any
        if (promptMessageIdStore[chatId]) {
            const ids = promptMessageIdStore[chatId];
            if (ids.promptId) {
                try {
                    yield bot.deleteMessage(chatId, ids.promptId);
                }
                catch (error) {
                    console.error('Failed to delete previous prompt message:', error);
                }
            }
            if (ids.replyId) {
                try {
                    yield bot.deleteMessage(chatId, ids.replyId);
                }
                catch (error) {
                    console.error('Failed to delete previous reply message:', error);
                }
            }
        }
        // Send message asking for BSC token
        const sentMessage = yield bot.sendMessage(chatId, botConvo_1.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        // Update the message ID store
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log('Received Sol token:', msg.text); // Log the received text
                    // Save the reply message ID
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error('Invalid BSC token.'));
                }
            }));
        });
    }
    catch (error) {
        console.error('Failed to ask for BSC token:', error);
        throw error;
    }
});
exports.askForSolToken = askForSolToken;
let tradesState = []; // Store the fetched trades
let currentIndex = 0; // Track the current trade index
function fetchAndDisplayActiveSolTrades(bot, chatId, blockchain) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Find active trades for the specific user based on chatId and blockchain
            const tradesState = yield trade_model_1.default.find({ chatId, isSold: false, blockchain }).exec();
            let currentIndex = 0;
            if (tradesState.length === 0) {
                yield bot.sendMessage(chatId, 'No active trades found!');
                return;
            }
            // Display the current trade information
            yield displayCurrentSolTrade(bot, chatId, tradesState, currentIndex);
        }
        catch (error) {
            console.error('Error fetching trades:', error);
            yield bot.sendMessage(chatId, 'An error occurred while fetching trades.');
        }
    });
}
function displayCurrentSolTrade(bot, chatId, tradesState, index, messageId) {
    return __awaiter(this, void 0, void 0, function* () {
        const trade = tradesState[index];
        const message = `
<b>Contract:</b> ${trade.contractName} (${trade.contractAddress})
<b>Wallet:</b> ${trade.walletId}
<b>Buy Amount:</b> ${trade.buyAmount}  <b>Sell Amount:</b> ${trade.sellAmount}
<b>Sold:</b> ${trade.isSold ? 'Yes' : 'No'}
  `;
        // Update user details
        yield user_models_1.default.findOneAndUpdate({ chat_id: chatId }, {
            solCurrentContractAddress: trade.contractAddress,
            solCurrentTokenName: trade.contractName,
        });
        // Update global state
        (0, globalState_1.setCurrentContractAddress)(trade.contractAddress);
        const options = {
            parse_mode: "HTML",
            disable_web_page_preview: true,
            reply_markup: {
                inline_keyboard: [
                    [
                        {
                            text: "Sell 25%",
                            callback_data: JSON.stringify({ command: "sell_25_sol" }),
                        },
                        {
                            text: "Sell 50%",
                            callback_data: JSON.stringify({ command: "sell_50_sol" }),
                        },
                    ],
                    [
                        {
                            text: "Sell 75%",
                            callback_data: JSON.stringify({ command: "sell_75_sol" }),
                        },
                        {
                            text: "Sell All",
                            callback_data: JSON.stringify({ command: "sell_all_sol" }),
                        },
                    ],
                    [
                        {
                            text: "Previous",
                            callback_data: JSON.stringify({ command: "previous_sol" }),
                        },
                        {
                            text: "Next",
                            callback_data: JSON.stringify({ command: "next_sol" }),
                        },
                    ],
                    [
                        {
                            text: "* Dismiss message",
                            callback_data: JSON.stringify({
                                command: "dismiss_message",
                            }),
                        },
                    ],
                ],
            },
        };
        if (messageId) {
            yield bot.editMessageText(message, Object.assign({ chat_id: chatId, message_id: messageId }, options));
        }
        else {
            yield bot.sendMessage(chatId, message, options);
        }
    });
}
const handleSolRefresh = (bot, chatId, callbackQuery) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const currentContractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (!currentContractAddress) {
            throw new Error('No contract address is currently set.');
        }
        yield (0, exports.handleSOLScanContract)(bot, chatId, currentContractAddress);
        // If a callbackQuery is provided, show an alert
        if (callbackQuery) {
            const callbackQueryId = callbackQuery.id;
            yield bot.answerCallbackQuery(callbackQueryId, {
                text: "Token Refresh Successfully!",
                show_alert: true
            });
        }
    }
    catch (error) {
        console.error('Failed to refresh contract details:', error);
        yield bot.sendMessage(chatId, 'Failed to refresh contract details. Please try again later.');
    }
});
exports.handleSolRefresh = handleSolRefresh;
const handleSolChart = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const currentContractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (currentContractAddress) {
            const dexscreenerUrl = `https://dexscreener.com/solana/${currentContractAddress}`;
            const chartMessage = yield bot.sendMessage(chatId, `<a href="${dexscreenerUrl}">Click here to view the chart on Dexscreener</a>`, { parse_mode: 'HTML' });
            promptMessageIdStore[chatId] = { promptId: chartMessage.message_id };
        }
        else {
            const errorMessage = yield bot.sendMessage(chatId, 'Error: Current contract address is not set.');
            promptMessageIdStore[chatId] = { promptId: errorMessage.message_id };
        }
    }
    catch (error) {
        console.error('Failed to handle chart action:', error);
    }
});
exports.handleSolChart = handleSolChart;
const handleSpendsol = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const sentMessage = yield bot.sendMessage(chatId, botConvo_2.SET_SOL_SPEND, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log('Received buy amount:', msg.text);
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error('Invalid buy amount.'));
                }
            }));
        });
    }
    catch (error) {
        console.error('Error handling spend sol:', error);
        throw new Error('Failed to handle spend sol action.');
    }
});
exports.handleSpendsol = handleSpendsol;
// export async function getsolTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
//   const tokenContract = new solers.Contract(
//       tokenAddress,
//       ['function balanceOf(address) view returns (uint)', 'function decimals() view returns (uint)'],
//       provider
//   );
//   const balance = await tokenContract.balanceOf(userAddress);
//   const tokenDecimals = await tokenContract.decimals();
//   const balanceWithDecimals = solers.utils.formatUnits(balance, tokenDecimals);
//   console.log(balanceWithDecimals);
//   return balanceWithDecimals;
// }
const handleSolSellAll = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }
        const { address: userAddress } = user.sol_wallet;
        const tokenAddress = user.solCurrentContractAddress || '';
        if (!tokenAddress) {
            throw new Error('No contract address found for user');
        }
        const connection = new web3_js_1.Connection(process.env.SOL_PROVIDER_URL);
        const tokenBalance = yield getSolTokenBalance(connection, userAddress, tokenAddress);
        if (isNaN(tokenBalance) || tokenBalance <= 0) {
            throw new Error('Invalid token balance');
        }
        const receipt = yield (0, solsell_trader_1.sellSolToken)(chatId, tokenBalance);
        if (receipt) {
            yield bot.sendMessage(chatId, `✅ Successfully sold all your tokens:\n\n` +
                `🔗 Transaction Hash: ${receipt}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${receipt}\nTo see your transaction`);
        }
        else {
            yield bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
        }
    }
    catch (error) {
        console.error('Error selling all tokens:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        yield bot.sendMessage(chatId, `An error occurred while selling all tokens: ${errorMessage}`);
    }
});
exports.handleSolSellAll = handleSolSellAll;
const sellSolTokenPercentage = (bot, chatId, percentage) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }
        const { address: userAddress } = user.sol_wallet;
        const tokenAddress = user.solCurrentContractAddress;
        if (!tokenAddress) {
            throw new Error('No contract address found for user');
        }
        const connection = new web3_js_1.Connection(process.env.SOL_PROVIDER_URL);
        const tokenBalance = yield getSolTokenBalance(connection, userAddress, tokenAddress);
        if (isNaN(tokenBalance) || tokenBalance <= 0) {
            throw new Error('Insufficient token balance');
        }
        const amountToSell = tokenBalance * (percentage / 100);
        if (amountToSell <= 0) {
            throw new Error('Invalid percentage value');
        }
        console.log('Amount to sell:', amountToSell);
        const receipt = yield (0, solsell_trader_1.sellSolToken)(chatId, amountToSell);
        if (receipt) {
            yield bot.sendMessage(chatId, `✅ Successfully sold ${percentage}% of your tokens:\n\n` +
                `🔗 Transaction Hash: ${receipt}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${receipt}\nTo see your transaction`);
        }
        else {
            yield bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
        }
    }
    catch (error) {
        console.error('Error selling tokens:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        yield bot.sendMessage(chatId, `An error occurred while selling tokens: ${errorMessage}`);
    }
});
exports.sellSolTokenPercentage = sellSolTokenPercentage;
// Retrieve token details by contract address
const getTokenDetails = (contractAddress) => {
    return localState[contractAddress];
};
exports.getTokenDetails = getTokenDetails;
