"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buyEthTokens = buyEthTokens;
exports.sellEthTokens = sellEthTokens;
const ethers_1 = require("ethers");
const axios_1 = __importDefault(require("axios"));
const user_models_1 = __importDefault(require("../models/user.models"));
const trade_model_1 = __importDefault(require("../models/trade.model"));
const trade_model_2 = require("../models/trade.model");
// Define the provider URL and admin address
const adminAddress = process.env.EVM_ADMIN_ADDRESS || '';
const providerUrl = process.env.ETH_PROVIDER_URL;
if (!adminAddress) {
    throw new Error('ADMIN_ADDRESS environment variable is not set');
}
if (!providerUrl) {
    throw new Error('Provider environment variable is not set');
}
// Replace with your 0x API key
const apiKey = 'a3e4332a-4ed8-4059-90fe-8bec73496649';
// Define the headers
const headers = {
    '0x-api-key': apiKey,
};
function getSwapQuote(url) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log('Requesting swap quote...');
            const response = yield axios_1.default.get(url, { headers });
            console.log('Swap Quote Response:', response.data);
            return response.data;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                // Handle Axios-specific errors
                console.error('Axios Error:', error.response ? error.response.data : error.message);
            }
            else {
                // Handle other types of errors
                console.error('Error:', error.message);
            }
            throw error; // Re-throw the error to handle it in the caller function
        }
    });
}
// ABI fragment for the `decimals` method
const abi = [
    "function decimals() view returns (uint8)"
];
// Function to get the token decimals dynamically
function getTokenDecimals(tokenAddress, provider) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const tokenContract = new ethers_1.ethers.Contract(tokenAddress, abi, provider);
            const decimals = yield tokenContract.decimals();
            return decimals;
        }
        catch (error) {
            console.error('Error fetching token decimals:', error);
            throw error;
        }
    });
}
function buyEthTokens(chatId, value) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Fetch user settings from the dataeth
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user) {
                throw new Error('User not found');
            }
            const currentBlockchain = user.currentBlockchain;
            if (currentBlockchain !== 'eth') {
                throw new Error(`Current blockchain is ${currentBlockchain}, not eth.`);
            }
            const ethConfig = user.eth_config;
            if (!ethConfig) {
                throw new Error('eth configuration is not set for the user');
            }
            const { private_key, address } = user.eth_wallet;
            const ethCurrentContractAddress = user.ethCurrentContractAddress;
            const ethCurrentTokenName = user.ethCurrentTokenName;
            if (!ethCurrentContractAddress) {
                throw new Error('Current contract address for eth is not set');
            }
            // Define buyToken ethd on the user's current contract address
            const buyToken = ethCurrentContractAddress;
            // Initialize provider and wallet
            const provider = new ethers_1.providers.JsonRpcProvider(providerUrl);
            const wallet = new ethers_1.Wallet(private_key, provider);
            // Get token decimals
            const decimals = yield getTokenDecimals(buyToken, provider);
            console.log(`Token Decimals: ${decimals}`);
            // Convert sellAmount to the smallest unit
            const sellAmountWei = ethers_1.ethers.utils.parseUnits(value, decimals);
            console.log(`Sell Amount (in smallest unit): ${sellAmountWei.toString()}`);
            // Define sellToken as the native token (BNB for eth)
            const sellToken = '******************************************';
            // Define feeRecipient and buyTokenPercentageFee
            const feeRecipient = adminAddress;
            const buyTokenPercentageFee = 0.08;
            // Get slippage
            const slippagePercentage = 0.1; // Assuming slippage base is stored in base_config
            // Build the query parameters
            const queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${sellAmountWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;
            // Define the endpoint URL with query parameters
            const url = `https://api.0x.org/swap/v1/quote?${queryParams}`;
            // Get the swap quote
            const quote = yield getSwapQuote(url);
            // Prepare the transaction to buy tokens
            const buyTx = {
                to: quote.to,
                data: quote.data,
                value: ethers_1.ethers.BigNumber.from(quote.value),
                gasLimit: ethers_1.ethers.BigNumber.from(500000), // Adjusted gas limit to a higher value
                gasPrice: ethers_1.ethers.utils.parseUnits('5', 'gwei'), // Adjusted gas price to a higher value
            };
            console.log('Signing and sending the buy transaction...');
            const buyTxResponse = yield wallet.sendTransaction(buyTx);
            console.log(`Buy Transaction Hash: ${buyTxResponse.hash}`);
            // Wait for the buy transaction to be mined
            const receipt = yield buyTxResponse.wait();
            console.log(`Buy Transaction Mined! Block Number: ${receipt.blockNumber}`);
            // Check if there is an existing trade for this contract address
            const existingTrade = yield trade_model_1.default.findOne({
                userId: user._id,
                contractAddress: ethCurrentContractAddress,
                isSold: false,
            }).exec();
            if (existingTrade) {
                existingTrade.buyAmount += parseFloat(value);
                yield existingTrade.save();
                console.log('Updated existing trade with new buy amount');
            }
            else {
                const newTrade = new trade_model_1.default({
                    userId: user._id,
                    walletId: user.eth_wallet.address,
                    contractAddress: ethCurrentContractAddress,
                    contractName: ethCurrentTokenName,
                    buyAmount: parseFloat(value),
                    sellAmount: 0,
                    takeProfit: 0,
                    stopLoss: 0,
                    isSold: false,
                    blockchain: 'eth'
                });
                yield newTrade.save();
                console.log('Created new trade entry');
            }
            // Update the user's document with the new trade entry
            yield user_models_1.default.updateOne({ _id: user._id }, { $push: { [`trades.${currentBlockchain}`]: existingTrade ? existingTrade._id : user._id } });
            console.log('User trades updated successfully');
            return receipt; // Return the receipt object after the transaction is successful
        }
        catch (error) {
            console.error('Error purchasing tokens:', error);
            throw error; // Re-throw the error to handle it in the bot action
        }
    });
}
const erc20Abi = [
    "function decimals() view returns (uint8)",
    "function allowance(address owner, address spender) view returns (uint256)",
    "function approve(address spender, uint256 amount) returns (bool)"
];
// INFINITE APPROVAL
//   async function checkAndApproveAllowance(tokenContract: ethers.Contract, ownerAddress: string, spenderAddress: string, amountWei: ethers.BigNumber) {
//     const allowance = await tokenContract.allowance(ownerAddress, spenderAddress);
//     if (allowance.lt(amountWei)) {
//       console.log('Approving token allowance...');
//       const approveTx = await tokenContract.approve(spenderAddress, ethers.constants.MaxUint256);
//       await approveTx.wait();
//       console.log('Allowance approved');
//     } else {
//       console.log('Sufficient allowance already exists');
//     }
//   }
// AMOUNT BASED APPROVAL
function checkAndApproveAllowance(tokenContract, ownerAddress, spenderAddress, amountWei) {
    return __awaiter(this, void 0, void 0, function* () {
        const allowance = yield tokenContract.allowance(ownerAddress, spenderAddress);
        if (allowance.lt(amountWei)) {
            console.log('Approving token allowance...');
            const approveTx = yield tokenContract.approve(spenderAddress, amountWei);
            yield approveTx.wait();
            console.log('Allowance approved');
        }
        else {
            console.log('Sufficient allowance already exists');
        }
    });
}
function sellEthTokens(chatId, value) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user)
                throw new Error('User not found');
            const currentBlockchain = user.currentBlockchain;
            if (currentBlockchain !== 'eth')
                throw new Error(`Current blockchain is ${currentBlockchain}, not eth.`);
            const ethConfig = user.eth_config;
            if (!ethConfig)
                throw new Error('eth configuration is not set for the user');
            const { private_key, address } = user.eth_wallet;
            const ethCurrentContractAddress = user.ethCurrentContractAddress;
            if (!ethCurrentContractAddress)
                throw new Error('Current contract address for eth is not set');
            const sellToken = ethCurrentContractAddress;
            const provider = new ethers_1.ethers.providers.JsonRpcProvider(providerUrl);
            const wallet = new ethers_1.ethers.Wallet(private_key, provider);
            const decimals = yield getTokenDecimals(sellToken, provider);
            const sellAmountWei = ethers_1.ethers.utils.parseUnits(value, decimals);
            const tokenContract = new ethers_1.ethers.Contract(sellToken, erc20Abi, wallet);
            const spenderAddress = "******************************************"; // 0x protocol spender address
            yield checkAndApproveAllowance(tokenContract, address, spenderAddress, sellAmountWei);
            // Define feeRecipient and buyTokenPercentageFee
            const feeRecipient = adminAddress;
            const buyTokenPercentageFee = 0.08;
            // Get slippage
            const slippagePercentage = 0.1; // Assuming slippage base is stored in base_config
            // Build the query parameters
            const queryParams = `buyToken=******************************************&sellToken=${sellToken}&sellAmount=${sellAmountWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;
            // Define the endpoint URL with query parameters
            const url = `https://api.0x.org/swap/v1/quote?${queryParams}`;
            console.log('Requesting swap quote...');
            const response = yield axios_1.default.get(url, { headers });
            console.log('Swap Quote Response:', response.data);
            const { to: routerAddress, data: encodedData, value: quoteValue, gas, gasPrice } = response.data;
            const tx = {
                to: routerAddress,
                data: encodedData,
                value: quoteValue ? ethers_1.ethers.BigNumber.from(quoteValue) : ethers_1.ethers.BigNumber.from(0),
                gasLimit: ethers_1.ethers.BigNumber.from(500000), // Adjusted gas limit to a higher value
                gasPrice: ethers_1.ethers.utils.parseUnits('5', 'gwei'), // Adjusted gas price to a higher value
            };
            console.log('Signing and sending the transaction...');
            const txResponse = yield wallet.sendTransaction(tx);
            console.log(`Transaction Hash: ${txResponse.hash}`);
            const receipt = yield txResponse.wait();
            console.log(`Transaction Mined! Block Number: ${receipt.blockNumber}`);
            yield (0, trade_model_2.updateTradeAsSold)(user._id, ethCurrentContractAddress, parseFloat(value), 'eth');
            return receipt;
        }
        catch (error) {
            console.error('Error selling tokens:', error);
            throw error;
        }
    });
}
