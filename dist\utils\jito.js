"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onBundleResult = void 0;
exports.bundle = bundle;
exports.bull_dozer = bull_dozer;
const bs58_1 = __importDefault(require("bs58"));
const web3_js_1 = require("@solana/web3.js");
const searcher_1 = require("jito-ts/dist/sdk/block-engine/searcher");
const types_1 = require("jito-ts/dist/sdk/block-engine/types");
const utils_1 = require("jito-ts/dist/sdk/block-engine/utils");
const JITO_AUTH_KEYPAIR = process.env.JITO_AUTH_KEYPAIR || '';
const BLOCKENGINE_URL = process.env.BLOCKENGINE_URL || '';
const SOL_PROVIDER_URL = process.env.SOL_PROVIDER_URL || '';
const JITO_FEE = process.env.JITO_FEE || '';
const connection = new web3_js_1.Connection(SOL_PROVIDER_URL);
function bundle(txs, keypair) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const txNum = Math.ceil(txs.length / 3);
            let successNum = 0;
            for (let i = 0; i < txNum; i++) {
                const upperIndex = (i + 1) * 3;
                const downIndex = i * 3;
                const newTxs = [];
                for (let j = downIndex; j < upperIndex; j++) {
                    if (txs[j])
                        newTxs.push(txs[j]);
                }
                // console.log(`------------- Bundle & Send: ${i + 1} ---------`)
                // let tryNum = 0
                let success = yield bull_dozer(newTxs, keypair);
                console.log('success', success);
                return success > 0 ? true : false;
            }
            if (successNum == txNum)
                return true;
            else
                return false;
        }
        catch (error) {
            return false;
        }
    });
}
function bull_dozer(txs, keypair) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log('bull_dozer');
            const bundleTransactionLimit = parseInt('4');
            const jito_auth_keypair_array = JITO_AUTH_KEYPAIR.split(',');
            const keyapair_num = Math.floor(Math.random() * jito_auth_keypair_array.length);
            const jito_auth_keypair = jito_auth_keypair_array[keyapair_num];
            const jitoKey = web3_js_1.Keypair.fromSecretKey(bs58_1.default.decode(jito_auth_keypair));
            console.log('jitoKey', jitoKey);
            const blochengine_url_array = BLOCKENGINE_URL.split(',');
            const blockengine_num = Math.floor(Math.random() * blochengine_url_array.length);
            const blochengine_url = blochengine_url_array[blockengine_num];
            console.log('blochengine_url', blochengine_url);
            const search = (0, searcher_1.searcherClient)(blochengine_url, jitoKey);
            yield build_bundle(search, bundleTransactionLimit, txs, keypair);
            const bundle_result = yield (0, exports.onBundleResult)(search);
            return bundle_result;
        }
        catch (error) {
            return 0;
        }
    });
}
function build_bundle(search, bundleTransactionLimit, txs, keypair) {
    return __awaiter(this, void 0, void 0, function* () {
        const accounts = yield search.getTipAccounts();
        console.log("tip account:", accounts);
        const _tipAccount = accounts[Math.min(Math.floor(Math.random() * accounts.length), 3)];
        const tipAccount = new web3_js_1.PublicKey(_tipAccount);
        const bund = new types_1.Bundle([], bundleTransactionLimit);
        const resp = yield connection.getLatestBlockhash("processed");
        bund.addTransactions(...txs);
        let maybeBundle = bund.addTipTx(keypair, Number(JITO_FEE), tipAccount, resp.blockhash);
        if ((0, utils_1.isError)(maybeBundle)) {
            throw maybeBundle;
        }
        try {
            yield search.sendBundle(maybeBundle);
            // logger.info("Bundling done")
        }
        catch (e) {
            console.log(e);
        }
        return maybeBundle;
    });
}
const onBundleResult = (c) => {
    let first = 0;
    let isResolved = false;
    return new Promise((resolve) => {
        // Set a timeout to reject the promise if no bundle is accepted within 5 seconds
        setTimeout(() => {
            resolve(first);
            isResolved = true;
        }, 30000);
        c.onBundleResult((result) => {
            if (isResolved)
                return first;
            // clearTimeout(timeout) // Clear the timeout if a bundle is accepted
            const bundleId = result.bundleId;
            const isAccepted = result.accepted;
            const isRejected = result.rejected;
            if (isResolved == false) {
                if (isAccepted) {
                    console.log("bundle accepted, ID:", result.bundleId, " Slot: ", result.accepted.slot);
                    first += 1;
                    isResolved = true;
                    // Resolve with 'first' when a bundle is accepted
                    resolve(first);
                }
                if (isRejected) {
                    // Do not resolve or reject the promise here
                }
            }
        }, (e) => {
            console.log(e);
            // Do not reject the promise here
        });
    });
};
exports.onBundleResult = onBundleResult;
