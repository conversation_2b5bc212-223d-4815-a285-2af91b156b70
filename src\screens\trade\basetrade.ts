/**
 * BASE Trade Screen
 *
 * This module provides the UI and functionality for trading on the Base network.
 * It uses the new trading interface for executing trades.
 */

import TelegramBot, { InlineKeyboardMarkup } from 'node-telegram-bot-api';
import { ethers } from 'ethers';
import mongoose from 'mongoose';
import cron from 'node-cron';
import AutoTradeModel, { Trade } from '../../models/autotrade.model';
import CronJob from '../../models/cronjob.model';
import { getTokenInfoFromDexscreener } from '../../services/dexscreener.service';
import { BlockchainType, TraderFactory } from '../../services/trading/TradingInterface';
import { logger } from '../../services/logger/LoggerService';
import { updateAutoTradeAsSold } from '../../models/autotrade.model';
import { updateTradeAsSold } from '../../models/trade.model';
import User from '../../models/user.models';

// Define the provider URL
const providerUrl = process.env.BASE_PROVIDER_URL;

if (!providerUrl) {
    throw new Error('BASE_PROVIDER_URL environment variable is not set');
}


const promptMessageIdStore: { [key: number]: { promptId?: number, replyId?: number, successId?: number } } = {};

export const askForBaseTradeInput = async (bot: TelegramBot, chatId: number, promptMessage: string, field: string): Promise<number> => {
  try {
    // Clear previous messages if any
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        await bot.deleteMessage(chatId, ids.promptId).catch(console.error);
      }
      if (ids.replyId) {
        await bot.deleteMessage(chatId, ids.replyId).catch(console.error);
      }
      if (ids.successId) {
        await bot.deleteMessage(chatId, ids.successId).catch(console.error);
      }
    }

    // Send prompt message
    const sentPromptMessage = await bot.sendMessage(chatId, promptMessage, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      }
    });

    promptMessageIdStore[chatId] = { promptId: sentPromptMessage.message_id };

    // Wait for user response
    return new Promise<number>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentPromptMessage.message_id, async (msg) => {
        if (msg.text) {
          const value = parseFloat(msg.text);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };

          // Send success message
          const successMessage = `✅ ${field.charAt(0).toUpperCase() + field.slice(1)} successfully updated.`;
          const sentSuccessMessage = await bot.sendMessage(chatId, successMessage);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], successId: sentSuccessMessage.message_id };

          resolve(value);
        } else {
          reject(new Error(`Invalid ${field}.`));
        }
      });
    });
  } catch (error) {
    console.error(`Failed to ask for ${field}:`, error);
    throw error;
  }
};



export const createBaseTradeKeyboard = (): InlineKeyboardMarkup => ({
  inline_keyboard: [
    [{ text: '🛒 Execute Trade', callback_data: JSON.stringify({ command: 'snipetrade_buy_base' }) }],
    [
      { text: '📈 Take Profit', callback_data: JSON.stringify({ command: 'snipe_set_take_profit_base' }) },
      { text: '📉 Stop Loss', callback_data: JSON.stringify({ command: 'snipe_set_stop_loss_base' }) },
      { text: '📉 Amount', callback_data: JSON.stringify({ command: 'snipe_set_spend_amount_base' }) },
    ],
    [
      { text: '🔄 Refresh Token', callback_data: JSON.stringify({ command: 'snipe_refresh_token_base' }) },
      { text: '🔍 Review Trade', callback_data: JSON.stringify({ command: 'snipe_review_trade_base' }) },
    ],
    [{ text: '⬅️ Back', callback_data: JSON.stringify({ command: 'back_to_main_trade' }) }]
  ]
});

export const updateBaseTradeConfig = async (chatId: number, contractAddress: string, field: keyof Trade['config'], value: number) => {
  try {
    const trade = await AutoTradeModel.findOne({ chatId, contractAddress, blockchain: 'base' });
    if (trade) {
      trade.config[field] = value;
      await trade.save();
      console.log(`Updated ${String(field)} for trade:`, trade);
    }
  } catch (error) {
    console.error(`Error updating trade ${String(field)}:`, error);
  }
};


/**
 * Execute a BASE trade
 * @param bot Telegram bot instance
 * @param chatId Chat ID
 * @param contractAddress Contract address
 */
export const executeBaseTrade = async (bot: TelegramBot, chatId: number, contractAddress: string) => {
  try {
    // Find the trade configuration
    const trade = await AutoTradeModel.findOne({ chatId, contractAddress, blockchain: 'base' });
    if (!trade) {
      await bot.sendMessage(chatId, '❌ Trade configuration not found.');
      return;
    }

    // Check if all configuration parameters are set
    if (trade.config.spendAmount <= 0) {
      await bot.sendMessage(chatId, '⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set.');
      return;
    }

    // Get the BASE trader
    const trader = TraderFactory.getTrader(BlockchainType.BASE);

    // Execute the trade
    const spendAmount = trade.config.spendAmount.toString();
    const result = await trader.buyTokens(
      contractAddress,
      spendAmount,
      '', // Wallet address will be determined by the trader
      {
        slippage: 5, // Default slippage
        gasLimit: 1000000 // Default gas limit
      }
    );

    // Check if the trade was successful
    if (!result.success) {
      logger.error('Failed to purchase tokens', {
        chatId,
        contractAddress,
        error: result.error
      });
      await bot.sendMessage(chatId, `❌ Failed to purchase tokens: ${result.error || 'Unknown error'}`);
      return;
    }

    // Update the trade status to indicate it's no longer pending
    await AutoTradeModel.findOneAndUpdate(
      { chatId, contractAddress, blockchain: 'base' },
      { isSold: false, isInitialized: 'ongoing' } // Set to 'ongoing'
    ).exec();

    // Send success messages
    await bot.sendMessage(chatId, `✅ Successfully purchased tokens. Transaction Hash: ${result.transactionHash}`);

    // Start monitoring the price and sell if conditions are met
    await monitorBasePriceAndSell(bot, chatId, trade);

    await bot.sendMessage(chatId, '✅ Trade executed successfully and monitoring started.');

    logger.info('BASE trade executed successfully', {
      chatId,
      contractAddress,
      transactionHash: result.transactionHash
    });
  } catch (error) {
    logger.error('Error executing BASE trade', {
      chatId,
      contractAddress,
      error: (error as Error).message
    });
    await bot.sendMessage(chatId, `⚠️ An error occurred while executing the trade: ${(error as Error).message}`);
  }
};


const activeCronJobs: Record<string, cron.ScheduledTask> = {}; // Store active cron jobs in memory


const activeIntervalIds: Record<string, NodeJS.Timeout> = {}; // Store active interval IDs in memory


/**
 * Monitor BASE price and sell when conditions are met
 * @param bot Telegram bot instance
 * @param chatId Chat ID
 * @param trade Trade to monitor
 */
export const monitorBasePriceAndSell = async (bot: TelegramBot | null, chatId: number, trade: Trade): Promise<void> => {
    // Define job key for tracking
    const jobKey = `${trade.chatId}_${trade.contractAddress}_${trade.blockchain}`;

    logger.info('Starting BASE price monitoring', {
        chatId,
        contractAddress: trade.contractAddress,
        takeProfit: trade.config.takeProfit,
        stopLoss: trade.config.stopLoss
    });

    // Set up the interval for checking
    const intervalId = setInterval(async () => {
        try {
            // Fetch token info
            const tokenInfo = await getTokenInfoFromDexscreener(trade.contractAddress, BlockchainType.BASE);
            if (!tokenInfo || tokenInfo.priceChange === undefined) {
                logger.warn('Token info or price change not found', {
                    contractAddress: trade.contractAddress
                });
                return;
            }

            // Convert priceChange to number for comparison
            const priceChange = parseFloat(tokenInfo.priceChange.h24?.toString() || '0');
            if (isNaN(priceChange)) {
                logger.warn('Invalid priceChange value', {
                    contractAddress: trade.contractAddress,
                    priceChange: tokenInfo.priceChange
                });
                return;
            }

            logger.debug(`Price Change: ${priceChange}%`, {
                contractAddress: trade.contractAddress,
                priceChange
            });

            // Check if priceChange meets the conditions
            if (priceChange >= trade.config.takeProfit || priceChange <= trade.config.stopLoss) {
                logger.info('Conditions met for selling tokens', {
                    contractAddress: trade.contractAddress,
                    priceChange,
                    takeProfit: trade.config.takeProfit,
                    stopLoss: trade.config.stopLoss
                });

                // Clear the interval to stop further checks
                clearInterval(intervalId);
                delete activeIntervalIds[jobKey];

                // Fetch user details
                const user = await User.findOne({ chat_id: chatId }).exec();
                if (!user) {
                    throw new Error('User not found');
                }

                const { address: userAddress } = user.base_wallet;

                // Get the BASE trader
                const trader = TraderFactory.getTrader(BlockchainType.BASE);

                // Get token balance
                const tokenBalance = await trader.getTokenBalance(trade.contractAddress, userAddress);
                const tokenBalanceNumber = parseFloat(tokenBalance);

                if (isNaN(tokenBalanceNumber) || tokenBalanceNumber <= 0) {
                    logger.error('Invalid or zero token balance', {
                        contractAddress: trade.contractAddress,
                        userAddress,
                        tokenBalance
                    });
                    return;
                }

                logger.info(`Token Balance: ${tokenBalanceNumber}`, {
                    contractAddress: trade.contractAddress,
                    userAddress
                });

                let sellSuccess = false;
                let attempts = 0;
                let transactionHash = '';

                // Attempt to sell the tokens
                while (attempts < 2 && !sellSuccess) {
                    try {
                        attempts++;
                        logger.info(`Selling tokens, attempt ${attempts}`, {
                            contractAddress: trade.contractAddress,
                            tokenBalance: tokenBalanceNumber
                        });

                        // Sell all available tokens
                        const result = await trader.sellTokens(
                            trade.contractAddress,
                            parseFloat(tokenBalance),
                            userAddress,
                            {
                                slippage: 5, // Default slippage
                                gasLimit: 1000000 // Default gas limit
                            }
                        );

                        if (result.success) {
                            sellSuccess = true;
                            transactionHash = result.transactionHash || '';

                            // Update the trade as sold in the database
                            await updateAutoTradeAsSold(user._id, trade.contractAddress, tokenBalanceNumber, 'base');
                            await updateTradeAsSold(user._id, trade.contractAddress, tokenBalanceNumber, 'base');

                            logger.info('Trade sold successfully', {
                                contractAddress: trade.contractAddress,
                                transactionHash
                            });

                            if (bot) {
                                await bot.sendMessage(
                                    trade.chatId,
                                    `✅ Trade Auto-sold successfully!\n\n` +
                                    `🔗 Transaction Hash: ${transactionHash}\n` +
                                    `💰 Amount Sold: ${tokenBalanceNumber} tokens\n` +
                                    `📊 Price Change: ${priceChange}%`
                                );
                            }
                            return;
                        } else {
                            logger.error('Failed to sell tokens', {
                                contractAddress: trade.contractAddress,
                                error: result.error
                            });
                        }
                    } catch (sellError) {
                        logger.error(`Attempt ${attempts} to sell tokens failed`, {
                            contractAddress: trade.contractAddress,
                            error: (sellError as Error).message
                        });
                    }
                }

                if (!sellSuccess) {
                    logger.error('Failed to sell tokens after 2 attempts', {
                        contractAddress: trade.contractAddress
                    });

                    if (bot) {
                        await bot.sendMessage(
                            trade.chatId,
                            `⚠️ Failed to auto-sell tokens after ${attempts} attempts.\n` +
                            `Please try to sell manually.`
                        );
                    }
                }

                return;
            } else {
                logger.debug('Conditions not met, continuing monitoring', {
                    contractAddress: trade.contractAddress,
                    priceChange,
                    takeProfit: trade.config.takeProfit,
                    stopLoss: trade.config.stopLoss
                });
            }
        } catch (error) {
            logger.error('Error in monitorBasePriceAndSell', {
                contractAddress: trade.contractAddress,
                error: (error as Error).message
            });
        }
    }, 15000); // Check every 15 seconds

    // Store the interval ID in memory
    activeIntervalIds[jobKey] = intervalId;
};


// The getbaseTokenBalance function remains the same as you provided.
export async function getbaseTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
  const provider = new ethers.providers.JsonRpcProvider(providerUrl);
  const tokenContract = new ethers.Contract(
    tokenAddress,
    [
      'function balanceOf(address owner) view returns (uint256)',
      'function decimals() view returns (uint8)'
    ],
    provider
  );

  // Fetch balance and decimals
  const [balance, decimals] = await Promise.all([
    tokenContract.balanceOf(userAddress),
    tokenContract.decimals()
  ]);

  // Convert balance to a human-readable format
  const formattedBalance = ethers.utils.formatUnits(balance, decimals);
  return formattedBalance;
}


/**
 * Review a BASE trade
 * @param bot Telegram bot instance
 * @param chatId Chat ID
 * @param messageId Message ID to edit
 * @param contractAddress Contract address
 */
export const reviewBaseTrade = async (bot: TelegramBot, chatId: number, messageId: number, contractAddress: string) => {
  try {
    // Find the trade
    const trade = await AutoTradeModel.findOne({ chatId, contractAddress, blockchain: 'base' });
    if (!trade) {
      await bot.sendMessage(chatId, '❌ No trade found.');
      return;
    }

    // Get token information
    const tokenInfo = await getTokenInfoFromDexscreener(contractAddress, BlockchainType.BASE);

    // Get current price and price change
    const currentPrice = tokenInfo?.priceUsd || 'Unknown';
    const priceChange = tokenInfo?.priceChange?.h24 || 'Unknown';

    // Format status
    const isSoldStatus = trade.isSold ? 'Yes' : 'No';
    const executionStatus = trade.isInitialized === 'ongoing' ? 'Active' :
                           trade.isInitialized === 'pending' ? 'Pending' : 'Completed';

    // Create detailed trade information
    const tradeDetails =
      `💼 **BASE Trade Details:**
📝 **Contract:** ${contractAddress}
🚀 **Token:** ${trade.contractName}
💵 **Current Price:** $${currentPrice}
📊 **24h Change:** ${priceChange}%
📈 **Take Profit:** ${trade.config.takeProfit}%
📉 **Stop Loss:** ${trade.config.stopLoss}%
💰 **Spend Amount:** ${trade.config.spendAmount} BASE
🔄 **Is Sold:** ${isSoldStatus}
⚙️ **Status:** ${executionStatus}`;

    // Edit the message with updated information
    await bot.editMessageText(tradeDetails, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: createBaseTradeKeyboard(),
    });

    logger.debug('BASE trade review displayed', {
      chatId,
      contractAddress,
      messageId
    });

  } catch (error) {
    logger.error('Error in reviewBaseTrade', {
      chatId,
      contractAddress,
      messageId,
      error: (error as Error).message
    });
    await bot.sendMessage(chatId, `⚠️ An error occurred while reviewing the trade: ${(error as Error).message}`);
  }
};



/**
 * Display the BASE trading screen
 * @param bot Telegram bot instance
 * @param chatId Chat ID
 * @param contractAddress Contract address
 */
export const tradeBaseScreen = async (bot: TelegramBot, chatId: number, contractAddress: string) => {
  try {
    // Get token information
    const tokenInfo = await getTokenInfoFromDexscreener(contractAddress, BlockchainType.BASE);
    if (!tokenInfo) {
      await bot.sendMessage(chatId, '❌ Token information not found.');
      return;
    }

    const { symbol, name } = tokenInfo;

    // Check if the token is a honeypot
    const trader = TraderFactory.getTrader(BlockchainType.BASE);
    const honeypotCheck = await trader.checkHoneypot(contractAddress);

    // Create a new trade record
    const newTrade = new AutoTradeModel({
      chatId,
      blockchain: 'base',
      contractAddress,
      contractName: name,
      isSold: false, // Set initial status
      isInitialized: 'pending', // Set to 'pending' for new trades
      config: {
        takeProfit: 0,
        stopLoss: 0,
        spendAmount: 0,
      },
    });

    await newTrade.save();

    // Create the message with token information
    let message = `💼 **BASE Trading**\n`;
    message += `📝 **Contract:** ${contractAddress}\n`;
    message += `🚀 **Token:** ${name} (${symbol})\n`;
    message += `🔗 [Dexscreener Link](https://dexscreener.com/base/${contractAddress})\n\n`;

    // Add honeypot warning if applicable
    if (honeypotCheck.isHoneypot) {
      message += `⚠️ **HONEYPOT WARNING** ⚠️\n`;
      message += `This token appears to be a honeypot. Trading is not recommended.\n`;
      message += `Reason: ${honeypotCheck.honeypotReason || 'Unknown'}\n\n`;
    }

    message += `What would you like to do next?`;

    // Send the message with keyboard
    const sentMessage = await bot.sendMessage(chatId, message, {
      parse_mode: 'Markdown',
      reply_markup: createBaseTradeKeyboard(),
    });

    logger.info('BASE trade screen displayed', {
      chatId,
      contractAddress,
      messageId: sentMessage.message_id,
      isHoneypot: honeypotCheck.isHoneypot
    });

    // Set up callback query handler for this specific message
    const callbackHandler = async (callbackQuery: TelegramBot.CallbackQuery) => {
      // Only process callbacks for this specific message
      if (callbackQuery.message?.message_id !== sentMessage.message_id) {
        return;
      }

      try {
        const data = JSON.parse(callbackQuery.data!);
        const command = data.command;

        // Acknowledge the callback query to stop the loading indicator
        await bot.answerCallbackQuery(callbackQuery.id);

        switch (command) {
          case 'snipe_set_take_profit_base':
            const takeProfit = await askForBaseTradeInput(bot, chatId, '📈 Set your take profit %:', 'take profit');
            await updateBaseTradeConfig(chatId, contractAddress, 'takeProfit', takeProfit);
            break;

          case 'snipe_set_stop_loss_base':
            const stopLoss = await askForBaseTradeInput(bot, chatId, '📉 Set your stop loss %:', 'stop loss');
            await updateBaseTradeConfig(chatId, contractAddress, 'stopLoss', stopLoss);
            break;

          case 'snipe_set_spend_amount_base':
            const spendAmount = await askForBaseTradeInput(bot, chatId, '💰 Set the amount to spend:', 'spend amount');
            await updateBaseTradeConfig(chatId, contractAddress, 'spendAmount', spendAmount);
            break;

          case 'snipetrade_buy_base':
            await executeBaseTrade(bot, chatId, contractAddress);
            break;

          case 'snipe_review_trade_base':
            await reviewBaseTrade(bot, chatId, callbackQuery.message!.message_id, contractAddress);
            break;

          case 'back_to_main_trade':
            // Remove this callback handler
            bot.removeListener('callback_query', callbackHandler);
            // Handle back to main trade logic
            break;
        }
      } catch (error) {
        logger.error('Error handling BASE trade callback', {
          chatId,
          contractAddress,
          error: (error as Error).message
        });
        await bot.sendMessage(chatId, `⚠️ An error occurred: ${(error as Error).message}`);
      }
    };

    // Add the callback handler
    bot.on('callback_query', callbackHandler);

  } catch (error) {
    logger.error('Error in tradeBaseScreen', {
      chatId,
      contractAddress,
      error: (error as Error).message
    });
    await bot.sendMessage(chatId, `⚠️ An error occurred while accessing the BASE trading screen: ${(error as Error).message}`);
  }
};

