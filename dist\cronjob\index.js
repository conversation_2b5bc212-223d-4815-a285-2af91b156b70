"use strict";
// import cron from 'node-cron';
// import mongoose from 'mongoose';
// import TelegramBot from 'node-telegram-bot-api';
// import Snipe, { ISnipe, ISnipeConfig } from '../models/snipe.model'; // Adjust the path as needed
// import CronJob from '../models/cronjob.model'; // Adjust the path as needed
// import { getLiquidityInfoFromDexscreener } from '../services/checkliquidity.service';
// import { buyBscTokens } from './cronSniper/pancake'; // Import your buy function for Binance Smart Chain
// import { buySolToken } from './cronSniper/jupiter'; // Import your buy function for Solana
// import { buyEthTokens } from './cronSniper/uniswap'; // Import your buy function for Ethereum
// import { buyBaseTokens } from './cronSniper/baseswap'; // Import your buy function for Base
// const buyToken = async (
//     blockchain: string,
//     chatId: number,
//     contractAddress: string,
//     bot: TelegramBot
// ) => {
//     console.log(`Attempting to buy token ${contractAddress} on ${blockchain.toUpperCase()}.`);
//     switch (blockchain) {
//         case 'bsc':
//             // await buyBscTokens(chatId, contractAddress);
//             break;
//         case 'sol':
//             // await buySolToken(chatId, contractAddress);
//             break;
//         case 'eth':
//             // await buyEthTokens(chatId, contractAddress);
//             break;
//         case 'base':
//             await buyBaseTokens(chatId, contractAddress);
//             break;
//         default:
//             throw new Error(`Unsupported blockchain: ${blockchain}`);
//     }
// };
// export const startCronJob = async (bot: TelegramBot, snipe: ISnipe) => {
//     const { chatId, contractAddress, blockchain } = snipe;
//     const executedKey = `isExecuted_${blockchain}` as keyof ISnipeConfig;
//     if (snipe.config[executedKey] as boolean) {
//         console.log(`Cron job already executed for ${contractAddress} on ${blockchain.toUpperCase()}.`);
//         return;
//     }
//     const existingCronJob = await CronJob.findOne({ chatId, contractAddress, blockchain }).exec();
//     const cronTime = existingCronJob ? existingCronJob.cronTime : '* * * * *';
//     const task = cron.schedule(cronTime, async () => {
//         try {
//             // await bot.sendMessage(chatId, `🚀 Snipe started for ${contractAddress} on ${blockchain.toUpperCase()}.`);
//             const tokenInfo = await getLiquidityInfoFromDexscreener(contractAddress);
//             console.log(`Token information for ${contractAddress}:`, tokenInfo);
//             if (tokenInfo.hasLiquidity) {
//                 let attempts = 0;
//                 const retriesOnFail = 1;
//                 while (attempts < retriesOnFail) {
//                     try {
//                         console.log(`Attempt ${attempts + 1} of ${retriesOnFail} to buy token ${contractAddress} on ${blockchain.toUpperCase()}.`);
//                         await buyToken(blockchain, chatId, contractAddress, bot);
//                         // Stop the cron job
//                         console.log(`Token ${contractAddress} on ${blockchain.toUpperCase()} has been bought successfully!`);
//                         await bot.sendMessage(chatId, `✅ Successfully bought token ${contractAddress} on ${blockchain.toUpperCase()}!`, {
//                         });
//                         task.stop();
//                         await CronJob.findOneAndDelete({ chatId, contractAddress, blockchain });
//                         break;
//                     } catch (buyError) {
//                         attempts++;
//                         if (attempts >= retriesOnFail) {
//                             console.error(`Failed to buy after ${attempts} attempts:`, (buyError as Error).message);
//                             await sendBuyManuallyOption(bot, chatId, contractAddress, blockchain, (buyError as Error).message);
//                         }
//                     }
//                 }
//             }
//         } catch (error) {
//             console.error('Error in cron job:', (error as Error).message);
//             await bot.sendMessage(chatId, `❌ Error occurred during snipe`);
//         }
//     });
//     if (!existingCronJob) {
//         const newCronJob = new CronJob({ chatId, contractAddress, blockchain, cronTime });
//         await newCronJob.save();
//     }
// };
// export const executeSnipes = async (bot: TelegramBot) => {
//     try {
//         const snipes = await Snipe.find({
//             'config.isConfigured': true,
//             'config.isBought': false,
//             'config.isExecuted_bsc': false,
//             'config.isExecuted_sol': false,
//             'config.isExecuted_eth': false,
//             'config.isExecuted_base': false
//         });
//         for (const snipe of snipes) {
//             await startCronJob(bot, snipe);
//         }
//         // Check for any existing cron jobs that need to be executed
//         const existingCronJobs = await CronJob.find({});
//         for (const cronJob of existingCronJobs) {
//             const snipe = await Snipe.findOne({
//                 chatId: cronJob.chatId,
//                 contractAddress: cronJob.contractAddress,
//                 blockchain: cronJob.blockchain
//             }).exec();
//             if (snipe) {
//                 const executedKey = `isExecuted_${snipe.blockchain}` as keyof ISnipeConfig;
//                 if (snipe.config[executedKey] === undefined) {
//                     console.error(`Configuration key ${executedKey} does not exist`);
//                 } else if (!(snipe.config[executedKey] as boolean)) {
//                     await startCronJob(bot, snipe);
//                 }
//             }
//         }
//     } catch (error) {
//         console.error('Error in executeSnipes:', (error as Error).message);
//     }
// };
// export const handleCallbackQuery = async (bot: TelegramBot, query: TelegramBot.CallbackQuery) => {
//     const chatId = query.message?.chat.id;
//     const messageId = query.message?.message_id;
//     const data = query.data ? JSON.parse(query.data) : null;
//     if (!chatId || !messageId || !data) {
//         console.error('Invalid callback query data');
//         return;
//     }
//     switch (data.command) {
//         case 'buy_manually':
//             await bot.sendMessage(chatId, `🔍 Please scan the contract address: ${data.contractAddress}`);
//             break;
//         default:
//             console.error('Unknown command in callback data');
//             break;
//     }
// };
// const sendBuyManuallyOption = async (
//     bot: TelegramBot,
//     chatId: number,
//     contractAddress: string,
//     blockchain: string,
//     errorMessage: string
// ) => {
//     const options = {
//         reply_markup: {
//             inline_keyboard: [
//                 [
//                     {
//                         text: '🔍 Buy Manually',
//                         callback_data: JSON.stringify({
//                             command: 'buy_manually',
//                             contractAddress,
//                         }),
//                     },
//                 ],
//             ],
//         },
//     };
//     await bot.sendMessage(chatId, `❌ Failed to buy token on ${blockchain.toUpperCase()} after several attempts. Error: ${errorMessage}. You can try to buy manually:`, options);
// };
