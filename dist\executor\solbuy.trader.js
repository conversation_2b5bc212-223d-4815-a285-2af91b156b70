"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buySolToken = buySolToken;
const web3_js_1 = require("@solana/web3.js");
const cross_fetch_1 = __importDefault(require("cross-fetch"));
const bs58_1 = __importDefault(require("bs58"));
const user_models_1 = __importDefault(require("../models/user.models"));
const trade_model_1 = __importDefault(require("../models/trade.model"));
const RPC_ENDPOINT = process.env.SOL_PROVIDER_URL || '';
const ADMIN_PUBLIC_KEY_STRING = process.env.SOL_ADMIN_PUBLIC_KEY || '';
const SOLANA_ADDRESS = 'So11111111111111111111111111111111111111112';
if (!RPC_ENDPOINT || !ADMIN_PUBLIC_KEY_STRING) {
    throw new Error('Environment variables SOL_PROVIDER_URL and ADMIN_PUBLIC_KEY must be set');
}
const connection = new web3_js_1.Connection(RPC_ENDPOINT);
function getUserSettings(chatId) {
    return __awaiter(this, void 0, void 0, function* () {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }
        yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'sol' } });
        const solConfig = user.sol_config;
        if (!solConfig) {
            throw new Error('SOL configuration is not set for the user');
        }
        const { slippage } = solConfig;
        const { private_key, address } = user.sol_wallet;
        const solCurrentContractAddress = user.solCurrentContractAddress;
        const solCurrentTokenName = user.solCurrentTokenName;
        return { slippage, private_key, address, solCurrentContractAddress, solCurrentTokenName };
    });
}
function getQuote(inputMint, outputMint, amount, slippageBps) {
    return __awaiter(this, void 0, void 0, function* () {
        const quoteResponse = yield (0, cross_fetch_1.default)(`https://quote-api.jup.ag/v6/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}`);
        const quoteData = yield quoteResponse.json();
        if (!quoteData) {
            throw new Error('Failed to get quote data');
        }
        return quoteData;
    });
}
function getSwapTransaction(quoteData, userPublicKey) {
    return __awaiter(this, void 0, void 0, function* () {
        const swapResponse = yield (0, cross_fetch_1.default)('https://quote-api.jup.ag/v6/swap', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                quoteResponse: quoteData,
                userPublicKey: userPublicKey,
                wrapAndUnwrapSol: true,
                dynamicComputeUnitLimit: true,
                prioritizationFeeLamports: 100000, // Set to bribe the miners
                autoMultiplier: 2,
            })
        });
        const swapData = yield swapResponse.json();
        if (!swapData || !swapData.swapTransaction) {
            throw new Error('Failed to get swap transaction data');
        }
        return swapData.swapTransaction;
    });
}
function executeTransaction(transaction, connection) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        let tryAgain = true;
        let txid;
        const maxTries = 10;
        let attempts = 0;
        while (tryAgain && attempts < maxTries) {
            try {
                attempts++;
                const rawTransaction = transaction.serialize();
                txid = yield connection.sendRawTransaction(rawTransaction, {
                    skipPreflight: true,
                    maxRetries: 2
                });
                yield new Promise(r => setTimeout(r, 2000));
                const result = yield connection.getSignatureStatus(txid, {
                    searchTransactionHistory: true,
                });
                if (((_a = result === null || result === void 0 ? void 0 : result.value) === null || _a === void 0 ? void 0 : _a.err) === null) {
                    tryAgain = false;
                }
                else if (attempts >= maxTries) {
                    tryAgain = false;
                    throw new Error('Max retries exceeded');
                }
            }
            catch (error) {
                console.error('Error during transaction execution:', error);
                if (attempts >= maxTries) {
                    throw new Error('Max retries exceeded');
                }
            }
        }
        if (!txid) {
            throw new Error('Transaction ID is undefined');
        }
        yield connection.confirmTransaction(txid);
        return txid;
    });
}
function buySolToken(chatId, amount) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { slippage, private_key, address, solCurrentTokenName, solCurrentContractAddress } = yield getUserSettings(chatId);
            const currentBlockchain = 'sol';
            const inputMint = SOLANA_ADDRESS;
            const outputMint = solCurrentContractAddress !== null && solCurrentContractAddress !== void 0 ? solCurrentContractAddress : '';
            if (!outputMint) {
                throw new Error('Output mint address is not defined');
            }
            console.log(outputMint);
            const slippageBps = slippage * 100;
            const secretKey = bs58_1.default.decode(private_key);
            const wallet = web3_js_1.Keypair.fromSecretKey(secretKey);
            const solAmountInLamports = BigInt(Math.round(amount * 1e9));
            const feeAmount = amount * 0.01;
            const feeInLamports = BigInt(Math.round(feeAmount * 1e9));
            const quoteData = yield getQuote(inputMint, outputMint, solAmountInLamports, slippageBps);
            const swapTransaction = yield getSwapTransaction(quoteData, wallet.publicKey.toString());
            const swapTransactionBuf = Buffer.from(swapTransaction, 'base64');
            const transaction = web3_js_1.VersionedTransaction.deserialize(swapTransactionBuf);
            const feeTransferInstruction = web3_js_1.SystemProgram.transfer({
                fromPubkey: wallet.publicKey,
                toPubkey: new web3_js_1.PublicKey(ADMIN_PUBLIC_KEY_STRING),
                lamports: feeInLamports,
            });
            const addressLookupTableAccounts = yield Promise.all(transaction.message.addressTableLookups.map((lookup) => __awaiter(this, void 0, void 0, function* () {
                const accountInfo = yield connection.getAccountInfo(lookup.accountKey);
                if (!accountInfo) {
                    throw new Error(`Account info not found for key: ${lookup.accountKey.toBase58()}`);
                }
                return new web3_js_1.AddressLookupTableAccount({
                    key: lookup.accountKey,
                    state: web3_js_1.AddressLookupTableAccount.deserialize(accountInfo.data),
                });
            })));
            let message = web3_js_1.TransactionMessage.decompile(transaction.message, { addressLookupTableAccounts });
            message.instructions.push(feeTransferInstruction);
            transaction.message = message.compileToV0Message(addressLookupTableAccounts);
            transaction.sign([wallet]);
            const txid = yield executeTransaction(transaction, connection);
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user) {
                throw new Error('User not found');
            }
            const existingTrade = yield trade_model_1.default.findOne({
                userId: user._id,
                contractAddress: outputMint,
                isSold: false
            }).exec();
            if (existingTrade) {
                existingTrade.buyAmount += amount;
                yield existingTrade.save();
                console.log('Updated existing trade with new buy amount');
            }
            else {
                const newTrade = new trade_model_1.default({
                    userId: user._id,
                    chatId: chatId,
                    walletId: user.sol_wallet.address,
                    contractAddress: outputMint,
                    contractName: solCurrentTokenName,
                    buyAmount: amount,
                    sellAmount: 0,
                    takeProfit: 0,
                    stopLoss: 0,
                    isSold: false,
                    blockchain: 'sol'
                });
                yield newTrade.save();
                console.log('Created new trade entry');
            }
            yield user_models_1.default.updateOne({ _id: user._id }, { $push: { [`trades.${currentBlockchain}`]: existingTrade ? existingTrade._id : user._id } });
            console.log('User trades updated successfully');
            return txid;
        }
        catch (error) {
            console.error('Error during token purchase:', error);
            throw error;
        }
    });
}
