import axios from 'axios';
import { logger } from './logger/LoggerService';
import { sendToUser, isUserConnected } from '../utils/socket.utils';
import Snipe from '../models/snipe.model';

interface TokenInfo {
  hasLiquidity: boolean;
  message?: string;
  symbol?: string;
  name?: string;
  priceNative?: string;
  priceUsd?: string;
  chainId?: string;
  priceChange?: string;
  dexId?: string;
  totalSupply?: string;
  pairAddress?: string;
  marketCap?: string;
  address?: string;
}

// Function to get token info from Dexscreener
/**
 * Send token price updates to users who have configured snipes for this token
 * @param contractAddress Token contract address
 * @param tokenInfo Token information
 */
async function sendTokenPriceUpdates(contractAddress: string, tokenInfo: TokenInfo): Promise<void> {
  try {
    // Find all users who have configured snipes for this token
    const snipes = await Snipe.find({
      contractAddress: contractAddress.toLowerCase(),
      'config.isConfigured': true
    });

    if (snipes.length === 0) {
      return;
    }

    // Group snipes by chatId to avoid duplicate notifications
    const chatIds = new Set<number>();
    for (const snipe of snipes) {
      chatIds.add(snipe.chatId);
    }

    // Prepare the price update data
    const priceData = {
      contractAddress,
      symbol: tokenInfo.symbol,
      name: tokenInfo.name,
      priceUsd: tokenInfo.priceUsd,
      priceNative: tokenInfo.priceNative,
      priceChange: tokenInfo.priceChange,
      marketCap: tokenInfo.marketCap,
      timestamp: new Date().toISOString()
    };

    // Send to each user who has a snipe configured for this token
    for (const chatId of chatIds) {
      if (isUserConnected(chatId)) {
        sendToUser(chatId, priceData, 'tokenPriceUpdate');
      }
    }

    logger.debug(`Sent price updates for ${contractAddress} to ${chatIds.size} users`);
  } catch (error) {
    logger.error(`Error sending token price updates for ${contractAddress}`, {
      error: (error as Error).message,
      contractAddress
    });
  }
}

export const getLiquidityInfoFromDexscreener = async (contractAddress: string): Promise<TokenInfo> => {
  const url = `https://api.dexscreener.com/latest/dex/tokens/${contractAddress}`;

  try {
    const response = await axios.get(url);

    // Check if the pairs array is null or empty
    if (!response.data.pairs || response.data.pairs.length === 0) {
      logger.debug(`No liquidity found for the token ${contractAddress}.`);
      return {
        hasLiquidity: false,
        message: `No liquidity found for the token ${contractAddress}.`
      };
    }

    const latestPair = response.data.pairs[0];

    const {
      baseToken,
      priceNative,
      priceUsd,
      chainId,
      priceChange,
      dexId,
      totalSupply,
      liquidity
    } = latestPair;

    const symbol = baseToken.symbol;
    const name = baseToken.name || 'Unknown Token';
    const pairAddress = latestPair.pairAddress || 'Not available';
    const marketCap = latestPair.marketCap || 'Not available';

    const tokenInfo = {
      hasLiquidity: true,
      symbol,
      name,
      priceNative,
      priceUsd,
      chainId,
      priceChange,
      dexId,
      totalSupply,
      pairAddress,
      marketCap,
      address: baseToken.address
    };

    // Send price updates to users who have configured snipes for this token
    sendTokenPriceUpdates(contractAddress, tokenInfo).catch(error => {
      logger.error(`Failed to send token price updates for ${contractAddress}`, {
        error: (error as Error).message,
        contractAddress
      });
    });

    return tokenInfo;
  } catch (error) {
    logger.error('Error fetching token info from Dexscreener:', {
      error: (error as Error).message,
      contractAddress
    });
    throw new Error('Error fetching token info from Dexscreener. Please try again later.');
  }
};

