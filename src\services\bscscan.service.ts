import TelegramBot, { SendMessageOptions, InlineKeyboardButton, InlineKeyboardMarkup } from 'node-telegram-bot-api';
import User from '../models/user.models';
import { Trade } from '../models/trade.model';
import TradeModel  from '../models/trade.model';
import { checkEVMHoneypot } from './checkEVMHoneyPot.service';
import { getTokenInfoFromDexscreener } from '../services/dexscreener.service';
import tokenAbi from '../abis/ERC20ABI.json';
import { ethers } from 'ethers';
import { handleBscDashboard } from '../screens/dashboards/bsc.dashboard';
import { generateBSCWallet } from './wallet.service';
import { INPUT_CONTRACT_ADDRESS } from '../botConvo';
import { SET_BSC_SPEND, INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE } from '../botConvo';
import { setCurrentContractAddress, getCurrentContractAddress } from '../globalState';
import { createBscTradeKeyboard } from '../screens/trade/bsctrade';

// import { sellBscTokens } from '../trader/pancake.trader';
import { createSession,  } from './sessions.service';
import mongoose from 'mongoose';
import { TraderFactory, BlockchainType } from './trading/TradingInterface';


interface TokenDetails {
  totalSupply?: any | null;
  marketCap?: number;
  checkHoneypot?: any;
  tokenInfo?: any;
  resultMessage?: any;
  tokenAddress?: any;
}

let localState: Record<string, TokenDetails> = {};
let messageIdStore: Record<string, number> = {};


// Create a provider instance
const providerUrl = process.env.BSC_PROVIDER_URL;

if (!providerUrl) {
  throw new Error('BSC_PROVIDER_URL is not defined in the environment variables.');
}

const provider = new ethers.providers.JsonRpcProvider(providerUrl);

// Get total supply
const getTotalSupply = async (tokenAddress: string): Promise<string | null> => {
  try {
    // Create a contract instance
    const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, provider);

    // Call the decimals function
    const decimals = await tokenContract.decimals();

    // Call the totalSupply function
    const totalSupply = await tokenContract.totalSupply();

    // Convert the total supply from a BigNumber to a more readable format
    const formattedTotalSupply = ethers.utils.formatUnits(totalSupply, decimals);

    return formattedTotalSupply;
  } catch (error) {
    console.error('Error fetching total supply:', error);
    return null;
  }
};


// Handle BSC scan contract

export const handleBSCScanContract = async (bot: TelegramBot, chat_id: number, userAddress: string) => {
  try {
    if (!userAddress) {
      throw new Error('Invalid contract address.');
    }

    const previousContractAddress = getCurrentContractAddress(BlockchainType.BSC);
    if (previousContractAddress && localState[previousContractAddress]) {
        delete localState[previousContractAddress];
    }

    localState[userAddress] = localState[userAddress] || {};
    setCurrentContractAddress(BlockchainType.BSC, userAddress);

    // Check if the contract is a honeypot
    const honeypotResult = await checkEVMHoneypot(userAddress, BlockchainType.BSC);

    // Get token info from Dexscreener
    const tokenInfo = await getTokenInfoFromDexscreener(userAddress);

    // Compute additional data
    const totalSupply = await getTotalSupply(userAddress);
    if (totalSupply === null) {
      throw new Error('Failed to fetch total supply.');
    }

    const totalSupplyNumber = Number(totalSupply);
    const priceUsdNumber = Number(tokenInfo.priceUsd);
    const marketCap = priceUsdNumber * totalSupplyNumber;

    // Utility function to format numbers for display
    const formatNumber = (num: number): string => {
        if (num >= 1_000_000_000_000) {
            return (num / 1_000_000_000_000).toFixed(2) + 'T'; // Trillions
        } else if (num >= 1_000_000_000) {
            return (num / 1_000_000_000).toFixed(2) + 'B'; // Billions
        } else if (num >= 1_000_000) {
            return (num / 1_000_000).toFixed(2) + 'M'; // Millions
        } else if (num >= 1_000) {
            return (num / 1_000).toFixed(2) + 'K'; // Thousands
        } else {
            return num.toFixed(2); // Less than a thousand
        }
    };

    const formattedTotalSupply = formatNumber(totalSupplyNumber);
    const formattedMarketCap = formatNumber(marketCap);
    const formattedLiquidity = formatNumber(Number(tokenInfo.liquidity.usd));

    const priceChangeH1 = tokenInfo.priceChange?.h1 || 0;
    const priceChangeEmoji = priceChangeH1 > 0 ? '🟢🟢🟢🟢🟢🟢' : '🔴🔴🔴🔴🔴🔴';

    const roundedBuyTax = Math.ceil(parseFloat(String(honeypotResult.buyTax || 0)));
    const roundedSellTax = Math.ceil(parseFloat(String(honeypotResult.sellTax || 0)));

    const resultMessage = `
🪙 ${tokenInfo.symbol} (${tokenInfo.name}) || 📈 ${priceChangeH1 > 0 ? '+' : ''}${priceChangeH1}%  || 🏦 ${tokenInfo.dexId} || ${tokenInfo.chainId}

${priceChangeEmoji}  Price Change: ${priceChangeH1 > 0 ? '+' : ''}${priceChangeH1}%

CA: ${tokenInfo.address}
LP: ${tokenInfo.pairAddress}

💼 TOTAL SUPPLY: ${formattedTotalSupply} ${tokenInfo.symbol}
🏷️ MC: $${formattedMarketCap}
💧 LIQUIDITY: $${formattedLiquidity}
💵 PRICE: $${tokenInfo.priceUsd}

🔍 Honeypot/Rugpull Risk: ${honeypotResult.isHoneypot ? 'HIGH' : 'LOW'}
📉 Buy Tax: ${roundedBuyTax}%
📈 Sell Tax: ${roundedSellTax}%

⚠️⚠️⚠️⚠️ ${honeypotResult.honeypotReason || 'No specific risks identified'}
    `;

    const user = await User.findOne({ chat_id }).exec();
    if (user) {
        user.bscCurrentTokenName = tokenInfo.name;
        user.bscCurrentContractAddress = userAddress;
        await user.save();
    }

    localState[userAddress] = {
        totalSupply: totalSupplyNumber, // Store raw number
        marketCap: marketCap,          // Store raw number
        checkHoneypot: honeypotResult,
        tokenInfo,
        resultMessage
    };

    const previousMessageId = messageIdStore[chat_id];
    if (previousMessageId) {
        await bot.deleteMessage(chat_id, previousMessageId);
    }

    const sentMessage = await bot.sendMessage(chat_id, resultMessage, {
      parse_mode: 'Markdown',
      reply_markup: createBSCKeyboard()
    });

    messageIdStore[chat_id] = sentMessage.message_id;
    return sentMessage.message_id;

  } catch (error) {
    console.error('Error scanning contract:', error);
    await bot.sendMessage(chat_id, 'There was an error scanning the contract. Please try again later.');
  }
};



// Create sell options keyboard
export const getSellOptionsKeyboard = async (): Promise<InlineKeyboardButton[][]> => {
  return [
    [
      { text: 'Sell All', callback_data: JSON.stringify({ command: 'sell_all_bsc' }) },
      { text: 'Sell 25%', callback_data: JSON.stringify({ command: 'sell_25_bsc' }) }
    ],
    [
      { text: 'Sell 50%', callback_data: JSON.stringify({ command: 'sell_50_bsc' }) },
      { text: 'Sell 75%', callback_data: JSON.stringify({ command: 'sell_75_bsc' }) }
    ],
    [
      { text: 'Back', callback_data: JSON.stringify({ command: 'back_bsc' }) }
    ]
  ];
};




export const createBSCKeyboard = (): { inline_keyboard: InlineKeyboardButton[][] } => {
  return {
    inline_keyboard: [
      [
        { text: '◀️ Previous', callback_data: JSON.stringify({ command: 'previous_bsc' }) },
        { text: '▶️ Next', callback_data: JSON.stringify({ command: 'next_bsc' }) }
      ],
      [
        { text: '🔄 Refresh', callback_data: JSON.stringify({ command: 'refresh_bsc' }) },
        { text: '💸 Sell', callback_data: JSON.stringify({ command: 'sell_bsc' }) }
      ],
      [
        { text: '📈 Chart', callback_data: JSON.stringify({ command: 'chart_bsc' }) },
        { text: '💸 Spend X BSC', callback_data: JSON.stringify({ command: 'spend_bsc' }) }
      ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ]
      // [
      //   { text: '💰 Advanced Trade', callback_data: JSON.stringify({ command: 'snipetrade_dashboard_bsc' }) }
      // ]
    ]
  };
};

// Update message with sell options
export const updateBSCMessageWithSellOptions = async (bot: TelegramBot, chatId: number, messageId: number) => {
  try {
    const sellOptions = await getSellOptionsKeyboard();

    // Fetch the current contract address from the global state
    const contractAddress = getCurrentContractAddress(BlockchainType.BSC);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    // Fetch token details using the contract address
    const tokenDetails = getTokenDetails(contractAddress);

    if (!tokenDetails || !tokenDetails.resultMessage) {
      console.error('No result message found for the token address.');
      return;
    }
    // Update the message with the new sell options
    await bot.editMessageText(tokenDetails.resultMessage, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: sellOptions
      }
    });
  } catch (error) {
    console.error('Failed to update sell options:', error);
  }
};

export const updateBSCMessageWithTradeOptions = async (
  bot: TelegramBot,
  chatId: number,
  messageId: number
): Promise<void> => {
  try {
    const tradeOptions = createBscTradeKeyboard();

    // Fetch the current contract address from the global state
    const contractAddress = getCurrentContractAddress(BlockchainType.BSC);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    await bot.editMessageReplyMarkup(
      { inline_keyboard: tradeOptions.inline_keyboard },
      { chat_id: chatId, message_id: messageId }
    );
  } catch (error) {
    console.error('Failed to update trade options:', error);
  }
};


export const mainBscMenu = async (bot: TelegramBot, chatId: number, messageId: number) => {
  try {
    // Create the BSC keyboard options
    const bscOptions = createBSCKeyboard();

    // Fetch the current contract address from the global state
    const contractAddress = getCurrentContractAddress(BlockchainType.BSC);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    // Fetch token details using the contract address
    const tokenDetails = getTokenDetails(contractAddress);

    if (!tokenDetails || !tokenDetails.resultMessage) {
      console.error('No result message found for the token address.');
      return;
    }

    // Update the message with the new BSC options
    await bot.editMessageText(tokenDetails.resultMessage, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: bscOptions.inline_keyboard
      }
    });
  } catch (error) {
    console.error('Failed to update BSC menu:', error);
    await bot.sendMessage(chatId, 'Failed to update the menu. Please try again later.');
  }
};



export const handleGenerateNewBscWallet = async (bot: TelegramBot, chatId: number) => {
  try {
    const user = await User.findOne({ chat_id: chatId });

    if (!user) {
      await bot.sendMessage(chatId, 'User not found.');
      return;
    }

    const newWallet = generateBSCWallet();

    await User.updateOne(
      { chat_id: chatId },
      { $set: { 'bsc_wallet.address': newWallet.address, 'bsc_wallet.private_key': newWallet.privateKey } }
    );

    // Send confirmation message
    await bot.sendMessage(
      chatId,
      `✅ You clicked "Generate New Wallet" in the BSC blockchain.\nNew wallet address: \`\`\`${newWallet.address}\`\`\`\nNew wallet Private Key: \`\`\`${newWallet.privateKey}\`\`\``
    );
    await handleBscDashboard(bot, chatId);
  } catch (error) {
    console.error('Failed to generate new wallet:', error);
    await bot.sendMessage(chatId, 'There was an error generating a new wallet. Please try again later.');
  }
};

// Message ID storage
let promptMessageIdStore: Record<number, { promptId?: number, replyId?: number }> = {};

export const askForContractAddress = async (bot: TelegramBot, chatId: number, messageId: number): Promise<string> => {
  try {
    // Save previous message IDs if any
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    // Send message asking for contract address
    const sentMessage = await bot.sendMessage(chatId, INPUT_CONTRACT_ADDRESS, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    // Update the message ID store
    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received contract address:', msg.text); // Log the received text
          // Save the reply message ID
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid contract address.'));
        }
      });
    });
  } catch (error) {
    console.error('Failed to ask for contract address:', error);
    throw error;
  }
};

export const askForBSCToken = async (bot: TelegramBot, chatId: number, messageId: number): Promise<string> => {
  try {
    // Save previous message IDs if any
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    // Send message asking for BSC token
    const sentMessage = await bot.sendMessage(chatId, INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    // Update the message ID store
    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received BSC token:', msg.text); // Log the received text
          // Save the reply message ID
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid BSC token.'));
        }
      });
    });
  } catch (error) {
    console.error('Failed to ask for BSC token:', error);
    throw error;
  }
};

let tradesState: any[] = []; // Store the fetched trades
let currentIndex: number = 0; // Track the current trade index


export async function fetchAndDisplayActiveBscTrades(bot: TelegramBot, chatId: number, blockchain: string) {
  try {
    // Find active trades for the specific user based on chatId and blockchain
    const tradesState = await TradeModel.find({ chatId, isSold: false, blockchain }).exec();
    let currentIndex = 0;

    if (tradesState.length === 0) {
      await bot.sendMessage(chatId, 'No active trades found!');
      return;
    }

    // Display the current trade information
    await displayBscCurrentTrade(bot, chatId, tradesState, currentIndex);
  } catch (error) {
    console.error('Error fetching trades:', error);
    await bot.sendMessage(chatId, 'An error occurred while fetching trades.');
  }
}



export async function displayBscCurrentTrade(bot: TelegramBot, chatId: number, tradesState: any[], index: number, messageId?: number) {
  const trade = tradesState[index];
  const message = `
<b>Contract:</b> ${trade.contractName} (${trade.contractAddress})
<b>Wallet:</b> ${trade.walletId}
<b>Buy Amount:</b> ${trade.buyAmount}  <b>Sell Amount:</b> ${trade.sellAmount}
<b>Sold:</b> ${trade.isSold ? 'Yes' : 'No'}
  `;

  // Update user details
  await User.findOneAndUpdate({ chat_id: chatId }, {
    bscCurrentContractAddress: trade.contractAddress,
    bscCurrentTokenName: trade.contractName,
  });


  // Update global state
  setCurrentContractAddress(BlockchainType.BSC, trade.contractAddress);

  const options = {
    parse_mode: "HTML" as const,
    disable_web_page_preview: true,
    reply_markup: {
      inline_keyboard: [
        [
          {
            text: "Sell 25%",
            callback_data: JSON.stringify({ command: "sell_25_bsc" }),
          },
          {
            text: "Sell 50%",
            callback_data: JSON.stringify({ command: "sell_50_bsc" }),
          },
        ],
        [
          {
            text: "Sell 75%",
            callback_data: JSON.stringify({ command: "sell_75_bsc" }),
          },
          {
            text: "Sell All",
            callback_data: JSON.stringify({ command: "sell_all_bsc" }),
          },
        ],
        [
          {
            text: "Previous",
            callback_data: JSON.stringify({ command: "previous_bsc" }),
          },
          {
            text: "Next",
            callback_data: JSON.stringify({ command: "next_bsc" }),
          },
        ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ],
      ],
    },
  };

  if (messageId) {
    await bot.editMessageText(message, { chat_id: chatId, message_id: messageId, ...options });
  } else {
    await bot.sendMessage(chatId, message, options);
  }
}



export const handleRefresh = async (bot: TelegramBot, chatId: number, callbackQuery?: TelegramBot.CallbackQuery) => {
  try {
    const currentContractAddress = getCurrentContractAddress(BlockchainType.BSC);
    if (!currentContractAddress) {
      throw new Error('No contract address is currently set.');
    }

    await handleBSCScanContract(bot, chatId, currentContractAddress);

    // If a callbackQuery is provided, show an alert
    if (callbackQuery) {
      const callbackQueryId = callbackQuery.id;
      await bot.answerCallbackQuery(callbackQueryId, {
        text: "Token Refresh Successfully!",
        show_alert: true
      });
    }

  } catch (error) {
    console.error('Failed to refresh contract details:', error);
    await bot.sendMessage(chatId, 'Failed to refresh contract details. Please try again later.');
  }
};


export const handleChart = async (bot: TelegramBot, chatId: number) => {
  try {
    const currentContractAddress = getCurrentContractAddress(BlockchainType.BSC);

    if (currentContractAddress) {
      const dexscreenerUrl = `https://dexscreener.com/bsc/${currentContractAddress}`;
      const chartMessage = await bot.sendMessage(chatId, `<a href="${dexscreenerUrl}">Click here to view the chart on Dexscreener</a>`, { parse_mode: 'HTML' });

      promptMessageIdStore[chatId] = { promptId: chartMessage.message_id };
    } else {
      const errorMessage = await bot.sendMessage(chatId, 'Error: Current contract address is not set.');

      promptMessageIdStore[chatId] = { promptId: errorMessage.message_id };
    }
  } catch (error) {
    console.error('Failed to handle chart action:', error);
  }
};


export const handleSpendBSC = async (bot: TelegramBot, chatId: number): Promise<string> => {
  try {
    const sentMessage = await bot.sendMessage(chatId, SET_BSC_SPEND, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received buy amount:', msg.text);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid buy amount.'));
        }
      });
    });

  } catch (error) {
    console.error('Error handling spend BSC:', error);
    throw new Error('Failed to handle spend BSC action.');
  }
};






export const handleBscSellAll = async (bot: TelegramBot, chatId: number) => {
  try {
    const user = await User.findOne({ chat_id: chatId }).exec();
    if (!user) {
      throw new Error('User not found');
    }

    const { address: userAddress } = user.bsc_wallet;
    const tokenAddress = user.bscCurrentContractAddress || '';

    if (!tokenAddress) {
      throw new Error('No contract address found for user');
    }

    // Get token balance
    const tokenBalanceString = await getBscTokenBalance(tokenAddress, userAddress);
    const tokenBalanceNumber = parseFloat(tokenBalanceString);

    if (isNaN(tokenBalanceNumber) || tokenBalanceNumber <= 0) {
      throw new Error('Invalid token balance');
    }

       const trader = TraderFactory.getTrader(BlockchainType.BSC);
       const receipt = await trader.sellTokens(tokenAddress, 100, userAddress);

    if (receipt && receipt.transactionHash && receipt.blockNumber) {
      const { transactionHash, blockNumber } = receipt;
      await bot.sendMessage(chatId, `✅ Successfully sold all your tokens:\n\n` +
        `🔗 Transaction Hash: ${transactionHash}\n` +
        `🧱 Block Number: ${blockNumber}\n\n` +
        `👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${transactionHash}\nTo see your transaction`);
    } else {
      await bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
    }
  } catch (error) {
    console.error('Error selling all tokens:', error);
    // Type guard for `Error` type
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    await bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are selling less than required amount and unstable network.`);
  }
};

async function getBscTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
  const provider = new ethers.providers.JsonRpcProvider('https://bsc-dataseed.binance.org/');

  const tokenContract = new ethers.Contract(
    tokenAddress,
    [
      'function balanceOf(address owner) view returns (uint256)',
      'function decimals() view returns (uint8)'
    ],
    provider
  );

  // Fetch balance and decimals
  const [balance, decimals] = await Promise.all([
    tokenContract.balanceOf(userAddress),
    tokenContract.decimals()
  ]);

  // Convert balance to a human-readable format
  const formattedBalance = ethers.utils.formatUnits(balance, decimals);
  return formattedBalance;
}



export const sellBscTokenPercentage = async (bot: TelegramBot, chatId: number, percentage: number) => {
  try {
    const user = await User.findOne({ chat_id: chatId }).exec();
    if (!user) {
      throw new Error('User not found');
    }

    const { address: userAddress } = user.bsc_wallet;
    const tokenAddress = user.bscCurrentContractAddress;

    if (!tokenAddress) {
      throw new Error('No contract address found for user');
    }

    // Ensure tokenBalance is converted to a number
    const tokenBalanceString = await getBscTokenBalance(tokenAddress, userAddress);
    const tokenBalance = parseFloat(tokenBalanceString); // Convert to number

    if (isNaN(tokenBalance) || tokenBalance <= 0) {
      throw new Error('Insufficient token balance');
    }

    const amountToSell = tokenBalance * (percentage / 100);
    if (amountToSell <= 0) {
      throw new Error('Invalid percentage value');
    }

    console.log('Amount to sell:', amountToSell);
    const trader = TraderFactory.getTrader(BlockchainType.BSC);
    const receipt = await trader.sellTokens(tokenAddress, percentage, userAddress);

    if (receipt && receipt.transactionHash && receipt.blockNumber) {
      const { transactionHash, blockNumber } = receipt;
      await bot.sendMessage(chatId, `✅ Successfully sold ${percentage}% of your tokens:\n\n` +
        `🔗 Transaction Hash: ${transactionHash}\n` +
        `🧱 Block Number: ${blockNumber}\n\n` +
        `👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${transactionHash}\nTo see your transaction`);
    } else {
      await bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
    }
  } catch (error) {
    console.error('Error selling tokens:', error);
    // Type guard for `Error` type
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    await bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are selling less than required amount and unstable network.`);
  }
};



// Retrieve token details by contract address
export const getTokenDetails = (contractAddress: string): TokenDetails | undefined => {
  return localState[contractAddress];
};
export { handleBscDashboard };

