import EventEmitter from 'events';
import TelegramBot from 'node-telegram-bot-api';
import mongoose from 'mongoose';
import User, { IUser, IWalletConfig } from '../models/user.models';

const configUpdateEmitter = new EventEmitter();

const createConfigKeyboard = (config: IWalletConfig): { inline_keyboard: TelegramBot.InlineKeyboardButton[][] } => ({
  inline_keyboard: [
    [
      { text: `Slippage: ${config.slippage}`, callback_data: JSON.stringify({ command: 'set_slippage_base' }) },
      { text: `Max Gas Price: ${config.maxGasPrice}`, callback_data: JSON.stringify({ command: 'set_max_gas_price_base' }) },
    ],
    [
      { text: `Max Gas Limit: ${config.maxGasLimit}`, callback_data: JSON.stringify({ command: 'set_max_gas_limit_base' }) },
      { text: `Min Liquidity: ${config.minLiquidity}`, callback_data: JSON.stringify({ command: 'set_min_liquidity_base' }) },
    ],
    [
      { text: `Max Market Cap: ${config.maxMarketCap}`, callback_data: JSON.stringify({ command: 'set_max_market_cap_base' }) },
      { text: `Max Liquidity: ${config.maxLiquidity}`, callback_data: JSON.stringify({ command: 'set_max_liquidity_base' }) },
    ],
    [
      { text: `Auto Buy: ${config.autoBuy ? '✅' : '❌'}`, callback_data: JSON.stringify({ command: 'toggle_auto_buy_base', state: config.autoBuy }) },
      { text: `Auto Sell: ${config.autoSell ? '✅' : '❌'}`, callback_data: JSON.stringify({ command: 'toggle_auto_sell_base', state: config.autoSell }) },
    ],
    [
      { text: `Auto Approve: ${config.autoApprove ? '✅' : '❌'}`, callback_data: JSON.stringify({ command: 'toggle_auto_approve_base', state: config.autoApprove }) },
      { text: '⬅️ Back', callback_data: JSON.stringify({ command: 'back_to_main_snipe_base' }) },
    ],
    [
        {
          text: "* Dismiss message",
          callback_data: JSON.stringify({
            command: "dismiss_message",
          }),
        },
      ]
  ],
});

const updateConfig = async (
  bot: TelegramBot,
  chatId: number,
  updates: Partial<IWalletConfig>
): Promise<void> => {
  try {
    const user = await User.findOne({ chat_id: chatId }).exec();
    if (!user) throw new Error('User not found.');

    const updatedConfigField = `${user.currentBlockchain}_config` as ConfigField;
    const updatedConfig = { ...user[updatedConfigField], ...updates };

    const configUpdate = {
      [updatedConfigField]: updatedConfig,
    };

    await User.updateOne({ chat_id: chatId }, { $set: configUpdate }).exec();

    configUpdateEmitter.emit('configUpdated', chatId, updatedConfig, bot);

    // Send a success message
    const successMessage = await bot.sendMessage(chatId, '✅ Configuration updated successfully.');

    // Delete the success message after 5 seconds
    setTimeout(async () => {
      try {
        await bot.deleteMessage(chatId, successMessage.message_id);
      } catch (error) {
        console.error('Failed to delete success message:', error);
      }
    }, 5000);

  } catch (error) {
    console.error('Error updating configuration:', error);
    await bot.sendMessage(chatId, '❌ An error occurred while updating your wallet configurations.');
  }
};

// Event listener for updating the keyboard
configUpdateEmitter.on('configUpdated', async (chatId: number, updatedConfig: IWalletConfig, bot: TelegramBot) => {
  try {
    const user = await User.findOne({ chat_id: chatId }).exec();
    if (!user) throw new Error('User not found.');

    const keyboard = createConfigKeyboard(updatedConfig);

    const messageId = user[`${user.currentBlockchain}_dashboard_message_id`];

    await bot.editMessageReplyMarkup(keyboard, {
      chat_id: chatId,
      message_id: messageId,
    });
  } catch (error) {
    console.error('Error updating the configuration keyboard:', error);
  }
});

const promptMessageIdStore: { [chatId: number]: { promptId?: number, replyId?: number } } = {};

const askForConfigInput = async (bot: TelegramBot, chatId: number, promptMessage: string, field: string): Promise<string> => {
  try {
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    const sentMessage = await bot.sendMessage(chatId, promptMessage, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log(`Received ${field}:`, msg.text);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error(`Invalid ${field}.`));
        }
      });
    });
  } catch (error) {
    console.error(`Failed to ask for ${field}:`, error);
    throw error;
  }
};

type ConfigFieldMap = {
  bsc: 'bsc_config',
  sol: 'sol_config',
  eth: 'eth_config',
  base: 'base_config'
};

type ConfigField = ConfigFieldMap[IUser['currentBlockchain']];

export const showConfigKeyboard = async (bot: TelegramBot, chatId: number): Promise<void> => {
  try {
    const user = await User.findOne({ chat_id: chatId }).exec();
    if (!user) throw new Error('User not found.');

    const configField: ConfigField = `${user.currentBlockchain}_config` as ConfigField;
    const config = user[configField] as IWalletConfig;

    const keyboard = createConfigKeyboard(config);

    const sentMessage = await bot.sendMessage(chatId, 'Please select the configuration option to edit:', {
      reply_markup: keyboard,
    });

    user[`${user.currentBlockchain}_dashboard_message_id`] = sentMessage.message_id;
    await user.save();

    bot.on('callback_query', async (callbackQuery) => {
        const { message, data } = callbackQuery;
        const chatId = message?.chat.id;
        const messageId = message?.message_id;
      
        if (!chatId || !data) return;
      
        const parsedData = JSON.parse(data);
        const command = parsedData.command;
      
        try {
          switch (command) {
            case 'set_slippage_base':
              const slippage = await askForConfigInput(bot, chatId, 'Enter new slippage percentage:', 'slippage');
              await updateConfig(bot, chatId, { slippage: parseFloat(slippage) });
              break;
      
            case 'set_max_gas_price_base':
              const maxGasPrice = await askForConfigInput(bot, chatId, 'Enter new max gas price:', 'maxGasPrice');
              await updateConfig(bot, chatId, { maxGasPrice: parseFloat(maxGasPrice) });
              break;
      
            case 'set_max_gas_limit_base':
              const maxGasLimit = await askForConfigInput(bot, chatId, 'Enter new max gas limit:', 'maxGasLimit');
              await updateConfig(bot, chatId, { maxGasLimit: parseFloat(maxGasLimit) });
              break;
      
            case 'set_min_liquidity_base':
              const minLiquidity = await askForConfigInput(bot, chatId, 'Enter new min liquidity:', 'minLiquidity');
              await updateConfig(bot, chatId, { minLiquidity: parseFloat(minLiquidity) });
              break;
      
            case 'set_max_market_cap_base':
              const maxMarketCap = await askForConfigInput(bot, chatId, 'Enter new max market cap:', 'maxMarketCap');
              await updateConfig(bot, chatId, { maxMarketCap: parseFloat(maxMarketCap) });
              break;
      
            case 'set_max_liquidity_base':
              const maxLiquidity = await askForConfigInput(bot, chatId, 'Enter new max liquidity:', 'maxLiquidity');
              await updateConfig(bot, chatId, { maxLiquidity: parseFloat(maxLiquidity) });
              break;
      
            case 'toggle_auto_buy_base':
              const newAutoBuyState = !parsedData.state;
              await updateConfig(bot, chatId, { autoBuy: newAutoBuyState });
              break;
      
            case 'toggle_auto_sell_base':
              const newAutoSellState = !parsedData.state;
              await updateConfig(bot, chatId, { autoSell: newAutoSellState });
              break;
      
            case 'toggle_auto_approve_base':
              const newAutoApproveState = !parsedData.state;
              await updateConfig(bot, chatId, { autoApprove: newAutoApproveState });
              break;
      
            case 'back_to_main_snipe_base':
              // Handle navigation to the main snipe menu
              break;
      
            default:
            //   await bot.sendMessage(chatId, 'Unknown command.');
          }
      
          // Acknowledge the callback query
          await bot.answerCallbackQuery(callbackQuery.id);
        } catch (error) {
          console.error('Error handling callback query:', error);
          await bot.sendMessage(chatId, '❌ An error occurred while processing your request.');
        }
      });

  } catch (error) {
    console.error('Failed to show configuration keyboard:', error);
    await bot.sendMessage(chatId, '❌ An error occurred while displaying the configuration keyboard.');
  }
};


