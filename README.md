# EasybotTS - Crypto Trading Bot

A Telegram bot for crypto trading and sniping across multiple blockchains (BSC, Base, Ethereum, Solana).

## Features

- Multi-blockchain support (BSC, Base, Ethereum, Solana)
- Token sniping with configurable parameters
- Wallet monitoring
- Automated trading with take profit and stop loss
- Telegram bot interface
- Worker-based architecture for high performance
- Clustering support for scalability

## Prerequisites

- Node.js 16+
- MongoDB
- Telegram Bot Token

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/EasybotTS.git
cd EasybotTS
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with the following variables:
```
# Telegram Bot Tokens
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
ADMIN_BOT_TOKEN=your_admin_bot_token

# MongoDB
MONGODB_URI=mongodb://localhost:27017/easybot

# Server Configuration
PORT=8000
SOCKET_PORT=3000

# Blockchain Provider URLs
BSC_PROVIDER_URL=https://bsc-dataseed.binance.org/
BASE_PROVIDER_URL=https://mainnet.base.org
ETH_PROVIDER_URL=https://mainnet.infura.io/v3/your_infura_key
SOL_PROVIDER_URL=https://api.mainnet-beta.solana.com

# API URLs for Swaps
BSCNODE_API_URL=https://api.1inch.io/v5.0/56/swap
BASENODE_API_URL=https://api.1inch.io/v5.0/8453/swap
ETHNODE_API_URL=https://api.1inch.io/v5.0/1/swap

# Admin Address
EVM_ADMIN_ADDRESS=your_admin_wallet_address

# API Keys
MASTER_KEY=your_api_key

# Clustering Configuration
ENABLE_CLUSTERING=false
NUM_WORKERS=4

# Logging
LOG_LEVEL=INFO
LOG_TO_FILE=true
LOG_FILE_PATH=logs/app.log

# Redis (Future Implementation)
USE_REDIS=false
```

4. Build the TypeScript code:
```bash
npm run build
```

5. Start the server:
```bash
npm start
```

## Architecture

The application uses a worker-based architecture to handle multiple tasks concurrently:

- **Main Process**: Handles the Telegram bot and API endpoints
- **Worker Pool**: Manages background tasks like token sniping and wallet monitoring
- **Job Queue**: Prioritizes and schedules tasks for execution

For high-load environments, the application supports clustering to utilize multiple CPU cores.

## Scaling

To scale the application for production:

1. Enable clustering by setting `ENABLE_CLUSTERING=true` in the `.env` file
2. Adjust `NUM_WORKERS` based on your server's CPU cores
3. Consider using a process manager like PM2:
```bash
npm install -g pm2
pm2 start dist/server.js --name easybot
```

## Future Enhancements

- Redis integration for distributed caching and job queue
- Additional blockchain support
- Advanced trading strategies
- Web dashboard

## License

MIT
