"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ethSniperScreen = void 0;
const snipe_model_1 = __importDefault(require("../../models/snipe.model"));
const checkliquidity_service_1 = require("../../services/checkliquidity.service");
const snipemonitor_1 = require("../../cronjob/snipemonitor");
// import { startCronJob } from '../../cronjob/index';
const createEthKeyboard = () => ({
    inline_keyboard: [
        [
            { text: '📊 Scan Another Contract', callback_data: JSON.stringify({ command: 'scan_snipe_eth' }) },
            { text: 'Execute Snipe', callback_data: JSON.stringify({ command: 'eth_snipe_execute' }) },
        ],
        [
            { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'eth_snipe_config' }) },
        ],
        [
            {
                text: "* Dismiss message",
                callback_data: JSON.stringify({
                    command: "dismiss_message",
                }),
            },
        ]
    ]
});
const createConfigKeyboard = () => ({
    inline_keyboard: [
        [
            { text: 'Slippage', callback_data: JSON.stringify({ command: 'set_slippage_eth' }) },
            { text: 'Spend', callback_data: JSON.stringify({ command: 'set_spend_eth' }) },
        ],
        [
            { text: 'Time Delay', callback_data: JSON.stringify({ command: 'set_time_delay_eth' }) },
            { text: 'Block Delay', callback_data: JSON.stringify({ command: 'set_block_delay_eth' }) },
        ],
        [
            { text: 'Retries on Fail', callback_data: JSON.stringify({ command: 'set_retries_eth' }) },
        ],
        [
            { text: '⬅️ Back', callback_data: JSON.stringify({ command: 'back_to_main_snipe_eth' }) },
        ]
    ]
});
const promptMessageIdStore = {};
const askForethSniperInput = (bot, chatId, promptMessage, field) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (promptMessageIdStore[chatId]) {
            const ids = promptMessageIdStore[chatId];
            if (ids.promptId) {
                try {
                    yield bot.deleteMessage(chatId, ids.promptId);
                }
                catch (error) {
                    console.error('Failed to delete previous prompt message:', error);
                }
            }
            if (ids.replyId) {
                try {
                    yield bot.deleteMessage(chatId, ids.replyId);
                }
                catch (error) {
                    console.error('Failed to delete previous reply message:', error);
                }
            }
        }
        const sentMessage = yield bot.sendMessage(chatId, promptMessage, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log(`Received ${field}:`, msg.text);
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error(`Invalid ${field}.`));
                }
            }));
        });
    }
    catch (error) {
        console.error(`Failed to ask for ${field}:`, error);
        throw error;
    }
});
const userConfigContext = {};
const ethSniperScreen = (bot, chatId, contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const tokenInfo = yield (0, checkliquidity_service_1.getLiquidityInfoFromDexscreener)(contractAddress);
        const { symbol, name, chainId, dexId, pairAddress, } = tokenInfo;
        const tokenDetails = `
🏷 **Token Information:**
📍 **Symbol:** ${symbol}
📛 **Name:** ${name}
🔗 **Chain ID:** ${chainId}
🏦 **Dex ID:** ${dexId}
🔗 **Pair Address:** ${pairAddress}
    `;
        const options = {
            parse_mode: 'Markdown',
            reply_markup: createEthKeyboard()
        };
        if (!tokenInfo.hasLiquidity) {
            yield bot.sendMessage(chatId, `⚠️ No liquidity found for the token ${contractAddress}`, options);
            yield saveSnipeConfig(chatId, contractAddress, false);
        }
        else {
            const initialMessage = yield bot.sendMessage(chatId, tokenDetails, options);
            userConfigContext[chatId] = Object.assign(Object.assign({}, userConfigContext[chatId]), { initialMessageId: initialMessage.message_id });
        }
        userConfigContext[chatId] = userConfigContext[chatId] || {};
        userConfigContext[chatId][contractAddress] = { isConfigured: true };
        bot.on('callback_query', (callbackQuery) => __awaiter(void 0, void 0, void 0, function* () {
            var _a, _b, _c, _d;
            const messageId = (_a = callbackQuery.message) === null || _a === void 0 ? void 0 : _a.message_id;
            const currentMessageText = ((_b = callbackQuery.message) === null || _b === void 0 ? void 0 : _b.text) || '';
            const currentReplyMarkup = ((_c = callbackQuery.message) === null || _c === void 0 ? void 0 : _c.reply_markup) || {};
            const data = JSON.parse(callbackQuery.data || '{}');
            const command = data.command;
            if (command === 'eth_snipe_config') {
                userConfigContext[chatId] = { contractAddress: contractAddress };
                const newReplyMarkup = createConfigKeyboard();
                if (JSON.stringify(newReplyMarkup) !== JSON.stringify(currentReplyMarkup)) {
                    yield bot.editMessageReplyMarkup(newReplyMarkup, { chat_id: chatId, message_id: messageId });
                }
            }
            else if (command === 'back_to_main_snipe_eth') {
                const newReplyMarkup = createEthKeyboard();
                if (JSON.stringify(newReplyMarkup) !== JSON.stringify(currentReplyMarkup)) {
                    yield bot.editMessageReplyMarkup(newReplyMarkup, { chat_id: chatId, message_id: messageId });
                }
            }
            else if (command === 'eth_snipe_execute') {
                if (tokenInfo.hasLiquidity) {
                    yield bot.answerCallbackQuery(callbackQuery.id, {
                        text: '🚫 Token has already been launched.',
                        show_alert: true
                    });
                }
                else {
                    const snipe = yield snipe_model_1.default.findOne({ chatId, contractAddress, blockchain: 'eth' });
                    if (snipe) {
                        (0, snipemonitor_1.startPeriodicSnipeUpdates)(bot);
                        yield bot.sendMessage(chatId, '🔫 Executing snipe...');
                    }
                    else {
                        yield bot.sendMessage(chatId, '❌ Snipe configuration not found.');
                    }
                }
            }
            else if (command === 'review_config_eth') {
                const configMessageId = (_d = userConfigContext[chatId]) === null || _d === void 0 ? void 0 : _d.initialMessageId;
                if (configMessageId) {
                    yield reviewMessage(bot, chatId, configMessageId, contractAddress);
                }
            }
            else if (command === 'set_slippage_eth') {
                const slippage = yield askForethSniperInput(bot, chatId, 'Enter new slippage percentage:', 'slippage');
                yield updateSnipeConfig(chatId, contractAddress, { slippage_eth: parseInt(slippage, 10) });
                yield bot.sendMessage(chatId, `Slippage set to ${slippage}%`);
            }
            else if (command === 'set_spend_eth') {
                const spend = yield askForethSniperInput(bot, chatId, 'Enter new spend amount:', 'spend');
                yield updateSnipeConfig(chatId, contractAddress, { spend_eth: parseFloat(spend) });
                yield bot.sendMessage(chatId, `Spend amount set to ${spend}`);
            }
            else if (command === 'set_time_delay_eth') {
                const timeDelay = yield askForethSniperInput(bot, chatId, 'Enter new time delay (in milliseconds):', 'time delay');
                yield updateSnipeConfig(chatId, contractAddress, { timeDelay_eth: parseInt(timeDelay, 10) });
                yield bot.sendMessage(chatId, `Time delay set to ${timeDelay} ms`);
            }
            else if (command === 'set_block_delay_eth') {
                const blockDelay = yield askForethSniperInput(bot, chatId, 'Enter new block delay:', 'block delay');
                yield updateSnipeConfig(chatId, contractAddress, { blockDelay_eth: parseInt(blockDelay, 10) });
                yield bot.sendMessage(chatId, `Block delay set to ${blockDelay}`);
            }
            else if (command === 'set_retries_eth') {
                const retries = yield askForethSniperInput(bot, chatId, 'Enter new retry count:', 'retries');
                yield updateSnipeConfig(chatId, contractAddress, { retries_eth: parseInt(retries, 10) });
                yield bot.sendMessage(chatId, `Retries set to ${retries}`);
            }
        }));
    }
    catch (error) {
        console.error('Error in ethSniperScreen:', error);
        yield bot.sendMessage(chatId, '❌ An error occurred during the operation. Please try again.');
    }
});
exports.ethSniperScreen = ethSniperScreen;
const updateSnipeConfig = (chatId, contractAddress, updates) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        let snipe = yield snipe_model_1.default.findOne({ chatId, contractAddress, blockchain: 'eth' });
        if (snipe) {
            updates.isConfigured = true;
            snipe.config = Object.assign(Object.assign({}, snipe.config), updates);
            yield snipe.save();
            console.log('Updated snipe config:', snipe);
        }
        else {
            // await bot.sendMessage(chatId, '❌ Snipe configuration not found.');
        }
    }
    catch (error) {
        console.error('Error updating snipe config:', error);
        // await bot.sendMessage(chatId, '❌ An error occurred while updating the snipe configuration.');
    }
});
const saveSnipeConfig = (chatId, contractAddress, isBought) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Check if a snipe with the given contractAddress already exists
        const existingSnipe = yield snipe_model_1.default.findOne({ contractAddress });
        if (existingSnipe) {
            console.log('Snipe config already exists for this contract address.');
            return;
        }
        // Create a new snipe if it doesn't already exist
        const snipe = new snipe_model_1.default({ chatId, contractAddress, blockchain: 'eth', isBought });
        yield snipe.save();
        console.log('Snipe config saved successfully.');
    }
    catch (error) {
        console.error('Error saving snipe config:', error);
    }
});
const reviewMessage = (bot, chatId, messageId, contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const snipe = yield snipe_model_1.default.findOne({ chatId, contractAddress, blockchain: 'eth' });
        if (snipe) {
            const config = snipe.config;
            const reviewText = `
📊 **Snipe Configuration:**
*Contract:* ${contractAddress}
*Slippage:* ${config.slippage_eth || 'N/A'}%
*Spend:* ${config.spend_eth || 'N/A'}
*Time Delay:* ${config.timeDelay_eth || 'N/A'} ms
*Block Delay:* ${config.blockDelay_eth || 'N/A'}
*Retries:* ${config.retries_eth || 'N/A'}
      `;
            yield bot.editMessageText(reviewText, { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' });
        }
        else {
            yield bot.sendMessage(chatId, '❌ Snipe configuration not found.');
        }
    }
    catch (error) {
        console.error('Error in reviewMessage:', error);
        yield bot.sendMessage(chatId, '❌ An error occurred while reviewing the snipe configuration.');
    }
});
