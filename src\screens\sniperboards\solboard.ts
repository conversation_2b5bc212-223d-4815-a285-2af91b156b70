import TelegramBot, { SendMessageOptions } from 'node-telegram-bot-api';
import Snipe, { ISnipe } from '../../models/snipe.model';
import { getLiquidityInfoFromDexscreener } from '../../services/checkliquidity.service';
import { startPeriodicSnipeUpdates } from '../../cronjob/snipemonitor';
// import { startCronJob } from '../../cronjob/index';

const createSolKeyboard = (): { inline_keyboard: TelegramBot.InlineKeyboardButton[][] } => ({
  inline_keyboard: [
    [
      { text: '📊 Scan Another Contract', callback_data: JSON.stringify({ command: 'scan_snipe_sol' }) },
      { text: 'Execute Snipe', callback_data: JSON.stringify({ command: 'sol_snipe_execute' }) },
    ],
    [
      { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'sol_snipe_config' }) },
    ],
    [
      {
        text: "* Dismiss message",
        callback_data: JSON.stringify({
          command: "dismiss_message",
        }),
      },
    ]
  ]
});

const createConfigKeyboard = (): { inline_keyboard: TelegramBot.InlineKeyboardButton[][] } => ({
  inline_keyboard: [
    [
      { text: 'Slippage', callback_data: JSON.stringify({ command: 'set_slippage_sol' }) },
      { text: 'Spend', callback_data: JSON.stringify({ command: 'set_spend_sol' }) },
    ],
    [
      { text: 'Time Delay', callback_data: JSON.stringify({ command: 'set_time_delay_sol' }) },
      { text: 'Block Delay', callback_data: JSON.stringify({ command: 'set_block_delay_sol' }) },
    ],
    [
      { text: 'Retries on Fail', callback_data: JSON.stringify({ command: 'set_retries_sol' }) },
    ],
    [
      { text: '⬅️ Back', callback_data: JSON.stringify({ command: 'back_to_main_snipe_sol' }) },
    ]
  ]
});

const promptMessageIdStore: { [chatId: number]: { promptId?: number, replyId?: number } } = {};

const askForsolSniperInput = async (bot: TelegramBot, chatId: number, promptMessage: string, field: string): Promise<string> => {
  try {
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    const sentMessage = await bot.sendMessage(chatId, promptMessage, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log(`Received ${field}:`, msg.text);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error(`Invalid ${field}.`));
        }
      });
    });
  } catch (error) {
    console.error(`Failed to ask for ${field}:`, error);
    throw error;
  }
};

const userConfigContext: { [chatId: number]: { [contractAddress: string]: any } } = {};

export const solSniperScreen = async (bot: TelegramBot, chatId: number, contractAddress: string) => {
  try {
    const tokenInfo = await getLiquidityInfoFromDexscreener(contractAddress);

    const {
      symbol,
      name,
      chainId,
      dexId,
      pairAddress,
    } = tokenInfo;

    const tokenDetails = `
🏷 **Token Information:**
📍 **Symbol:** ${symbol}
📛 **Name:** ${name}
🔗 **Chain ID:** ${chainId}
🏦 **Dex ID:** ${dexId}
🔗 **Pair Address:** ${pairAddress}
    `;

    const options: SendMessageOptions = {
      parse_mode: 'Markdown',
      reply_markup: createSolKeyboard()
    };

    if (!tokenInfo.hasLiquidity) {
      await bot.sendMessage(chatId, `⚠️ No liquidity found for the token ${contractAddress}`, options);
      await saveSnipeConfig(chatId, contractAddress, false);
    } else {
      const initialMessage = await bot.sendMessage(chatId, tokenDetails, options);
      userConfigContext[chatId] = { ...userConfigContext[chatId], initialMessageId: initialMessage.message_id };
    }

    userConfigContext[chatId] = userConfigContext[chatId] || {};
    userConfigContext[chatId][contractAddress] = { isConfigured: true };

    bot.on('callback_query', async (callbackQuery) => {
      const messageId = callbackQuery.message?.message_id;
      const currentMessageText = callbackQuery.message?.text || '';
      const currentReplyMarkup = callbackQuery.message?.reply_markup || {};

      const data = JSON.parse(callbackQuery.data || '{}');
      const command = data.command;

      if (command === 'sol_snipe_config') {
        userConfigContext[chatId] = { contractAddress: contractAddress };

        const newReplyMarkup = createConfigKeyboard();

        if (JSON.stringify(newReplyMarkup) !== JSON.stringify(currentReplyMarkup)) {
          await bot.editMessageReplyMarkup(
            newReplyMarkup,
            { chat_id: chatId, message_id: messageId }
          );
        }
      } else if (command === 'back_to_main_snipe_sol') {
        const newReplyMarkup = createSolKeyboard();

        if (JSON.stringify(newReplyMarkup) !== JSON.stringify(currentReplyMarkup)) {
          await bot.editMessageReplyMarkup(
            newReplyMarkup,
            { chat_id: chatId, message_id: messageId }
          );
        }
      } else if (command === 'sol_snipe_execute') {
        if (tokenInfo.hasLiquidity) {
          await bot.answerCallbackQuery(callbackQuery.id, {
            text: '🚫 Token has already been launched.',
            show_alert: true
          });
        } else {
          const snipe = await Snipe.findOne({ chatId, contractAddress, blockchain: 'sol' });

          if (snipe) {
            startPeriodicSnipeUpdates(bot);
            await bot.sendMessage(chatId, '🔫 Executing snipe...');
          } else {
            await bot.sendMessage(chatId, '❌ Snipe configuration not found.');
          }
        }
      } else if (command === 'review_config_sol') {
        const configMessageId = userConfigContext[chatId]?.initialMessageId;
        if (configMessageId) {
          await reviewMessage(bot, chatId, configMessageId, contractAddress);
        }
      } else if (command === 'set_slippage_sol') {
        const slippage = await askForsolSniperInput(bot, chatId, 'Enter new slippage percentage:', 'slippage');
        await updateSnipeConfig(chatId, contractAddress, { slippage_sol: parseInt(slippage, 10) });
        await bot.sendMessage(chatId, `Slippage set to ${slippage}%`);
      } else if (command === 'set_spend_sol') {
        const spend = await askForsolSniperInput(bot, chatId, 'Enter new spend amount:', 'spend');
        await updateSnipeConfig(chatId, contractAddress, { spend_sol: parseFloat(spend) });
        await bot.sendMessage(chatId, `Spend amount set to ${spend}`);
      } else if (command === 'set_time_delay_sol') {
        const timeDelay = await askForsolSniperInput(bot, chatId, 'Enter new time delay (in milliseconds):', 'time delay');
        await updateSnipeConfig(chatId, contractAddress, { timeDelay_sol: parseInt(timeDelay, 10) });
        await bot.sendMessage(chatId, `Time delay set to ${timeDelay} ms`);
      } else if (command === 'set_block_delay_sol') {
        const blockDelay = await askForsolSniperInput(bot, chatId, 'Enter new block delay:', 'block delay');
        await updateSnipeConfig(chatId, contractAddress, { blockDelay_sol: parseInt(blockDelay, 10) });
        await bot.sendMessage(chatId, `Block delay set to ${blockDelay}`);
      } else if (command === 'set_retries_sol') {
        const retries = await askForsolSniperInput(bot, chatId, 'Enter new retry count:', 'retries');
        await updateSnipeConfig(chatId, contractAddress, { retries_sol: parseInt(retries, 10) });
        await bot.sendMessage(chatId, `Retries set to ${retries}`);
      }
    });
  } catch (error) {
    console.error('Error in solSniperScreen:', error);
    await bot.sendMessage(chatId, '❌ An error occurred during the operation. Please try again.');
  }
};

const updateSnipeConfig = async (chatId: number, contractAddress: string, updates: Partial<ISnipe['config']>): Promise<void> => {
  try {
    let snipe = await Snipe.findOne({ chatId, contractAddress, blockchain: 'sol' });

    if (snipe) {
      updates.isConfigured = true;
      snipe.config = { ...snipe.config, ...updates };
      await snipe.save();
      console.log('Updated snipe config:', snipe);
    } else {
      // await bot.sendMessage(chatId, '❌ Snipe configuration not found.');
    }
  } catch (error) {
    console.error('Error updating snipe config:', error);
    // await bot.sendMessage(chatId, '❌ An error occurred while updating the snipe configuration.');
  }
};

/**
 * Save a new SOL snipe configuration
 * @param chatId Chat ID
 * @param contractAddress Contract address
 * @param isBought Whether the token has been bought
 */
const saveSnipeConfig = async (chatId: number, contractAddress: string, isBought: boolean): Promise<void> => {
  try {
    // Check if a snipe with the given contractAddress already exists for this blockchain and chat
    const existingSnipe = await Snipe.findOne({
      chatId,
      contractAddress,
      blockchain: 'sol'
    });

    if (existingSnipe) {
      console.log('SOL snipe config already exists for this contract address.');
      return;
    }

    // Create a new snipe if it doesn't already exist
    const snipe = new Snipe({
      chatId,
      contractAddress,
      blockchain: 'sol',
      isBought
    });

    await snipe.save();
    console.log('SOL snipe config saved successfully.');
  } catch (error) {
    console.error('Error saving SOL snipe config:', error);
  }
};


const reviewMessage = async (bot: TelegramBot, chatId: number, messageId: number, contractAddress: string): Promise<void> => {
  try {
    const snipe = await Snipe.findOne({ chatId, contractAddress, blockchain: 'sol' });
    if (snipe) {
      const config = snipe.config;
      const reviewText = `
📊 **Snipe Configuration:**
*Contract:* ${contractAddress}
*Slippage:* ${config.slippage_sol || 'N/A'}%
*Spend:* ${config.spend_sol || 'N/A'}
*Time Delay:* ${config.timeDelay_sol || 'N/A'} ms
*Block Delay:* ${config.blockDelay_sol || 'N/A'}
*Retries:* ${config.retries_sol || 'N/A'}
      `;
      await bot.editMessageText(reviewText, { chat_id: chatId, message_id: messageId, parse_mode: 'Markdown' });
    } else {
      await bot.sendMessage(chatId, '❌ Snipe configuration not found.');
    }
  } catch (error) {
    console.error('Error in reviewMessage:', error);
    await bot.sendMessage(chatId, '❌ An error occurred while reviewing the snipe configuration.');
  }
};
