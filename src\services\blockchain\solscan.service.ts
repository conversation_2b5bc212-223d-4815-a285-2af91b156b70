/**
 * Solana Scan Service
 * 
 * This service provides functionality for interacting with the Solana blockchain
 * and fetching data from Solscan.
 */

import { Connection, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { TOKEN_PROGRAM_ID, getAccount, getMint } from '@solana/spl-token';
import axios from 'axios';
import { logger } from '../logger/LoggerService';
import { BlockchainType, TraderFactory } from '../trading/TradingInterface';

// Solana configuration
const SOL_RPC_URL = process.env.SOL_PROVIDER_URL || 'https://api.mainnet-beta.solana.com';
const SOLSCAN_API_URL = 'https://api.solscan.io';

// Create connection
const connection = new Connection(SOL_RPC_URL);

/**
 * Get token information from Solscan
 * @param contractAddress Token mint address
 * @returns Token information
 */
export async function getTokenInfo(contractAddress: string) {
  try {
    logger.info(`Getting token info for ${contractAddress} from Solscan`, {
      contractAddress
    });

    // Get token mint info
    const mintPubkey = new PublicKey(contractAddress);
    const mintInfo = await getMint(connection, mintPubkey);

    // Get token metadata (simplified)
    let name = 'Unknown';
    let symbol = 'Unknown';
    
    try {
      // Try to get metadata from Solscan API
      const response = await axios.get(`${SOLSCAN_API_URL}/token/${contractAddress}`);
      if (response.data && response.data.name) {
        name = response.data.name;
        symbol = response.data.symbol || 'Unknown';
      }
    } catch (error) {
      logger.warn(`Could not fetch token metadata from Solscan API for ${contractAddress}`, {
        contractAddress,
        error: (error as Error).message
      });
    }

    // Format supply
    const formattedSupply = (Number(mintInfo.supply) / Math.pow(10, mintInfo.decimals)).toString();

    logger.info(`Token info retrieved for ${contractAddress}`, {
      contractAddress,
      name,
      symbol,
      decimals: mintInfo.decimals,
      supply: formattedSupply
    });

    return {
      name,
      symbol,
      decimals: mintInfo.decimals,
      supply: formattedSupply
    };
  } catch (error) {
    logger.error(`Error getting token info for ${contractAddress}`, {
      contractAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Get token balance for a wallet
 * @param contractAddress Token mint address
 * @param walletAddress Wallet address
 * @returns Token balance
 */
export async function getTokenBalance(contractAddress: string, walletAddress: string) {
  try {
    logger.info(`Getting token balance for ${walletAddress}`, {
      contractAddress,
      walletAddress
    });

    // Get token account
    const mintPubkey = new PublicKey(contractAddress);
    const walletPubkey = new PublicKey(walletAddress);
    
    // Find token account
    const tokenAccounts = await connection.getTokenAccountsByOwner(
      walletPubkey,
      { mint: mintPubkey }
    );
    
    if (tokenAccounts.value.length === 0) {
      logger.info(`No token account found for ${contractAddress} in wallet ${walletAddress}`, {
        contractAddress,
        walletAddress
      });
      return '0';
    }
    
    // Get token account info
    const tokenAccountInfo = await getAccount(
      connection,
      tokenAccounts.value[0].pubkey
    );
    
    // Get mint info for decimals
    const mintInfo = await getMint(connection, mintPubkey);
    
    // Format balance
    const formattedBalance = (Number(tokenAccountInfo.amount) / Math.pow(10, mintInfo.decimals)).toString();

    logger.info(`Token balance retrieved for ${walletAddress}`, {
      contractAddress,
      walletAddress,
      balance: formattedBalance
    });

    return formattedBalance;
  } catch (error) {
    logger.error(`Error getting token balance for ${walletAddress}`, {
      contractAddress,
      walletAddress,
      error: (error as Error).message
    });
    
    // Return 0 on error
    return '0';
  }
}

/**
 * Get SOL balance for a wallet
 * @param walletAddress Wallet address
 * @returns SOL balance
 */
export async function getSolBalance(walletAddress: string) {
  try {
    logger.info(`Getting SOL balance for ${walletAddress}`, {
      walletAddress
    });

    // Get SOL balance
    const walletPubkey = new PublicKey(walletAddress);
    const balance = await connection.getBalance(walletPubkey);
    
    // Format balance
    const formattedBalance = (balance / LAMPORTS_PER_SOL).toString();

    logger.info(`SOL balance retrieved for ${walletAddress}`, {
      walletAddress,
      balance: formattedBalance
    });

    return formattedBalance;
  } catch (error) {
    logger.error(`Error getting SOL balance for ${walletAddress}`, {
      walletAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Check if a token is a honeypot
 * @param contractAddress Token mint address
 * @returns Honeypot check result
 */
export async function checkHoneypot(contractAddress: string) {
  try {
    logger.info(`Checking if ${contractAddress} is a honeypot`, {
      contractAddress
    });

    // Use the trader to check if the token is a honeypot
    const trader = TraderFactory.getTrader(BlockchainType.SOL);
    const result = await trader.checkHoneypot(contractAddress);

    logger.info(`Honeypot check completed for ${contractAddress}`, {
      contractAddress,
      isHoneypot: result.isHoneypot,
      reason: result.honeypotReason
    });

    return result;
  } catch (error) {
    logger.error(`Error checking honeypot for ${contractAddress}`, {
      contractAddress,
      error: (error as Error).message
    });
    
    return {
      isHoneypot: false,
      honeypotReason: `Error checking honeypot: ${(error as Error).message}`
    };
  }
}
