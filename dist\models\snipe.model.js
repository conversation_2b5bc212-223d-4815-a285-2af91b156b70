"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
const SnipeSchema = new mongoose_1.Schema({
    chatId: { type: Number, required: true },
    blockchain: { type: String, required: true },
    contractAddress: { type: String, required: true },
    config: {
        retries_base: { type: Number },
        retries_eth: { type: Number },
        retries_sol: { type: Number },
        retries_bsc: { type: Number },
        slippage_bsc: { type: Number },
        spend_bsc: { type: Number },
        timeDelay_bsc: { type: Number },
        blockDelay_bsc: { type: Number },
        retriesOnFail_bsc: { type: Number },
        slippage_base: { type: Number },
        spend_base: { type: Number },
        timeDelay_base: { type: Number },
        blockDelay_base: { type: Number },
        retriesOnFail_base: { type: Number },
        slippage_sol: { type: Number },
        spend_sol: { type: Number },
        timeDelay_sol: { type: Number },
        blockDelay_sol: { type: Number },
        retriesOnFail_sol: { type: Number },
        slippage_eth: { type: Number },
        spend_eth: { type: Number },
        timeDelay_eth: { type: Number },
        blockDelay_eth: { type: Number },
        retriesOnFail_eth: { type: Number },
        isConfigured: { type: Boolean, default: false },
        isBought: { type: Boolean, default: false },
        isExecuted_bsc: { type: Boolean, default: false },
        isExecuted_base: { type: Boolean, default: false },
        isExecuted_sol: { type: Boolean, default: false },
        isExecuted_eth: { type: Boolean, default: false },
    },
    hasLiquidity: { type: Boolean, default: false },
    lastPriceUpdate: { type: Date },
});
const Snipe = mongoose_1.default.model('Snipe', SnipeSchema);
exports.default = Snipe;
