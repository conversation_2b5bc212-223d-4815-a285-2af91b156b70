"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleBaseDashboard = exports.getBaseBalance = void 0;
const ethers_1 = require("ethers");
const user_models_1 = __importDefault(require("../../models/user.models"));
const getBaseBalance = (address) => __awaiter(void 0, void 0, void 0, function* () {
    const providerUrl = process.env.BASE_PROVIDER_URL;
    if (!providerUrl) {
        throw new Error('Provider is not defined in the .env file');
    }
    const providerbase = new ethers_1.ethers.providers.JsonRpcProvider(providerUrl);
    const balanceBASE = yield providerbase.getBalance(address);
    return parseFloat(ethers_1.ethers.utils.formatEther(balanceBASE)).toFixed(6);
});
exports.getBaseBalance = getBaseBalance;
const handleBaseDashboard = (bot, chat_id) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const user = yield user_models_1.default.findOne({ chat_id });
    if (!user) {
        bot.sendMessage(chat_id, 'User not found.');
        return;
    }
    const firstName = user.first_name || 'User';
    const baseWallet = ((_a = user.base_wallet) === null || _a === void 0 ? void 0 : _a.address) || 'Not set';
    const balance = baseWallet !== 'Not set' ? yield (0, exports.getBaseBalance)(baseWallet) : '0.0 BNB';
    const welcomeMessage = `👋 Welcome, ${firstName}, to your BASE Dashboard!
\n📍 **Address:** \`${baseWallet}\`
\n🔗 **Blockchain:** base
\n💰 **Balance:** \`${balance} BASE\`
\n⚙️ **Settings:** [Antirug, etc.]`;
    const options = {
        parse_mode: 'Markdown',
        reply_markup: {
            inline_keyboard: [
                [
                    { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_base' }) }
                ],
                [
                    { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
                    { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_base' }) }
                ],
                [
                    { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_base' }) },
                    { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_base_wallet' }) }
                ],
                [
                    {
                        text: "* Dismiss message",
                        callback_data: JSON.stringify({
                            command: "dismiss_message",
                        }),
                    },
                ]
            ]
        }
    };
    const sentMessage = yield bot.sendMessage(chat_id, welcomeMessage, options);
    yield user_models_1.default.updateOne({ chat_id: chat_id }, {
        $set: {
            'base_dashboard_message_id': sentMessage.message_id,
            'base_dashboard_content': welcomeMessage,
            'base_dashboard_markup': options.reply_markup
        }
    });
});
exports.handleBaseDashboard = handleBaseDashboard;
