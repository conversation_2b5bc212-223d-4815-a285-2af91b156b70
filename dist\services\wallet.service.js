"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateBASEWallet = exports.generateSOLWallet = exports.generateBSCWallet = exports.generateETHWallet = void 0;
const web3_js_1 = require("@solana/web3.js");
const ethers_1 = require("ethers");
const bs58_1 = __importDefault(require("bs58"));
const generateETHWallet = () => {
    const wallet = ethers_1.ethers.Wallet.createRandom();
    return {
        blockchain: 'eth',
        network: 'mainnet',
        address: wallet.address,
        privateKey: wallet.privateKey,
        rpcUrl: 'https://mainnet.infura.io/v3/********************************',
        mnemonic: wallet.mnemonic.phrase,
    };
};
exports.generateETHWallet = generateETHWallet;
const generateBSCWallet = () => {
    const wallet = ethers_1.ethers.Wallet.createRandom();
    return {
        blockchain: 'bsc',
        network: 'mainnet',
        address: wallet.address,
        privateKey: wallet.privateKey,
        rpcUrl: 'https://bsc-dataseed.binance.org/',
        mnemonic: wallet.mnemonic.phrase,
    };
};
exports.generateBSCWallet = generateBSCWallet;
const generateSOLWallet = () => {
    const keyPair = web3_js_1.Keypair.generate();
    return {
        blockchain: 'sol',
        network: 'mainnet',
        address: keyPair.publicKey.toString(),
        privateKey: bs58_1.default.encode(keyPair.secretKey),
        rpcUrl: 'https://api.mainnet-beta.solana.com',
    };
};
exports.generateSOLWallet = generateSOLWallet;
const generateBASEWallet = () => {
    try {
        const wallet = ethers_1.ethers.Wallet.createRandom();
        return {
            blockchain: 'base',
            network: 'mainnet',
            address: wallet.address,
            privateKey: wallet.privateKey,
            rpcUrl: 'https://mainnet.base.org/',
            mnemonic: wallet.mnemonic.phrase,
        };
    }
    catch (error) {
        throw new Error('Failed to generate BASE wallet. Consult BASE documentation.');
    }
};
exports.generateBASEWallet = generateBASEWallet;
