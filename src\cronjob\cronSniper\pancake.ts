import { providers, Wallet, ethers } from 'ethers';
import Snipe from '../../models/snipe.model'; // Adjust the path as needed
import User from '../../models/user.models'; // Adjust the path as needed
import Trade from '../../models/trade.model'; // Adjust the path as needed
import axios from 'axios';
import dotenv from 'dotenv';
dotenv.config();


const adminAddress: string = process.env.EVM_ADMIN_ADDRESS || '';
const providerUrl = process.env.BSC_PROVIDER_URL;

if (!adminAddress) {
    throw new Error('ADMIN_ADDRESS environment variable is not set');
}

if (!providerUrl) {
    throw new Error('bsc_PROVIDER_URL environment variable is not set');
}

const apiKey = process.env.MASTER_KEY || '';

const headers = {
  '0x-api-key': apiKey,
};

async function getSwapQuote(url: string) {
    const response = await axios.get(url,{ headers });
    return response.data;
}

export async function buyBscTokens(chatId: number, contractAddress: string): Promise<providers.TransactionReceipt> {
    // console.log("Starting token purchase on bscSwap");

    try {
        // Fetch user and Snipe configuration
        const user = await User.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }

        const snipe = await Snipe.findOne({
            chatId: chatId,
            contractAddress: contractAddress,
            blockchain: 'bsc'
        }).exec();

        if (!snipe) {
            throw new Error('Snipe configuration not found');
        }

        const { private_key, address } = user.bsc_wallet;
        const bscCurrentTokenName = user.bscCurrentTokenName;

        const spendbsc = snipe.config.spend_bsc;
        // console.log(spendbsc)
        const slippagebsc = snipe.config.slippage_bsc;

        if (spendbsc === undefined || spendbsc === null || isNaN(spendbsc)) {
            throw new Error('Invalid spend_bsc value');
        }

        if (slippagebsc === undefined || slippagebsc === null || isNaN(slippagebsc)) {
            throw new Error('Invalid slippage_bsc value');
        }

        const buyToken = contractAddress;
        // console.log("Buying", buyToken);

        const provider = new providers.JsonRpcProvider(providerUrl);
        const wallet = new Wallet(private_key, provider);

        const spendbscWei = ethers.utils.parseEther(spendbsc.toString());
        // console.log(`Spend Amount (in Wei): ${spendbscWei.toString()}`);

        const sellToken = '******************************************';
        const feeRecipient = adminAddress;
        const buyTokenPercentageFee = 0.8;

        // Convert slippage percentage
        const slippagePercentage = slippagebsc / 100;

        let queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${spendbscWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;

        const bscApiUrl = process.env.BSCNODE_API_URL;
        const url = `${bscApiUrl}?${queryParams}`;

        const quote = await getSwapQuote(url);

        const buyTx = {
            to: quote.to,
            data: quote.data,
            value: ethers.BigNumber.from(quote.value),
            gasLimit: ethers.BigNumber.from(1000000),
            gasPrice: ethers.utils.parseUnits('1.5', 'gwei'),
        };

        // console.log('Signing and sending the buy transaction...');
        const buyTxResponse = await wallet.sendTransaction(buyTx);

        // console.log(`Buy Transaction Hash: ${buyTxResponse.hash}`);

        const receipt = await buyTxResponse.wait();

        // console.log(`Buy Transaction Mined! Block Number: ${receipt.blockNumber}`);

        const existingTrade = await Trade.findOne({
            userId: user._id,
            contractAddress: buyToken,
            isSold: false,
        }).exec();

        if (existingTrade) {
            existingTrade.buyAmount += parseFloat(spendbsc.toString());
            await existingTrade.save();
            // console.log('Updated existing trade with new buy amount');
        } else {
            const newTrade = new Trade({
                userId: user._id,
                chatId: chatId,
                walletId: user.bsc_wallet.address,
                contractAddress: buyToken,
                contractName: bscCurrentTokenName,
                buyAmount: parseFloat(spendbsc.toString()),
                sellAmount: 0,
                takeProfit: 0,
                stopLoss: 0,
                isSold: false,
                blockchain: 'bsc'
            });

            await newTrade.save();
            // console.log('Created new trade entry');
        }

        await User.updateOne(
            { _id: user._id },
            { $push: { [`trades.${'bsc'}`]: existingTrade ? existingTrade._id : user._id } }
        );

        // console.log('User trades updated successfully');

        return receipt;
    } catch (error) {
        console.error('Error purchasing tokens:', error);
        throw error;
    }
}
