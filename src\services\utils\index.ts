/**
 * Utility Services Module
 * 
 * This module exports all utility services used in the application.
 */

// Export utility services
export * from '../wallet.service';
export * from '../sessions.service';
export * from '../user.service';

/**
 * Initialize all utility services
 */
export function initializeUtilityServices(): void {
  // Initialize utility services
  const { logger } = require('../logger/LoggerService');
  logger.info('Initializing utility services...');
  
  // Log initialization complete
  logger.info('All utility services initialized successfully');
}
