(()=>{"use strict";var e={1888:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.fetchBalancesForAllChains=void 0;const a=n(9321);s(n(818)).default.config();const o={eth:new a.ethers.providers.JsonRpcProvider(process.env.ETH_PROVIDER_URL),bsc:new a.ethers.providers.JsonRpcProvider(process.env.BSC_PROVIDER_URL),base:new a.ethers.providers.JsonRpcProvider(process.env.BASE_PROVIDER_URL)},i={eth:process.env.ETH_PRIVATE_KEY||"4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b",bsc:process.env.BSC_PRIVATE_KEY||"4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b",base:process.env.BASE_PRIVATE_KEY||"4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b"},d={eth:new a.ethers.Wallet(i.eth,o.eth),bsc:new a.ethers.Wallet(i.bsc,o.bsc),base:new a.ethers.Wallet(i.base,o.base)},c=[process.env.TOKEN_ADDRESS_1||"",process.env.TOKEN_ADDRESS_2||"",process.env.TOKEN_ADDRESS_3||""],l=["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)","function symbol() view returns (string)"],u=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=new a.ethers.Contract(t,l,e),[r,s,o]=yield Promise.all([n.balanceOf(e.address),n.decimals(),n.symbol()]);return{tokenAddress:t,balance:r,symbol:o,decimals:s}}catch(n){return console.error(`Error fetching balance for token ${t} on ${e}:`,n),null}}));t.fetchBalancesForAllChains=()=>r(void 0,void 0,void 0,(function*(){for(const[e,t]of Object.entries(d)){console.log(`\n${e.toUpperCase()} Wallet: ${t.address}`);for(const e of c){const n=yield u(t,e);if(n){const{balance:e,decimals:t,symbol:r}=n;console.log(`Token: ${r}, Balance: ${a.ethers.utils.formatUnits(e,t)}`)}}}})),(0,t.fetchBalancesForAllChains)().catch(console.error)},8014:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SET_SOL_SPEND=t.SET_ETH_SPEND=t.SET_BASE_SPEND=t.SET_BSC_SPEND=t.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_SOL_CONTRACT_ADDRESS=t.INPUT_ETH_CONTRACT_ADDRESS=t.INPUT_BASE_CONTRACT_ADDRESS=t.INPUT_CONTRACT_ADDRESS=void 0,t.INPUT_CONTRACT_ADDRESS="Please submit the BSC contract address you want to scan.",t.INPUT_BASE_CONTRACT_ADDRESS="Please submit the BASE contract address you want to scan.",t.INPUT_ETH_CONTRACT_ADDRESS="Please submit the ETH contract address you want to scan.",t.INPUT_SOL_CONTRACT_ADDRESS="Please submit the SOL contract address you want to scan.",t.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE="Please enter the BSC token you want to snipe",t.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE="Please enter the BASE token you want to snipe",t.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE="Please enter the ETH token you want to snipe",t.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE="Please enter the SOL token you want to snipe",t.SET_BSC_SPEND="Input how much BSC to spend",t.SET_BASE_SPEND="Input how much BASE to spend",t.SET_ETH_SPEND="Input how much ETH to spend",t.SET_SOL_SPEND="Input how much SOL to spend"},3433:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.initializeSocketServer=t.startPeriodicSnipeUpdates=t.updateTokens=void 0;const a=s(n(8611)),o=n(4437),i=s(n(8087)),d=n(3653),c=n(871),l=n(1213),u=n(8904),g=n(8708),h=s(n(857)),y=Math.max(1,h.default.cpus().length-1),p=new d.WorkerManager("./dist/workers/worker.js",{workerCount:y,concurrency:10,enableRecycling:!0,enableHealthCheck:!0,enableMetrics:!0}),f=a.default.createServer(),m=new o.Server(f,{cors:{origin:"*",methods:["GET","POST"]}}),_=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{yield e.sendMessage(t,n,s),l.logger.debug(`Message sent to chat ID ${t}`,{chatId:t,messageLength:n.length})}catch(e){l.logger.error(`Failed to send message to chat ID ${t}`,{error:e.message,chatId:t})}}));function b(){return r(this,void 0,void 0,(function*(){l.logger.info("Starting graceful shutdown");try{f.close((()=>{l.logger.info("HTTP server closed")})),yield p.shutdown(3e4),l.logger.info("Graceful shutdown completed"),process.exit(0)}catch(e){l.logger.error("Error during graceful shutdown",{error:e.message}),process.exit(1)}}))}p.registerHandler("CHECK_LIQUIDITY",(e=>r(void 0,void 0,void 0,(function*(){const{contractAddress:t,blockchain:n}=e.data,r=`liquidity:${n}:${t}`;try{const e=yield g.cacheService.get(r);if(e)return l.logger.debug(`Using cached liquidity info for ${t}`,{contractAddress:t,blockchain:n}),e;const s=yield(0,u.getLiquidityInfoFromDexscreener)(t);return yield g.cacheService.set(r,s,60),s}catch(e){throw l.logger.error(`Error checking liquidity for ${t}`,{error:e.message,contractAddress:t,blockchain:n}),e}})))),p.registerHandler("EXECUTE_SNIPE",(e=>r(void 0,void 0,void 0,(function*(){var t,n;const{snipe:r,bot:s}=e.data,{chatId:a,contractAddress:o,blockchain:d}=r;try{let u;l.logger.info(`Executing snipe for ${o} on ${d}`,{chatId:a,contractAddress:o,blockchain:d,jobId:e.id});try{if(!c.TraderFactory.hasTrader(d))throw new Error(`No trader registered for blockchain: ${d}`);{const e=c.TraderFactory.getTrader(d),n=r.config,s=`spend_${d}`,a=`slippage_${d}`,i=(null===(t=n[s])||void 0===t?void 0:t.toString())||"0.1",l=Number(n[a])||5;if(parseFloat(i)<=0)throw new Error(`Invalid spend amount: ${i}`);if(u=yield e.buyTokens(o,i,"",{slippage:l}),!u.success)throw new Error(u.error||"Transaction failed")}const e=`isExecuted_${d}`;yield i.default.updateOne({_id:r._id},{$set:{[`config.${String(e)}`]:!0,"config.isBought":!0}});const g=(null===(n=r.config[`spend_${d}`])||void 0===n?void 0:n.toString())||"0.1";return yield _(s,a,`✅ Successfully sniped ${o} on ${d.toUpperCase()}!\n\n🔗 Transaction Hash: ${u.transactionHash}\n💰 Amount Spent: ${g} ${d.toUpperCase()}`),m.emit("tokenUpdate",{tokenAddress:o,tokenInfo:{hasLiquidity:!0,message:"Liquidity detected and token purchased.",transactionHash:u.transactionHash}}),l.logger.info(`Successfully sniped token ${o} on ${d}`,{chatId:a,contractAddress:o,blockchain:d,transactionHash:u.transactionHash}),{success:!0,transactionHash:u.transactionHash}}catch(e){l.logger.error(`Error executing snipe for ${o}`,{error:e.message,chatId:a,contractAddress:o,blockchain:d});const t=`retries_${d}`,n=r.config[t]||0;throw yield i.default.updateOne({_id:r._id},{$set:{[`config.${String(t)}`]:n+1}}),yield _(s,a,`❌ Error sniping ${o} on ${d.toUpperCase()}: ${e.message}`),e}}catch(e){return l.logger.error(`Failed to execute snipe for ${o}`,{error:e.message,chatId:a,contractAddress:o,blockchain:d}),{success:!1,error:e.message}}})))),t.updateTokens=e=>r(void 0,void 0,void 0,(function*(){const t=Date.now();l.logger.info("Started checking tokens for liquidity");try{const n=yield i.default.find({"config.isConfigured":!0,"config.isBought":!1,$or:[{"config.isExecuted_bsc":!1},{"config.isExecuted_sol":!1},{"config.isExecuted_eth":!1},{"config.isExecuted_base":!1}]}).lean();if(0===n.length)return void l.logger.debug("No snipes to process");l.logger.info(`Found ${n.length} snipes to process`);const s=n.reduce(((e,t)=>{const n=t.blockchain;return e[n]||(e[n]=[]),e[n].push(t),e}),{});for(const[t,n]of Object.entries(s)){l.logger.info(`Processing ${n.length} snipes for ${t}`);const s=[...new Set(n.map((e=>e.contractAddress)))];for(let a=0;a<s.length;a+=20){const o=s.slice(a,a+20);l.logger.debug(`Processing batch ${Math.floor(a/20)+1} of ${Math.ceil(s.length/20)} for ${t}`);const d=o.map((e=>p.addJob("CHECK_LIQUIDITY",{contractAddress:e,blockchain:t},{priority:5}))),c=yield Promise.all(d);yield new Promise((e=>setTimeout(e,1e3)));const u=yield Promise.all(c.map((e=>r(void 0,void 0,void 0,(function*(){const t=p.getJob(e);return"completed"===(null==t?void 0:t.status)&&t.result?{address:o[c.indexOf(e)],result:t.result}:null})))).filter(Boolean));for(const r of u){if(!r)continue;const{address:s,result:a}=r,o=n.filter((e=>e.contractAddress===s));for(const n of o){yield i.default.updateOne({_id:n._id},{$set:{hasLiquidity:a.hasLiquidity,lastPriceUpdate:new Date}});const r=`isExecuted_${t}`;if(a.hasLiquidity&&!n.config[r]){l.logger.info(`Liquidity found for token ${s} on ${t}, adding to execution queue`);const r="base"===t?10:"bsc"===t?8:"eth"===t?6:4;yield p.addJob("EXECUTE_SNIPE",{snipe:n,bot:e},{priority:r,maxAttempts:3,tags:[t,"snipe",s]}),yield _(e,n.chatId,`🔍 Liquidity detected for ${s} on ${t.toUpperCase()}! Attempting to snipe...`),m.emit("liquidityFound",{tokenAddress:s,blockchain:t,timestamp:(new Date).toISOString()})}else a.hasLiquidity||l.logger.debug(`No liquidity found for token ${s} on ${t}`)}}a+20<s.length&&(yield new Promise((e=>setTimeout(e,2e3))))}}const a=Date.now()-t;l.logger.info(`Completed token update in ${a}ms`,{snipeCount:n.length,durationMs:a})}catch(e){l.logger.error("Error updating tokens",{error:e.message,stack:e.stack})}})),t.startPeriodicSnipeUpdates=e=>(l.logger.info("Starting periodic snipe updates (every 30 seconds)"),setInterval((()=>(0,t.updateTokens)(e)),3e4)),t.initializeSocketServer=(e,n)=>{m.on("connection",(e=>{l.logger.info("New client connected",{socketId:e.id}),v(e),e.on("disconnect",(()=>{l.logger.info("Client disconnected",{socketId:e.id})})),e.on("execute_snipe",(t=>r(void 0,void 0,void 0,(function*(){try{const r=yield i.default.findById(t.snipeId);if(!r)return void e.emit("error",{message:"Snipe not found"});const s=p.addJob("EXECUTE_SNIPE",{snipe:r,bot:n},{priority:10,maxAttempts:3,tags:[r.blockchain,"manual_snipe",r.contractAddress]});e.emit("snipe_queued",{snipeId:t.snipeId,jobId:s,message:"Snipe has been queued for execution"}),l.logger.info(`Manual snipe queued for ${r.contractAddress}`,{snipeId:t.snipeId,jobId:s,blockchain:r.blockchain})}catch(n){l.logger.error("Error executing manual snipe",{error:n.message,snipeId:t.snipeId}),e.emit("error",{message:n.message})}})))),e.on("health_check",(()=>r(void 0,void 0,void 0,(function*(){try{const t={status:"healthy",workerCount:y,activeWorkers:p.getHealth().activeWorkers,queueLength:p.getMetrics().queueLength,uptime:process.uptime(),timestamp:(new Date).toISOString()};e.emit("health_status",t)}catch(t){l.logger.error("Error sending health status",{error:t.message}),e.emit("error",{message:t.message})}}))))})),f.listen(e,(()=>{l.logger.info(`Socket.IO server is running on port ${e}`)})),(0,t.startPeriodicSnipeUpdates)(n),process.on("SIGTERM",(()=>r(void 0,void 0,void 0,(function*(){l.logger.info("SIGTERM received, shutting down gracefully"),yield b()})))),process.on("SIGINT",(()=>r(void 0,void 0,void 0,(function*(){l.logger.info("SIGINT received, shutting down gracefully"),yield b()}))))};const v=e=>r(void 0,void 0,void 0,(function*(){try{const t=yield i.default.find({"config.isConfigured":!0,"config.isBought":!1}).lean();e.emit("snipe_data",{snipes:t}),l.logger.debug("Sent snipe data to client",{socketId:e.id,snipeCount:t.length})}catch(t){l.logger.error("Error sending snipe data",{error:t.message,socketId:e.id})}}))},4736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.clearAllContractAddresses=t.getAllCurrentContractAddresses=t.setCurrentContractAddress=t.getCurrentContractAddress=void 0;const r=n(871),s={currentContractAddresses:{[r.BlockchainType.BSC]:null,[r.BlockchainType.ETH]:null,[r.BlockchainType.SOL]:null,[r.BlockchainType.BASE]:null}};t.getCurrentContractAddress=e=>s.currentContractAddresses[e]||null,t.setCurrentContractAddress=(e,t)=>{s.currentContractAddresses[e]=t},t.getAllCurrentContractAddresses=()=>Object.assign({},s.currentContractAddresses),t.clearAllContractAddresses=()=>{Object.keys(s.currentContractAddresses).forEach((e=>{s.currentContractAddresses[e]=null}))},t.default=s},4103:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return s(t,e),t},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.updateAutoTradeAsSold=function(e,t,n,r){return o(this,void 0,void 0,(function*(){yield c.findOneAndUpdate({contractAddress:t},{isSold:!0}).exec()}))};const i=a(n(6037)),d=new i.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String},contractName:{type:String,default:""},isSold:{type:Boolean,default:!1},isInitialized:{type:String,enum:["pending","ongoing"],default:"pending"},config:{takeProfit:{type:Number,default:0},stopLoss:{type:Number,default:0},spendAmount:{type:Number,default:0}}}),c=i.default.model("AutoTrade",d);t.default=c},6131:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.UserSchema=void 0;const s=r(n(8722));t.UserSchema=s.default},8087:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const o=a(n(6037)),i=new o.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String,required:!0},config:{retries_base:{type:Number},retries_eth:{type:Number},retries_sol:{type:Number},retries_bsc:{type:Number},slippage_bsc:{type:Number},spend_bsc:{type:Number},timeDelay_bsc:{type:Number},blockDelay_bsc:{type:Number},retriesOnFail_bsc:{type:Number},slippage_base:{type:Number},spend_base:{type:Number},timeDelay_base:{type:Number},blockDelay_base:{type:Number},retriesOnFail_base:{type:Number},slippage_sol:{type:Number},spend_sol:{type:Number},timeDelay_sol:{type:Number},blockDelay_sol:{type:Number},retriesOnFail_sol:{type:Number},slippage_eth:{type:Number},spend_eth:{type:Number},timeDelay_eth:{type:Number},blockDelay_eth:{type:Number},retriesOnFail_eth:{type:Number},isConfigured:{type:Boolean,default:!1},isBought:{type:Boolean,default:!1},isExecuted_bsc:{type:Boolean,default:!1},isExecuted_base:{type:Boolean,default:!1},isExecuted_sol:{type:Boolean,default:!1},isExecuted_eth:{type:Boolean,default:!1}},hasLiquidity:{type:Boolean,default:!1},lastPriceUpdate:{type:Date}}),d=o.default.model("Snipe",i);t.default=d},6786:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return s(t,e),t},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.updateTradeAsSold=function(e,t,n,r){return o(this,void 0,void 0,(function*(){yield c.findOneAndUpdate({contractAddress:t},{isSold:!0}).exec()}))};const i=a(n(6037)),d=new i.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String},contractName:{type:String,default:""},buyAmount:{type:Number,default:0},sellAmount:{type:Number,default:0},isSold:{type:Boolean,default:!1},config:{takeProfit:{type:Number,default:0},stopLoss:{type:Number,default:0},spendAmount:{type:Number,default:0}}}),c=i.default.model("Trade",d);t.default=c},8722:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const o=a(n(6037)),i=new o.Schema({chat_id:{type:Number,required:!0},first_name:{type:String,required:!0},last_name:{type:String},username:{type:String},sol_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},bsc_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},base_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},eth_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},preset_setting:{type:[Number],default:[.01,1,5,10]},nonce:{type:Number,default:0},retired:{type:Boolean,default:!1},referrer_code:{type:String,default:null},referrer_wallet:{type:String,default:null},referral_code:{type:String,default:null},referral_date:{type:String,default:null},schedule:{type:String,default:"60"},burn_fee:{type:Boolean,default:!1},auto_buy:{type:Boolean,default:!1},auto_buy_amount:{type:String,default:"0"},auto_sell_amount:{type:String,default:"0"},trades:{bsc:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}],sol:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}],eth:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}],base:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}]},currentBlockchain:{type:String,enum:["bsc","sol","eth","base"],default:"bsc"},sol_config:{slippage:{type:o.Schema.Types.Decimal128,default:.6},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},bsc_config:{slippage:{type:o.Schema.Types.Decimal128,default:.1},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},base_config:{slippage:{type:o.Schema.Types.Decimal128,default:.1},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},eth_config:{slippage:{type:o.Schema.Types.Decimal128,default:.2},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},bsc_dashboard_message_id:{type:Number},bsc_dashboard_content:{type:String},bsc_dashboard_markup:{type:o.Schema.Types.Mixed},eth_dashboard_message_id:{type:Number},eth_dashboard_content:{type:String},eth_dashboard_markup:{type:o.Schema.Types.Mixed},sol_dashboard_message_id:{type:Number},sol_dashboard_content:{type:String},sol_dashboard_markup:{type:o.Schema.Types.Mixed},base_dashboard_message_id:{type:Number},base_dashboard_content:{type:String},base_dashboard_markup:{type:o.Schema.Types.Mixed},bscCurrentContractAddress:{type:String},solCurrentContractAddress:{type:String},baseCurrentContractAddress:{type:String},ethCurrentContractAddress:{type:String},bscCurrentTokenName:{type:String},solCurrentTokenName:{type:String},baseCurrentTokenName:{type:String},ethCurrentTokenName:{type:String},snipes:[{type:o.Schema.Types.ObjectId,ref:"Snipe",default:[]}]}),d=o.default.model("User",i);t.default=d},566:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.showConfigKeyboard=void 0;const a=s(n(6261)),o=s(n(8722)),i=new a.default,d=e=>({inline_keyboard:[[{text:`Slippage: ${e.slippage}`,callback_data:JSON.stringify({command:"set_slippage_base"})},{text:`Max Gas Price: ${e.maxGasPrice}`,callback_data:JSON.stringify({command:"set_max_gas_price_base"})}],[{text:`Max Gas Limit: ${e.maxGasLimit}`,callback_data:JSON.stringify({command:"set_max_gas_limit_base"})},{text:`Min Liquidity: ${e.minLiquidity}`,callback_data:JSON.stringify({command:"set_min_liquidity_base"})}],[{text:`Max Market Cap: ${e.maxMarketCap}`,callback_data:JSON.stringify({command:"set_max_market_cap_base"})},{text:`Max Liquidity: ${e.maxLiquidity}`,callback_data:JSON.stringify({command:"set_max_liquidity_base"})}],[{text:"Auto Buy: "+(e.autoBuy?"✅":"❌"),callback_data:JSON.stringify({command:"toggle_auto_buy_base",state:e.autoBuy})},{text:"Auto Sell: "+(e.autoSell?"✅":"❌"),callback_data:JSON.stringify({command:"toggle_auto_sell_base",state:e.autoSell})}],[{text:"Auto Approve: "+(e.autoApprove?"✅":"❌"),callback_data:JSON.stringify({command:"toggle_auto_approve_base",state:e.autoApprove})},{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_base"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const s=yield o.default.findOne({chat_id:t}).exec();if(!s)throw new Error("User not found.");const a=`${s.currentBlockchain}_config`,d=Object.assign(Object.assign({},s[a]),n),c={[a]:d};yield o.default.updateOne({chat_id:t},{$set:c}).exec(),i.emit("configUpdated",t,d,e);const l=yield e.sendMessage(t,"✅ Configuration updated successfully.");setTimeout((()=>r(void 0,void 0,void 0,(function*(){try{yield e.deleteMessage(t,l.message_id)}catch(e){console.error("Failed to delete success message:",e)}}))),5e3)}catch(n){console.error("Error updating configuration:",n),yield e.sendMessage(t,"❌ An error occurred while updating your wallet configurations.")}}));i.on("configUpdated",((e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chat_id:e}).exec();if(!r)throw new Error("User not found.");const s=d(t),a=r[`${r.currentBlockchain}_dashboard_message_id`];yield n.editMessageReplyMarkup(s,{chat_id:e,message_id:a})}catch(e){console.error("Error updating the configuration keyboard:",e)}}))));const l={},u=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(l[t]){const n=l[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return l[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${s}:`,e.text),l[t]=Object.assign(Object.assign({},l[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}}));t.showConfigKeyboard=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield o.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found.");const s=n[`${n.currentBlockchain}_config`],a=d(s),i=yield e.sendMessage(t,"Please select the configuration option to edit:",{reply_markup:a});n[`${n.currentBlockchain}_dashboard_message_id`]=i.message_id,yield n.save(),e.on("callback_query",(t=>r(void 0,void 0,void 0,(function*(){const{message:n,data:r}=t,s=null==n?void 0:n.chat.id;if(null==n||n.message_id,!s||!r)return;const a=JSON.parse(r),o=a.command;try{switch(o){case"set_slippage_base":const t=yield u(e,s,"Enter new slippage percentage:","slippage");yield c(e,s,{slippage:parseFloat(t)});break;case"set_max_gas_price_base":const n=yield u(e,s,"Enter new max gas price:","maxGasPrice");yield c(e,s,{maxGasPrice:parseFloat(n)});break;case"set_max_gas_limit_base":const r=yield u(e,s,"Enter new max gas limit:","maxGasLimit");yield c(e,s,{maxGasLimit:parseFloat(r)});break;case"set_min_liquidity_base":const o=yield u(e,s,"Enter new min liquidity:","minLiquidity");yield c(e,s,{minLiquidity:parseFloat(o)});break;case"set_max_market_cap_base":const i=yield u(e,s,"Enter new max market cap:","maxMarketCap");yield c(e,s,{maxMarketCap:parseFloat(i)});break;case"set_max_liquidity_base":const d=yield u(e,s,"Enter new max liquidity:","maxLiquidity");yield c(e,s,{maxLiquidity:parseFloat(d)});break;case"toggle_auto_buy_base":const l=!a.state;yield c(e,s,{autoBuy:l});break;case"toggle_auto_sell_base":const g=!a.state;yield c(e,s,{autoSell:g});break;case"toggle_auto_approve_base":const h=!a.state;yield c(e,s,{autoApprove:h})}yield e.answerCallbackQuery(t.id)}catch(t){console.error("Error handling callback query:",t),yield e.sendMessage(s,"❌ An error occurred while processing your request.")}}))))}catch(n){console.error("Failed to show configuration keyboard:",n),yield e.sendMessage(t,"❌ An error occurred while displaying the configuration keyboard.")}}))},1347:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBaseDashboard=t.getBaseBalance=void 0;const a=n(9321),o=s(n(8722));t.getBaseBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.BASE_PROVIDER_URL;if(!t)throw new Error("Provider is not defined in the .env file");const n=new a.ethers.providers.JsonRpcProvider(t),r=yield n.getBalance(e);return parseFloat(a.ethers.utils.formatEther(r)).toFixed(6)})),t.handleBaseDashboard=(e,n)=>r(void 0,void 0,void 0,(function*(){var r;const s=yield o.default.findOne({chat_id:n});if(!s)return void e.sendMessage(n,"User not found.");const a=s.first_name||"User",i=(null===(r=s.base_wallet)||void 0===r?void 0:r.address)||"Not set",d=`👋 Welcome, ${a}, to your BASE Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** base\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,t.getBaseBalance)(i):"0.0 BNB"} BASE\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_base"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_base"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_base"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_base_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(n,d,c);yield o.default.updateOne({chat_id:n},{$set:{base_dashboard_message_id:l.message_id,base_dashboard_content:d,base_dashboard_markup:c.reply_markup}})}))},4544:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBscDashboard=t.getBscBalance=void 0;const a=n(9321),o=s(n(8722));t.getBscBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.BSC_PROVIDER_URL;if(!t)throw new Error("BSC_PROVIDER_URL is not defined in the .env file");const n=new a.ethers.providers.JsonRpcProvider(t),r=yield n.getBalance(e);return parseFloat(a.ethers.utils.formatEther(r)).toFixed(6)})),t.handleBscDashboard=(e,n)=>r(void 0,void 0,void 0,(function*(){var r;const s=yield o.default.findOne({chat_id:n});if(!s)return void e.sendMessage(n,"User not found.");const a=s.first_name||"User",i=(null===(r=s.bsc_wallet)||void 0===r?void 0:r.address)||"Not set",d=`👋 Welcome, ${a}, to your BSC Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** BSC\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,t.getBscBalance)(i):"0.0 BNB"} BNB\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_bsc"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_bsc"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_bsc"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_bsc_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(n,d,c);yield o.default.updateOne({chat_id:n},{$set:{bsc_dashboard_message_id:l.message_id,bsc_dashboard_content:d,bsc_dashboard_markup:c.reply_markup}})}))},439:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleEthDashboard=t.getEthBalance=void 0;const a=n(9321),o=s(n(8722));t.getEthBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.ETH_PROVIDER_URL;if(!t)throw new Error("Provider is not defined in the .env file");const n=new a.ethers.providers.JsonRpcProvider(t),r=yield n.getBalance(e);return parseFloat(a.ethers.utils.formatEther(r)).toFixed(6)})),t.handleEthDashboard=(e,n)=>r(void 0,void 0,void 0,(function*(){var r;const s=yield o.default.findOne({chat_id:n});if(!s)return void e.sendMessage(n,"User not found.");const a=s.first_name||"User",i=(null===(r=s.eth_wallet)||void 0===r?void 0:r.address)||"Not set",d=`👋 Welcome, ${a}, to your ETH Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** ethereum\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,t.getEthBalance)(i):"0.0 ETH"} ETH\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_eth"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_eth"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_eth"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_eth_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(n,d,c);yield o.default.updateOne({chat_id:n},{$set:{eth_dashboard_message_id:l.message_id,eth_dashboard_content:d,eth_dashboard_markup:c.reply_markup}})}))},8322:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleSolDashboard=void 0;const a=s(n(8722)),o=n(4035);t.handleSolDashboard=(e,t)=>r(void 0,void 0,void 0,(function*(){var n;try{const r=yield a.default.findOne({chat_id:t});if(!r)return void(yield e.sendMessage(t,"User not found."));const s=r.first_name||"User",i=(null===(n=r.sol_wallet)||void 0===n?void 0:n.address)||"Not set",d=`👋 Welcome, ${s}, to your sol Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** Solana\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,o.getSolBalance)(i):"0.0 sol"} SOL\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_sol"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_sol"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_sol"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_sol_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(t,d,c);yield a.default.updateOne({chat_id:t},{$set:{sol_dashboard_message_id:l.message_id,sol_dashboard_content:d,sol_dashboard_markup:c.reply_markup}})}catch(n){console.error("Error handling Sol dashboard:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while loading the Sol dashboard: ${r}`)}}))},5542:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.privatekeyHandler=void 0;const a=s(n(8722));t.privatekeyHandler=(e,t)=>r(void 0,void 0,void 0,(function*(){const{username:n,id:r,first_name:s,last_name:o}=t.chat;try{const t=yield a.default.findOne({chat_id:r});if(t){const n=`👋 View your Keys (Tap to copy)!\n\nSOL: <code><tg-spoiler> ${t.sol_wallet.private_key}</tg-spoiler></code>\nBSC: <code><tg-spoiler>${t.bsc_wallet.private_key}</tg-spoiler></code>\nBASE: <code><tg-spoiler>${t.base_wallet.private_key}</tg-spoiler></code>\nETH: <code><tg-spoiler> ${t.eth_wallet.private_key}</tg-spoiler></code>\n\n`;yield e.sendMessage(r,n,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}else yield e.sendMessage(r,"User not found. Please register first.")}catch(t){console.error("Error handling wallet selection:",t),yield e.sendMessage(r,"An error occurred while processing your request. Please try again later.")}}))},7307:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=s(n(8722));t.default=(e,t)=>r(void 0,void 0,void 0,(function*(){const{id:n}=t.chat;try{if(yield a.default.findOne({chat_id:n})){const t="👋 Select a blockchain to snipe!\n\n";yield e.sendMessage(n,t,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"scan_snipe_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"scan_snipe_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"scan_snipe_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"scan_snipe_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}}catch(t){console.error("Error handling wallet selection:",t),yield e.sendMessage(n,"An error occurred while processing your request. Please try again later.")}}))},8786:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.baseSniperScreen=void 0;const a=s(n(8087)),o=n(8904),i=n(3433),d=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_base"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"base_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"base_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c={},l=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(c[t]){const n=c[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return c[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${s}:`,e.text),c[t]=Object.assign(Object.assign({},c[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),u={};t.baseSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const s=yield(0,o.getLiquidityInfoFromDexscreener)(n),{symbol:c,name:p,chainId:f,dexId:m,pairAddress:_}=s,b=`\n🏷 **Token Information:**\n📍 **Symbol:** ${c}\n📛 **Name:** ${p}\n🔗 **Chain ID:** ${f}\n🏦 **Dex ID:** ${m}\n🔗 **Pair Address:** ${_}\n    `,v={parse_mode:"Markdown",reply_markup:d()};if(s.hasLiquidity){const n=yield e.sendMessage(t,b,v);u[t]=Object.assign(Object.assign({},u[t]),{initialMessageId:n.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}`,v),yield h(t,n,!1);u[t]=u[t]||{},u[t][n]={isConfigured:!0},e.on("callback_query",(o=>r(void 0,void 0,void 0,(function*(){var r,c,h,p;const f=null===(r=o.message)||void 0===r?void 0:r.message_id,m=(null===(c=o.message)||void 0===c||c.text,(null===(h=o.message)||void 0===h?void 0:h.reply_markup)||{}),_=JSON.parse(o.data||"{}").command;if("base_snipe_config"===_){u[t]={contractAddress:n};const r={inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_base"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_base"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_base"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_base"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_base"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_base"})}]]};JSON.stringify(r)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:f}))}else if("back_to_main_snipe_base"===_){const n=d();JSON.stringify(n)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(n,{chat_id:t,message_id:f}))}else if("base_snipe_execute"===_)s.hasLiquidity?yield e.answerCallbackQuery(o.id,{text:"🚫 Token has already been launched.",show_alert:!0}):(yield a.default.findOne({chatId:t,contractAddress:n,blockchain:"base"}))?((0,i.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Executing snipe...")):yield e.sendMessage(t,"❌ Snipe configuration not found.");else if("review_config_base"===_){const r=null===(p=u[t])||void 0===p?void 0:p.initialMessageId;r&&(yield y(e,t,r,n))}else if("set_slippage_base"===_){const r=yield l(e,t,"Enter new slippage percentage:","slippage");yield g(t,n,{slippage_base:parseInt(r,10)}),yield e.sendMessage(t,`Slippage set to ${r}%`)}else if("set_spend_base"===_){const r=yield l(e,t,"Enter new spend amount:","spend");yield g(t,n,{spend_base:parseFloat(r)}),yield e.sendMessage(t,`Spend amount set to ${r}`)}else if("set_time_delay_base"===_){const r=yield l(e,t,"Enter new time delay (in milliseconds):","time delay");yield g(t,n,{timeDelay_base:parseInt(r,10)}),yield e.sendMessage(t,`Time delay set to ${r} ms`)}else if("set_block_delay_base"===_){const r=yield l(e,t,"Enter new block delay:","block delay");yield g(t,n,{blockDelay_base:parseInt(r,10)}),yield e.sendMessage(t,`Block delay set to ${r}`)}else if("set_retries_base"===_){const r=yield l(e,t,"Enter new retry count:","retries");yield g(t,n,{retries_base:parseInt(r,10)}),yield e.sendMessage(t,`Retries set to ${r}`)}}))))}catch(n){console.error("Error in baseSniperScreen:",n),yield e.sendMessage(t,"❌ An error occurred during the operation. Please try again.")}}));const g=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"base"});r&&(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),console.log("Updated snipe config:",r))}catch(e){console.error("Error updating snipe config:",e)}})),h=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"base"}))return void console.log("BASE snipe config already exists for this contract address.");const r=new a.default({chatId:e,contractAddress:t,blockchain:"base",isBought:n});yield r.save(),console.log("BASE snipe config saved successfully.")}catch(e){console.error("Error saving BASE snipe config:",e)}})),y=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chatId:t,contractAddress:s,blockchain:"base"});if(r){const a=r.config,o=`\n📊 **Snipe Configuration:**\n*Contract:* ${s}\n*Slippage:* ${a.slippage_base||"N/A"}%\n*Spend:* ${a.spend_base||"N/A"}\n*Time Delay:* ${a.timeDelay_base||"N/A"} ms\n*Block Delay:* ${a.blockDelay_base||"N/A"}\n*Retries:* ${a.retries_base||"N/A"}\n      `;yield e.editMessageText(o,{chat_id:t,message_id:n,parse_mode:"Markdown"})}else yield e.sendMessage(t,"❌ Snipe configuration not found.")}catch(n){console.error("Error in reviewMessage:",n),yield e.sendMessage(t,"❌ An error occurred while reviewing the snipe configuration.")}}))},2893:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.bscSniperScreen=void 0;const a=s(n(8087)),o=n(9558),i=n(871),d=n(1213),c=n(3433),l=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_bsc"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"bsc_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"bsc_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),u=()=>({inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_bsc"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_bsc"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_bsc"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_bsc"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_bsc"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_bsc"})}]]}),g={},h=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(g[t]){const n=g[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return g[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${s}:`,e.text),g[t]=Object.assign(Object.assign({},g[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),y={};t.bscSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){var s;try{const g=yield(0,o.getTokenInfoFromDexscreener)(n,i.BlockchainType.BSC),_=i.TraderFactory.getTrader(i.BlockchainType.BSC),b=yield _.checkHoneypot(n),{symbol:v="Unknown",name:k="Unknown",priceUsd:w="Unknown",liquidity:S="Unknown",pairAddress:T="Unknown"}=g||{};let A=`\n🏷 **Token Information:**\n📍 **Symbol:** ${v}\n📛 **Name:** ${k}\n💵 **Price:** $${w}\n💧 **Liquidity:** $${"object"==typeof S&&null!==S?S.usd:"Unknown"}\n🔗 **Pair Address:** ${T}\n    `;b.isHoneypot&&(A+=`\n⚠️ **HONEYPOT WARNING** ⚠️\nThis token appears to be a honeypot. Sniping is not recommended.\nReason: ${b.honeypotReason||"Unknown"}\n      `);const O={parse_mode:"Markdown",reply_markup:l()},E=(null===(s=null==g?void 0:g.liquidity)||void 0===s?void 0:s.usd)>0;if(E){const r=yield e.sendMessage(t,A,O);y[t]=y[t]||{},y[t].bsc=y[t].bsc||{},y[t].bsc[n]={isConfigured:!0,initialMessageId:r.message_id},d.logger.info("BSC token with liquidity displayed",{chatId:t,contractAddress:n,hasLiquidity:!0,messageId:r.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}. Ready to snipe when liquidity is added.`,O),yield f(t,n,!1),d.logger.info("BSC snipe configuration saved",{chatId:t,contractAddress:n,hasLiquidity:!1});y[t]=y[t]||{},y[t].bsc=y[t].bsc||{},y[t].bsc[n]=y[t].bsc[n]||{isConfigured:!0};const x=s=>r(void 0,void 0,void 0,(function*(){var r,o,i,g,f,_,b,v,k,w,S;try{const T=null===(r=s.message)||void 0===r?void 0:r.message_id;if(!T)return;if((null===(o=s.message)||void 0===o?void 0:o.chat.id)!==t)return;if((null===(f=null===(g=null===(i=y[t])||void 0===i?void 0:i.bsc)||void 0===g?void 0:g[n])||void 0===f?void 0:f.initialMessageId)!==T&&!(null===(b=null===(_=s.message)||void 0===_?void 0:_.text)||void 0===b?void 0:b.includes(n)))return;const A=(null===(v=s.message)||void 0===v?void 0:v.reply_markup)||{},O=JSON.parse(s.data||"{}").command;switch(yield e.answerCallbackQuery(s.id),O){case"bsc_snipe_config":y[t]=y[t]||{},y[t].bsc=y[t].bsc||{},y[t].bsc[n]=y[t].bsc[n]||{},y[t].bsc[n].configMode=!0;const r=u();JSON.stringify(r)!==JSON.stringify(A)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:T}));break;case"back_to_main_snipe_bsc":const o=l();JSON.stringify(o)!==JSON.stringify(A)&&(yield e.editMessageReplyMarkup(o,{chat_id:t,message_id:T}));break;case"bsc_snipe_execute":if(E)yield e.answerCallbackQuery(s.id,{text:"🚫 Token has already been launched.",show_alert:!0});else{const r=yield a.default.findOne({chatId:t,contractAddress:n,blockchain:"bsc"});r?((0,c.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Snipe configured and ready! Monitoring for liquidity...\n\nThe bot will automatically execute the snipe when liquidity is detected."),d.logger.info("BSC snipe execution started",{chatId:t,contractAddress:n,snipeId:r._id})):yield e.sendMessage(t,"❌ Snipe configuration not found. Please configure the snipe first.")}break;case"review_config_bsc":const i=null===(S=null===(w=null===(k=y[t])||void 0===k?void 0:k.bsc)||void 0===w?void 0:w[n])||void 0===S?void 0:S.initialMessageId;i&&(yield m(e,t,i,n));break;case"set_slippage_bsc":const g=yield h(e,t,"Enter new slippage percentage:","slippage");yield p(t,n,{slippage_bsc:parseInt(g,10)}),yield e.sendMessage(t,`✅ Slippage set to ${g}%`);break;case"set_spend_bsc":const f=yield h(e,t,"Enter new spend amount:","spend");yield p(t,n,{spend_bsc:parseFloat(f)}),yield e.sendMessage(t,`✅ Spend amount set to ${f} BSC`);break;case"set_time_delay_bsc":const _=yield h(e,t,"Enter new time delay (in milliseconds):","time delay");yield p(t,n,{timeDelay_bsc:parseInt(_,10)}),yield e.sendMessage(t,`✅ Time delay set to ${_} ms`);break;case"set_block_delay_bsc":const b=yield h(e,t,"Enter new block delay:","block delay");yield p(t,n,{blockDelay_bsc:parseInt(b,10)}),yield e.sendMessage(t,`✅ Block delay set to ${b}`);break;case"set_retries_bsc":const v=yield h(e,t,"Enter new retry count:","retries");yield p(t,n,{retries_bsc:parseInt(v,10)}),yield e.sendMessage(t,`✅ Retries set to ${v}`);break;case"dismiss_message":try{yield e.deleteMessage(t,T),e.removeListener("callback_query",x),d.logger.debug("BSC sniper message dismissed",{chatId:t,messageId:T})}catch(e){d.logger.error("Failed to delete BSC sniper message",{chatId:t,messageId:T,error:e.message})}}}catch(r){d.logger.error("Error handling BSC sniper callback",{chatId:t,contractAddress:n,error:r.message}),yield e.sendMessage(t,`⚠️ An error occurred: ${r.message}`)}}));e.on("callback_query",x)}catch(r){d.logger.error("Error in bscSniperScreen",{chatId:t,contractAddress:n,error:r.message}),yield e.sendMessage(t,`❌ An error occurred: ${r.message}`)}}));const p=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});r?(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),d.logger.info("Updated BSC snipe config",{chatId:e,contractAddress:t,updates:Object.keys(n),snipeId:r._id})):d.logger.warn("Snipe configuration not found for update",{chatId:e,contractAddress:t,blockchain:"bsc"})}catch(n){d.logger.error("Error updating snipe config",{chatId:e,contractAddress:t,error:n.message})}})),f=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});if(r)return void d.logger.info("BSC snipe config already exists",{chatId:e,contractAddress:t,snipeId:r._id});const s=new a.default({chatId:e,contractAddress:t,blockchain:"bsc",hasLiquidity:n,config:{isConfigured:!0,isBought:!1,isExecuted_bsc:!1,slippage_bsc:5,spend_bsc:.1,timeDelay_bsc:0,blockDelay_bsc:0,retries_bsc:3}});yield s.save(),d.logger.info("New BSC snipe config saved",{chatId:e,contractAddress:t,snipeId:s._id})}catch(n){d.logger.error("Error saving BSC snipe config",{chatId:e,contractAddress:t,error:n.message})}})),m=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){var r;try{const c=yield a.default.findOne({chatId:t,contractAddress:s,blockchain:"bsc"});if(c){const a=yield(0,o.getTokenInfoFromDexscreener)(s,i.BlockchainType.BSC),l=c.config,g=`\n📊 **BSC Snipe Configuration:**\n📝 **Contract:** ${s}\n🚀 **Token:** ${(null==a?void 0:a.name)||"Unknown"} (${(null==a?void 0:a.symbol)||"Unknown"})\n💵 **Current Price:** $${(null==a?void 0:a.priceUsd)||"Unknown"}\n💧 **Liquidity:** $${(null===(r=null==a?void 0:a.liquidity)||void 0===r?void 0:r.usd)||"Unknown"}\n\n⚙️ **Snipe Settings:**\n📈 **Slippage:** ${l.slippage_bsc||"5"}%\n💰 **Spend Amount:** ${l.spend_bsc||"0.1"} BSC\n⏱️ **Time Delay:** ${l.timeDelay_bsc||"0"} ms\n🧱 **Block Delay:** ${l.blockDelay_bsc||"0"} blocks\n🔄 **Retries:** ${l.retries_bsc||"3"}\n🚀 **Ready to Snipe:** ${c.hasLiquidity?"No (Liquidity already added)":"Yes"}\n      `;yield e.editMessageText(g,{chat_id:t,message_id:n,parse_mode:"Markdown",reply_markup:u()}),d.logger.debug("BSC snipe configuration reviewed",{chatId:t,contractAddress:s,snipeId:c._id})}else yield e.sendMessage(t,"❌ Snipe configuration not found. Please set up a snipe first.")}catch(r){d.logger.error("Error in reviewMessage",{chatId:t,contractAddress:s,messageId:n,error:r.message}),yield e.sendMessage(t,`❌ An error occurred: ${r.message}`)}}))},4248:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ethSniperScreen=void 0;const a=s(n(8087)),o=n(8904),i=n(3433),d=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_eth"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"eth_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"eth_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c={},l=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(c[t]){const n=c[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return c[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${s}:`,e.text),c[t]=Object.assign(Object.assign({},c[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),u={};t.ethSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const s=yield(0,o.getLiquidityInfoFromDexscreener)(n),{symbol:c,name:p,chainId:f,dexId:m,pairAddress:_}=s,b=`\n🏷 **Token Information:**\n📍 **Symbol:** ${c}\n📛 **Name:** ${p}\n🔗 **Chain ID:** ${f}\n🏦 **Dex ID:** ${m}\n🔗 **Pair Address:** ${_}\n    `,v={parse_mode:"Markdown",reply_markup:d()};if(s.hasLiquidity){const n=yield e.sendMessage(t,b,v);u[t]=Object.assign(Object.assign({},u[t]),{initialMessageId:n.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}`,v),yield h(t,n,!1);u[t]=u[t]||{},u[t][n]={isConfigured:!0},e.on("callback_query",(o=>r(void 0,void 0,void 0,(function*(){var r,c,h,p;const f=null===(r=o.message)||void 0===r?void 0:r.message_id,m=(null===(c=o.message)||void 0===c||c.text,(null===(h=o.message)||void 0===h?void 0:h.reply_markup)||{}),_=JSON.parse(o.data||"{}").command;if("eth_snipe_config"===_){u[t]={contractAddress:n};const r={inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_eth"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_eth"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_eth"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_eth"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_eth"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_eth"})}]]};JSON.stringify(r)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:f}))}else if("back_to_main_snipe_eth"===_){const n=d();JSON.stringify(n)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(n,{chat_id:t,message_id:f}))}else if("eth_snipe_execute"===_)s.hasLiquidity?yield e.answerCallbackQuery(o.id,{text:"🚫 Token has already been launched.",show_alert:!0}):(yield a.default.findOne({chatId:t,contractAddress:n,blockchain:"eth"}))?((0,i.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Executing snipe...")):yield e.sendMessage(t,"❌ Snipe configuration not found.");else if("review_config_eth"===_){const r=null===(p=u[t])||void 0===p?void 0:p.initialMessageId;r&&(yield y(e,t,r,n))}else if("set_slippage_eth"===_){const r=yield l(e,t,"Enter new slippage percentage:","slippage");yield g(t,n,{slippage_eth:parseInt(r,10)}),yield e.sendMessage(t,`Slippage set to ${r}%`)}else if("set_spend_eth"===_){const r=yield l(e,t,"Enter new spend amount:","spend");yield g(t,n,{spend_eth:parseFloat(r)}),yield e.sendMessage(t,`Spend amount set to ${r}`)}else if("set_time_delay_eth"===_){const r=yield l(e,t,"Enter new time delay (in milliseconds):","time delay");yield g(t,n,{timeDelay_eth:parseInt(r,10)}),yield e.sendMessage(t,`Time delay set to ${r} ms`)}else if("set_block_delay_eth"===_){const r=yield l(e,t,"Enter new block delay:","block delay");yield g(t,n,{blockDelay_eth:parseInt(r,10)}),yield e.sendMessage(t,`Block delay set to ${r}`)}else if("set_retries_eth"===_){const r=yield l(e,t,"Enter new retry count:","retries");yield g(t,n,{retries_eth:parseInt(r,10)}),yield e.sendMessage(t,`Retries set to ${r}`)}}))))}catch(n){console.error("Error in ethSniperScreen:",n),yield e.sendMessage(t,"❌ An error occurred during the operation. Please try again.")}}));const g=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"});r&&(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),console.log("Updated snipe config:",r))}catch(e){console.error("Error updating snipe config:",e)}})),h=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"}))return void console.log("ETH snipe config already exists for this contract address.");const r=new a.default({chatId:e,contractAddress:t,blockchain:"eth",isBought:n});yield r.save(),console.log("ETH snipe config saved successfully.")}catch(e){console.error("Error saving ETH snipe config:",e)}})),y=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chatId:t,contractAddress:s,blockchain:"eth"});if(r){const a=r.config,o=`\n📊 **Snipe Configuration:**\n*Contract:* ${s}\n*Slippage:* ${a.slippage_eth||"N/A"}%\n*Spend:* ${a.spend_eth||"N/A"}\n*Time Delay:* ${a.timeDelay_eth||"N/A"} ms\n*Block Delay:* ${a.blockDelay_eth||"N/A"}\n*Retries:* ${a.retries_eth||"N/A"}\n      `;yield e.editMessageText(o,{chat_id:t,message_id:n,parse_mode:"Markdown"})}else yield e.sendMessage(t,"❌ Snipe configuration not found.")}catch(n){console.error("Error in reviewMessage:",n),yield e.sendMessage(t,"❌ An error occurred while reviewing the snipe configuration.")}}))},4259:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.solSniperScreen=void 0;const a=s(n(8087)),o=n(8904),i=n(3433),d=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_sol"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"sol_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"sol_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c={},l=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(c[t]){const n=c[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return c[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${s}:`,e.text),c[t]=Object.assign(Object.assign({},c[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),u={};t.solSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const s=yield(0,o.getLiquidityInfoFromDexscreener)(n),{symbol:c,name:p,chainId:f,dexId:m,pairAddress:_}=s,b=`\n🏷 **Token Information:**\n📍 **Symbol:** ${c}\n📛 **Name:** ${p}\n🔗 **Chain ID:** ${f}\n🏦 **Dex ID:** ${m}\n🔗 **Pair Address:** ${_}\n    `,v={parse_mode:"Markdown",reply_markup:d()};if(s.hasLiquidity){const n=yield e.sendMessage(t,b,v);u[t]=Object.assign(Object.assign({},u[t]),{initialMessageId:n.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}`,v),yield h(t,n,!1);u[t]=u[t]||{},u[t][n]={isConfigured:!0},e.on("callback_query",(o=>r(void 0,void 0,void 0,(function*(){var r,c,h,p;const f=null===(r=o.message)||void 0===r?void 0:r.message_id,m=(null===(c=o.message)||void 0===c||c.text,(null===(h=o.message)||void 0===h?void 0:h.reply_markup)||{}),_=JSON.parse(o.data||"{}").command;if("sol_snipe_config"===_){u[t]={contractAddress:n};const r={inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_sol"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_sol"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_sol"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_sol"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_sol"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_sol"})}]]};JSON.stringify(r)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:f}))}else if("back_to_main_snipe_sol"===_){const n=d();JSON.stringify(n)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(n,{chat_id:t,message_id:f}))}else if("sol_snipe_execute"===_)s.hasLiquidity?yield e.answerCallbackQuery(o.id,{text:"🚫 Token has already been launched.",show_alert:!0}):(yield a.default.findOne({chatId:t,contractAddress:n,blockchain:"sol"}))?((0,i.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Executing snipe...")):yield e.sendMessage(t,"❌ Snipe configuration not found.");else if("review_config_sol"===_){const r=null===(p=u[t])||void 0===p?void 0:p.initialMessageId;r&&(yield y(e,t,r,n))}else if("set_slippage_sol"===_){const r=yield l(e,t,"Enter new slippage percentage:","slippage");yield g(t,n,{slippage_sol:parseInt(r,10)}),yield e.sendMessage(t,`Slippage set to ${r}%`)}else if("set_spend_sol"===_){const r=yield l(e,t,"Enter new spend amount:","spend");yield g(t,n,{spend_sol:parseFloat(r)}),yield e.sendMessage(t,`Spend amount set to ${r}`)}else if("set_time_delay_sol"===_){const r=yield l(e,t,"Enter new time delay (in milliseconds):","time delay");yield g(t,n,{timeDelay_sol:parseInt(r,10)}),yield e.sendMessage(t,`Time delay set to ${r} ms`)}else if("set_block_delay_sol"===_){const r=yield l(e,t,"Enter new block delay:","block delay");yield g(t,n,{blockDelay_sol:parseInt(r,10)}),yield e.sendMessage(t,`Block delay set to ${r}`)}else if("set_retries_sol"===_){const r=yield l(e,t,"Enter new retry count:","retries");yield g(t,n,{retries_sol:parseInt(r,10)}),yield e.sendMessage(t,`Retries set to ${r}`)}}))))}catch(n){console.error("Error in solSniperScreen:",n),yield e.sendMessage(t,"❌ An error occurred during the operation. Please try again.")}}));const g=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"});r&&(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),console.log("Updated snipe config:",r))}catch(e){console.error("Error updating snipe config:",e)}})),h=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(yield a.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"}))return void console.log("SOL snipe config already exists for this contract address.");const r=new a.default({chatId:e,contractAddress:t,blockchain:"sol",isBought:n});yield r.save(),console.log("SOL snipe config saved successfully.")}catch(e){console.error("Error saving SOL snipe config:",e)}})),y=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chatId:t,contractAddress:s,blockchain:"sol"});if(r){const a=r.config,o=`\n📊 **Snipe Configuration:**\n*Contract:* ${s}\n*Slippage:* ${a.slippage_sol||"N/A"}%\n*Spend:* ${a.spend_sol||"N/A"}\n*Time Delay:* ${a.timeDelay_sol||"N/A"} ms\n*Block Delay:* ${a.blockDelay_sol||"N/A"}\n*Retries:* ${a.retries_sol||"N/A"}\n      `;yield e.editMessageText(o,{chat_id:t,message_id:n,parse_mode:"Markdown"})}else yield e.sendMessage(t,"❌ Snipe configuration not found.")}catch(n){console.error("Error in reviewMessage:",n),yield e.sendMessage(t,"❌ An error occurred while reviewing the snipe configuration.")}}))},9266:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeBaseScreen=t.reviewBaseTrade=t.monitorBasePriceAndSell=t.executeBaseTrade=t.updateBaseTradeConfig=t.createBaseTradeKeyboard=t.askForBaseTradeInput=void 0,t.getbaseTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){const n=new a.ethers.providers.JsonRpcProvider(h),r=new a.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[s,o]=yield Promise.all([r.balanceOf(t),r.decimals()]);return a.ethers.utils.formatUnits(s,o)}))};const a=n(9321),o=s(n(4103)),i=n(9558),d=n(871),c=n(1213),l=n(4103),u=n(6786),g=s(n(8722)),h=process.env.BASE_PROVIDER_URL;if(!h)throw new Error("BASE_PROVIDER_URL environment variable is not set");const y={};t.askForBaseTradeInput=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(y[t]){const n=y[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return y[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(a=>r(void 0,void 0,void 0,(function*(){if(a.text){const r=parseFloat(a.text);y[t]=Object.assign(Object.assign({},y[t]),{replyId:a.message_id});const o=`✅ ${s.charAt(0).toUpperCase()+s.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);y[t]=Object.assign(Object.assign({},y[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),t.createBaseTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_base"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_base"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_base"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_base"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_base"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_base"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateBaseTradeConfig=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:e,contractAddress:t,blockchain:"base"});r&&(r.config[n]=s,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeBaseTrade=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:n,contractAddress:s,blockchain:"base"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const a=d.TraderFactory.getTrader(d.BlockchainType.BASE),i=r.config.spendAmount.toString(),l=yield a.buyTokens(s,i,"",{slippage:5,gasLimit:1e6});if(!l.success)return c.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:s,error:l.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${l.error||"Unknown error"}`));yield o.default.findOneAndUpdate({chatId:n,contractAddress:s,blockchain:"base"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${l.transactionHash}`),yield(0,t.monitorBasePriceAndSell)(e,n,r),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),c.logger.info("BASE trade executed successfully",{chatId:n,contractAddress:s,transactionHash:l.transactionHash})}catch(t){c.logger.error("Error executing BASE trade",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const p={};t.monitorBasePriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const s=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;c.logger.info("Starting BASE price monitoring",{chatId:t,contractAddress:n.contractAddress,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const a=setInterval((()=>r(void 0,void 0,void 0,(function*(){var r;try{const o=yield(0,i.getTokenInfoFromDexscreener)(n.contractAddress,d.BlockchainType.BASE);if(!o||void 0===o.priceChange)return void c.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress});const h=parseFloat((null===(r=o.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(h))return void c.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:o.priceChange});if(c.logger.debug(`Price Change: ${h}%`,{contractAddress:n.contractAddress,priceChange:h}),h>=n.config.takeProfit||h<=n.config.stopLoss){c.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:h,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),clearInterval(a),delete p[s];const r=yield g.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:o}=r.base_wallet,i=d.TraderFactory.getTrader(d.BlockchainType.BASE),y=yield i.getTokenBalance(n.contractAddress,o),f=parseFloat(y);if(isNaN(f)||f<=0)return void c.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:y});c.logger.info(`Token Balance: ${f}`,{contractAddress:n.contractAddress,userAddress:o});let m=!1,_=0,b="";for(;_<2&&!m;)try{_++,c.logger.info(`Selling tokens, attempt ${_}`,{contractAddress:n.contractAddress,tokenBalance:f});const t=yield i.sellTokens(n.contractAddress,parseFloat(y),o,{slippage:5,gasLimit:1e6});if(t.success)return m=!0,b=t.transactionHash||"",yield(0,l.updateAutoTradeAsSold)(r._id,n.contractAddress,f,"base"),yield(0,u.updateTradeAsSold)(r._id,n.contractAddress,f,"base"),c.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:b}),void(e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${b}\n💰 Amount Sold: ${f} tokens\n📊 Price Change: ${h}%`)));c.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){c.logger.error(`Attempt ${_} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return void(m||(c.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${_} attempts.\nPlease try to sell manually.`))))}c.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:h,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss})}catch(e){c.logger.error("Error in monitorBasePriceAndSell",{contractAddress:n.contractAddress,error:e.message})}}))),15e3);p[s]=a})),t.reviewBaseTrade=(e,n,s,a)=>r(void 0,void 0,void 0,(function*(){var r;try{const l=yield o.default.findOne({chatId:n,contractAddress:a,blockchain:"base"});if(!l)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,i.getTokenInfoFromDexscreener)(a,d.BlockchainType.BASE),g=(null==u?void 0:u.priceUsd)||"Unknown",h=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",y=l.isSold?"Yes":"No",p="ongoing"===l.isInitialized?"Active":"pending"===l.isInitialized?"Pending":"Completed",f=`💼 **BASE Trade Details:**\n📝 **Contract:** ${a}\n🚀 **Token:** ${l.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${h}%\n📈 **Take Profit:** ${l.config.takeProfit}%\n📉 **Stop Loss:** ${l.config.stopLoss}%\n💰 **Spend Amount:** ${l.config.spendAmount} BASE\n🔄 **Is Sold:** ${y}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:(0,t.createBaseTradeKeyboard)()}),c.logger.debug("BASE trade review displayed",{chatId:n,contractAddress:a,messageId:s})}catch(t){c.logger.error("Error in reviewBaseTrade",{chatId:n,contractAddress:a,messageId:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeBaseScreen=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const a=yield(0,i.getTokenInfoFromDexscreener)(s,d.BlockchainType.BASE);if(!a)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:l,name:u}=a,g=d.TraderFactory.getTrader(d.BlockchainType.BASE),h=yield g.checkHoneypot(s),y=new o.default({chatId:n,blockchain:"base",contractAddress:s,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield y.save();let p="💼 **BASE Trading**\n";p+=`📝 **Contract:** ${s}\n`,p+=`🚀 **Token:** ${u} (${l})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/base/${s})\n\n`,h.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${h.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createBaseTradeKeyboard)()});c.logger.info("BASE trade screen displayed",{chatId:n,contractAddress:s,messageId:f.message_id,isHoneypot:h.isHoneypot});const m=a=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=a.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(a.data).command;switch(yield e.answerCallbackQuery(a.id),r){case"snipe_set_take_profit_base":const r=yield(0,t.askForBaseTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateBaseTradeConfig)(n,s,"takeProfit",r);break;case"snipe_set_stop_loss_base":const o=yield(0,t.askForBaseTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateBaseTradeConfig)(n,s,"stopLoss",o);break;case"snipe_set_spend_amount_base":const i=yield(0,t.askForBaseTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateBaseTradeConfig)(n,s,"spendAmount",i);break;case"snipetrade_buy_base":yield(0,t.executeBaseTrade)(e,n,s);break;case"snipe_review_trade_base":yield(0,t.reviewBaseTrade)(e,n,a.message.message_id,s);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){c.logger.error("Error handling BASE trade callback",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){c.logger.error("Error in tradeBaseScreen",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the BASE trading screen: ${t.message}`)}}))},8485:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeBscScreen=t.reviewBscTrade=t.monitorBscPriceAndSell=t.executeBscTrade=t.updateBscTradeConfig=t.createBscTradeKeyboard=t.askForBscTradeInput=void 0,t.getBscTokenBalance=m;const a=n(9321),o=s(n(4103)),i=n(9558),d=n(871),c=n(1213),l=n(4103),u=n(6786),g=s(n(8722)),h=process.env.EVM_ADMIN_ADDRESS||"0x25AA04655aefbC523412eD5a67d36968127499f5",y=process.env.BSC_PROVIDER_URL;if(!h)throw new Error("ADMIN_ADDRESS environment variable is not set");if(!y)throw new Error("Provider environment variable is not set");const p={};t.askForBscTradeInput=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(p[t]){const n=p[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return p[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(a=>r(void 0,void 0,void 0,(function*(){if(a.text){const r=parseFloat(a.text);p[t]=Object.assign(Object.assign({},p[t]),{replyId:a.message_id});const o=`✅ ${s.charAt(0).toUpperCase()+s.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);p[t]=Object.assign(Object.assign({},p[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),t.createBscTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_bsc"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_bsc"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_bsc"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_bsc"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_bsc"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_bsc"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateBscTradeConfig=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});r&&(r.config[n]=s,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeBscTrade=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:n,contractAddress:s,blockchain:"bsc"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const a=d.TraderFactory.getTrader(d.BlockchainType.BSC),i=r.config.spendAmount.toString(),l=yield a.buyTokens(s,i,"",{slippage:5,gasLimit:1e6});if(!l.success)return c.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:s,error:l.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${l.error||"Unknown error"}`));yield o.default.findOneAndUpdate({chatId:n,contractAddress:s,blockchain:"bsc"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${l.transactionHash}`),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),(0,t.monitorBscPriceAndSell)(e,n,r),c.logger.info("BSC trade executed successfully",{chatId:n,contractAddress:s,transactionHash:l.transactionHash})}catch(t){c.logger.error("Error executing BSC trade",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const f={};function m(e,t){return r(this,void 0,void 0,(function*(){const n=new a.ethers.providers.JsonRpcProvider(y),r=new a.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[s,o]=yield Promise.all([r.balanceOf(t),r.decimals()]);return a.ethers.utils.formatUnits(s,o)}))}t.monitorBscPriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const s=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;c.logger.info("Starting BSC price monitoring",{chatId:t,contractAddress:n.contractAddress,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const a=setInterval((()=>r(void 0,void 0,void 0,(function*(){var r;try{const o=yield(0,i.getTokenInfoFromDexscreener)(n.contractAddress,d.BlockchainType.BSC);if(!o||void 0===o.priceChange)return void c.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress});const h=parseFloat((null===(r=o.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(h))return void c.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:o.priceChange});if(c.logger.debug(`Price Change: ${h}%`,{contractAddress:n.contractAddress,priceChange:h}),h>=n.config.takeProfit||h<=n.config.stopLoss){c.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:h,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),clearInterval(a),delete f[s];const r=yield g.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:o}=r.bsc_wallet,i=yield m(n.contractAddress,o),y=parseFloat(i);if(isNaN(y)||y<=0)return void c.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:i});c.logger.info(`Token Balance: ${y}`,{contractAddress:n.contractAddress,userAddress:o});const p=d.TraderFactory.getTrader(d.BlockchainType.BSC);let _=!1,b=0,v="";for(;b<2&&!_;)try{b++,c.logger.info(`Selling tokens, attempt ${b}`,{contractAddress:n.contractAddress,tokenBalance:y});const t=yield p.sellTokens(n.contractAddress,parseFloat(i),"",{slippage:5,gasLimit:1e6});if(t.success)return _=!0,v=t.transactionHash||"",yield(0,l.updateAutoTradeAsSold)(r._id,n.contractAddress,y,"bsc"),yield(0,u.updateTradeAsSold)(r._id,n.contractAddress,y,"bsc"),c.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:v}),void(e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${v}\n💰 Amount Sold: ${y} tokens\n📊 Price Change: ${h}%`)));c.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){c.logger.error(`Attempt ${b} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return void(_||(c.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${b} attempts.\nPlease try to sell manually.`))))}c.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:h,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss})}catch(e){c.logger.error("Error in monitorBscPriceAndSell",{contractAddress:n.contractAddress,error:e.message})}}))),15e3);f[s]=a})),t.reviewBscTrade=(e,n,s,a)=>r(void 0,void 0,void 0,(function*(){var r;try{const l=yield o.default.findOne({chatId:n,contractAddress:a,blockchain:"bsc"});if(!l)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,i.getTokenInfoFromDexscreener)(a,d.BlockchainType.BSC),g=(null==u?void 0:u.priceUsd)||"Unknown",h=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",y=l.isSold?"Yes":"No",p="ongoing"===l.isInitialized?"Active":"pending"===l.isInitialized?"Pending":"Completed",f=`💼 **BSC Trade Details:**\n📝 **Contract:** ${a}\n🚀 **Token:** ${l.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${h}%\n📈 **Take Profit:** ${l.config.takeProfit}%\n📉 **Stop Loss:** ${l.config.stopLoss}%\n💰 **Spend Amount:** ${l.config.spendAmount} BSC\n🔄 **Is Sold:** ${y}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:(0,t.createBscTradeKeyboard)()}),c.logger.debug("BSC trade review displayed",{chatId:n,contractAddress:a,messageId:s})}catch(t){c.logger.error("Error in reviewBscTrade",{chatId:n,contractAddress:a,messageId:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeBscScreen=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const a=yield(0,i.getTokenInfoFromDexscreener)(s,d.BlockchainType.BSC);if(!a)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:l,name:u}=a,g=d.TraderFactory.getTrader(d.BlockchainType.BSC),h=yield g.checkHoneypot(s),y=new o.default({chatId:n,blockchain:"bsc",contractAddress:s,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield y.save();let p="💼 **BSC Trading**\n";p+=`📝 **Contract:** ${s}\n`,p+=`🚀 **Token:** ${u} (${l})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/bsc/${s})\n\n`,h.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${h.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createBscTradeKeyboard)()});c.logger.info("BSC trade screen displayed",{chatId:n,contractAddress:s,messageId:f.message_id,isHoneypot:h.isHoneypot});const m=a=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=a.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(a.data).command;switch(yield e.answerCallbackQuery(a.id),r){case"snipe_set_take_profit_bsc":const r=yield(0,t.askForBscTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateBscTradeConfig)(n,s,"takeProfit",r);break;case"snipe_set_stop_loss_bsc":const o=yield(0,t.askForBscTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateBscTradeConfig)(n,s,"stopLoss",o);break;case"snipe_set_spend_amount_bsc":const i=yield(0,t.askForBscTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateBscTradeConfig)(n,s,"spendAmount",i);break;case"snipetrade_buy_bsc":yield(0,t.executeBscTrade)(e,n,s);break;case"snipe_review_trade_bsc":yield(0,t.reviewBscTrade)(e,n,a.message.message_id,s);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){c.logger.error("Error handling BSC trade callback",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){c.logger.error("Error in tradeBscScreen",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the BSC trading screen: ${t.message}`)}}))},2836:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeEthScreen=t.reviewEthTrade=t.monitorEthPriceAndSell=t.executeEthTrade=t.updateEthTradeConfig=t.createEthTradeKeyboard=t.askForEthTradeInput=void 0,t.getEthTokenBalance=m;const a=n(9321),o=s(n(4103)),i=n(9558),d=n(871),c=n(1213),l=n(4103),u=n(6786),g=s(n(8722)),h=process.env.EVM_ADMIN_ADDRESS||"",y=process.env.ETH_PROVIDER_URL;if(!h)throw new Error("ADMIN_ADDRESS environment variable is not set");if(!y)throw new Error("Provider environment variable is not set");const p={};t.askForEthTradeInput=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(p[t]){const n=p[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return p[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(a=>r(void 0,void 0,void 0,(function*(){if(a.text){const r=parseFloat(a.text);p[t]=Object.assign(Object.assign({},p[t]),{replyId:a.message_id});const o=`✅ ${s.charAt(0).toUpperCase()+s.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);p[t]=Object.assign(Object.assign({},p[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),t.createEthTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_eth"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_eth"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_eth"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_eth"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_eth"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_eth"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateEthTradeConfig=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"});r&&(r.config[n]=s,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeEthTrade=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:n,contractAddress:s,blockchain:"eth"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const a=d.TraderFactory.getTrader(d.BlockchainType.ETH),i=r.config.spendAmount.toString(),l=yield a.buyTokens(s,i,"",{slippage:5,gasLimit:1e6});if(!l.success)return c.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:s,error:l.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${l.error||"Unknown error"}`));yield o.default.findOneAndUpdate({chatId:n,contractAddress:s,blockchain:"eth"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${l.transactionHash}`),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),(0,t.monitorEthPriceAndSell)(e,n,r),c.logger.info("ETH trade executed successfully",{chatId:n,contractAddress:s,transactionHash:l.transactionHash})}catch(t){c.logger.error("Error executing ETH trade",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const f={};function m(e,t){return r(this,void 0,void 0,(function*(){const n=new a.ethers.providers.JsonRpcProvider(y),r=new a.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[s,o]=yield Promise.all([r.balanceOf(t),r.decimals()]);return a.ethers.utils.formatUnits(s,o)}))}t.monitorEthPriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){var r;try{const s=yield(0,i.getTokenInfoFromDexscreener)(n.contractAddress,d.BlockchainType.ETH);if(!s||void 0===s.priceChange)return c.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress}),!1;const a=parseFloat((null===(r=s.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(a))return c.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:s.priceChange}),!1;if(c.logger.debug(`Price Change: ${a}%`,{contractAddress:n.contractAddress,priceChange:a}),a>=n.config.takeProfit||a<=n.config.stopLoss){c.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:a,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const r=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;f[r]&&(f[r].stop(),delete f[r]);const s=yield g.default.findOne({chat_id:t}).exec();if(!s)throw new Error("User not found");const{address:o}=s.eth_wallet,i=yield m(n.contractAddress,o),h=parseFloat(i);if(isNaN(h)||h<=0)return c.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:i}),!1;c.logger.info(`Token Balance: ${h}`,{contractAddress:n.contractAddress,userAddress:o});const y=d.TraderFactory.getTrader(d.BlockchainType.ETH);let p=!1,_=0,b="";for(;_<2&&!p;)try{_++,c.logger.info(`Selling tokens, attempt ${_}`,{contractAddress:n.contractAddress,tokenBalance:h});const t=yield y.sellTokens(n.contractAddress,parseFloat(i),"",{slippage:5,gasLimit:1e6});if(t.success)return p=!0,b=t.transactionHash||"",yield(0,l.updateAutoTradeAsSold)(s._id,n.contractAddress,h,"eth"),yield(0,u.updateTradeAsSold)(s._id,n.contractAddress,h,"eth"),c.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:b}),e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${b}\n💰 Amount Sold: ${h} tokens\n📊 Price Change: ${a}%`)),!0;c.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){c.logger.error(`Attempt ${_} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return p||(c.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${_} attempts.\nPlease try to sell manually.`))),p}return c.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:a,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),!1}catch(e){return c.logger.error("Error in monitorEthPriceAndSell",{contractAddress:n.contractAddress,error:e.message}),!1}})),t.reviewEthTrade=(e,n,s,a)=>r(void 0,void 0,void 0,(function*(){var r;try{const l=yield o.default.findOne({chatId:n,contractAddress:a,blockchain:"eth"});if(!l)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,i.getTokenInfoFromDexscreener)(a,d.BlockchainType.ETH),g=(null==u?void 0:u.priceUsd)||"Unknown",h=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",y=l.isSold?"Yes":"No",p="ongoing"===l.isInitialized?"Active":"pending"===l.isInitialized?"Pending":"Completed",f=`💼 **ETH Trade Details:**\n📝 **Contract:** ${a}\n🚀 **Token:** ${l.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${h}%\n📈 **Take Profit:** ${l.config.takeProfit}%\n📉 **Stop Loss:** ${l.config.stopLoss}%\n💰 **Spend Amount:** ${l.config.spendAmount} ETH\n🔄 **Is Sold:** ${y}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:(0,t.createEthTradeKeyboard)()}),c.logger.debug("ETH trade review displayed",{chatId:n,contractAddress:a,messageId:s})}catch(t){c.logger.error("Error in reviewEthTrade",{chatId:n,contractAddress:a,messageId:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeEthScreen=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const a=yield(0,i.getTokenInfoFromDexscreener)(s,d.BlockchainType.ETH);if(!a)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:l,name:u}=a,g=d.TraderFactory.getTrader(d.BlockchainType.ETH),h=yield g.checkHoneypot(s),y=new o.default({chatId:n,blockchain:"eth",contractAddress:s,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield y.save();let p="💼 **ETH Trading**\n";p+=`📝 **Contract:** ${s}\n`,p+=`🚀 **Token:** ${u} (${l})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/ethereum/${s})\n\n`,h.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${h.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createEthTradeKeyboard)()});c.logger.info("ETH trade screen displayed",{chatId:n,contractAddress:s,messageId:f.message_id,isHoneypot:h.isHoneypot});const m=a=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=a.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(a.data).command;switch(yield e.answerCallbackQuery(a.id),r){case"snipe_set_take_profit_eth":const r=yield(0,t.askForEthTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateEthTradeConfig)(n,s,"takeProfit",r);break;case"snipe_set_stop_loss_eth":const o=yield(0,t.askForEthTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateEthTradeConfig)(n,s,"stopLoss",o);break;case"snipe_set_spend_amount_eth":const i=yield(0,t.askForEthTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateEthTradeConfig)(n,s,"spendAmount",i);break;case"snipetrade_buy_eth":yield(0,t.executeEthTrade)(e,n,s);break;case"snipe_review_trade_eth":yield(0,t.reviewEthTrade)(e,n,a.message.message_id,s);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){c.logger.error("Error handling ETH trade callback",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){c.logger.error("Error in tradeEthScreen",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the ETH trading screen: ${t.message}`)}}))},9527:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeSolScreen=t.reviewSolTrade=t.monitorSolPriceAndSell=t.executeSolTrade=t.updateSolTradeConfig=t.createSolTradeKeyboard=t.askForSolTradeInput=void 0,t.getSolTokenBalance=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield e.getParsedTokenAccountsByOwner(new a.PublicKey(t),{mint:new a.PublicKey(n)});if(0===r.value.length)throw new Error("Token account not found");const s=r.value[0].account.data.parsed.info.tokenAmount.uiAmount;return console.log("Balance (using Solana-Web3.js): ",s),s}catch(e){throw console.error("Error fetching token balance:",e),e}}))};const a=n(8491),o=s(n(4103)),i=n(9558),d=n(871),c=n(1213),l=n(4103),u=n(6786),g=s(n(8722)),h=process.env.SOL_PROVIDER_URL;if(new a.Connection(h||"https://api.mainnet-beta.solana.com"),!h)throw new Error("Provider environment variable is not set");const y={};t.askForSolTradeInput=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(y[t]){const n=y[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const a=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return y[t]={promptId:a.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,a.message_id,(a=>r(void 0,void 0,void 0,(function*(){if(a.text){const r=parseFloat(a.text);y[t]=Object.assign(Object.assign({},y[t]),{replyId:a.message_id});const o=`✅ ${s.charAt(0).toUpperCase()+s.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);y[t]=Object.assign(Object.assign({},y[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${s}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${s}:`,e),e}})),t.createSolTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_sol"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_sol"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_sol"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_sol"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_sol"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_sol"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateSolTradeConfig=(e,t,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"});r&&(r.config[n]=s,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeSolTrade=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:n,contractAddress:s,blockchain:"sol"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const a=d.TraderFactory.getTrader(d.BlockchainType.SOL),i=r.config.spendAmount.toString(),l=yield a.buyTokens(s,i,"",{slippage:5,gasLimit:1e6});if(!l.success)return c.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:s,error:l.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${l.error||"Unknown error"}`));yield o.default.findOneAndUpdate({chatId:n,contractAddress:s,blockchain:"sol"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${l.transactionHash}`),yield(0,t.monitorSolPriceAndSell)(e,n,r),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),c.logger.info("SOL trade executed successfully",{chatId:n,contractAddress:s,transactionHash:l.transactionHash})}catch(t){c.logger.error("Error executing SOL trade",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const p={};t.monitorSolPriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const s=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;c.logger.info("Starting SOL price monitoring",{chatId:t,contractAddress:n.contractAddress,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const a=setInterval((()=>r(void 0,void 0,void 0,(function*(){var r;try{const o=yield(0,i.getTokenInfoFromDexscreener)(n.contractAddress,d.BlockchainType.SOL);if(!o||void 0===o.priceChange)return void c.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress});const h=parseFloat((null===(r=o.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(h))return void c.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:o.priceChange});if(c.logger.debug(`Price Change: ${h}%`,{contractAddress:n.contractAddress,priceChange:h}),h>=n.config.takeProfit||h<=n.config.stopLoss){c.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:h,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),clearInterval(a),delete p[s];const r=yield g.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:o}=r.sol_wallet,i=d.TraderFactory.getTrader(d.BlockchainType.SOL),y=yield i.getTokenBalance(n.contractAddress,o),f=parseFloat(y);if(isNaN(f)||f<=0)return void c.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:y});c.logger.info(`Token Balance: ${f}`,{contractAddress:n.contractAddress,userAddress:o});let m=!1,_=0,b="";for(;_<2&&!m;)try{_++,c.logger.info(`Selling tokens, attempt ${_}`,{contractAddress:n.contractAddress,tokenBalance:f});const t=yield i.sellTokens(n.contractAddress,parseFloat(y),o,{slippage:5,gasLimit:1e6});if(t.success)return m=!0,b=t.transactionHash||"",yield(0,l.updateAutoTradeAsSold)(r._id,n.contractAddress,f,"sol"),yield(0,u.updateTradeAsSold)(r._id,n.contractAddress,f,"sol"),c.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:b}),void(e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${b}\n💰 Amount Sold: ${f} tokens\n📊 Price Change: ${h}%`)));c.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){c.logger.error(`Attempt ${_} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return void(m||(c.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${_} attempts.\nPlease try to sell manually.`))))}c.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:h,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss})}catch(e){c.logger.error("Error in monitorSolPriceAndSell",{contractAddress:n.contractAddress,error:e.message})}}))),15e3);p[s]=a})),t.reviewSolTrade=(e,n,s,a)=>r(void 0,void 0,void 0,(function*(){var r;try{const l=yield o.default.findOne({chatId:n,contractAddress:a,blockchain:"sol"});if(!l)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,i.getTokenInfoFromDexscreener)(a,d.BlockchainType.SOL),g=(null==u?void 0:u.priceUsd)||"Unknown",h=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",y=l.isSold?"Yes":"No",p="ongoing"===l.isInitialized?"Active":"pending"===l.isInitialized?"Pending":"Completed",f=`💼 **SOL Trade Details:**\n📝 **Contract:** ${a}\n🚀 **Token:** ${l.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${h}%\n📈 **Take Profit:** ${l.config.takeProfit}%\n📉 **Stop Loss:** ${l.config.stopLoss}%\n💰 **Spend Amount:** ${l.config.spendAmount} SOL\n🔄 **Is Sold:** ${y}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:(0,t.createSolTradeKeyboard)()}),c.logger.debug("SOL trade review displayed",{chatId:n,contractAddress:a,messageId:s})}catch(t){c.logger.error("Error in reviewSolTrade",{chatId:n,contractAddress:a,messageId:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeSolScreen=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const a=yield(0,i.getTokenInfoFromDexscreener)(s,d.BlockchainType.SOL);if(!a)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:l,name:u}=a,g=d.TraderFactory.getTrader(d.BlockchainType.SOL),h=yield g.checkHoneypot(s),y=new o.default({chatId:n,blockchain:"sol",contractAddress:s,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield y.save();let p="💼 **SOL Trading**\n";p+=`📝 **Contract:** ${s}\n`,p+=`🚀 **Token:** ${u} (${l})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/solana/${s})\n\n`,h.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${h.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createSolTradeKeyboard)()});c.logger.info("SOL trade screen displayed",{chatId:n,contractAddress:s,messageId:f.message_id,isHoneypot:h.isHoneypot});const m=a=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=a.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(a.data).command;switch(yield e.answerCallbackQuery(a.id),r){case"snipe_set_take_profit_sol":const r=yield(0,t.askForSolTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateSolTradeConfig)(n,s,"takeProfit",r);break;case"snipe_set_stop_loss_sol":const o=yield(0,t.askForSolTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateSolTradeConfig)(n,s,"stopLoss",o);break;case"snipe_set_spend_amount_sol":const i=yield(0,t.askForSolTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateSolTradeConfig)(n,s,"spendAmount",i);break;case"snipetrade_buy_sol":yield(0,t.executeSolTrade)(e,n,s);break;case"snipe_review_trade_sol":yield(0,t.reviewSolTrade)(e,n,a.message.message_id,s);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){c.logger.error("Error handling SOL trade callback",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){c.logger.error("Error in tradeSolScreen",{chatId:n,contractAddress:s,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the SOL trading screen: ${t.message}`)}}))},6913:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=s(n(8722));t.default=(e,t)=>r(void 0,void 0,void 0,(function*(){const{id:n}=t.chat;try{if(yield a.default.findOne({chat_id:n})){const t="👋 Select a wallet!\n\n";yield e.sendMessage(n,t,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"view_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"view_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}}catch(t){console.error("Error handling wallet selection:",t),yield e.sendMessage(n,"An error occurred while processing your request. Please try again later.")}}))},4046:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=s(n(8722)),o=s(n(6786)),i=n(4653);t.default=(e,t)=>r(void 0,void 0,void 0,(function*(){const{username:n,id:r,first_name:s,last_name:d}=t.chat;let c,l=0,u=null;if(u=yield a.default.findOne({chat_id:r}),u){const t=`👋 Welcome back to EasyBot!\n\nHere are your wallet addresses:\n\n<b>SOL Wallet Address:</b> <code>${u.sol_wallet.address}</code>\n<b>BSC Wallet Address:</b> <code>${u.bsc_wallet.address}</code>\n<b>BASE Wallet Address:</b> <code>${u.base_wallet.address}</code>\n<b>ETH Wallet Address:</b> <code>${u.eth_wallet.address}</code>\n\n<b>Fire /privatekeys to view</b>\n\n`;yield e.sendMessage(r,t,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"view_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"view_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}else{do{const t=(0,i.generateSOLWallet)(),l=(0,i.generateBSCWallet)(),g=(0,i.generateBASEWallet)(),h=(0,i.generateETHWallet)();c=new a.default({chat_id:r,first_name:s,last_name:d,username:n,sol_wallet:{address:t.address,private_key:t.privateKey},bsc_wallet:{address:l.address,private_key:l.privateKey},base_wallet:{address:g.address,private_key:g.privateKey},eth_wallet:{address:h.address,private_key:h.privateKey},trades:{bsc:[],sol:[],eth:[],base:[]}}),u=yield c.save();for(const e of["bsc","sol","eth","base"]){const t=u[`${e}_wallet`].address,n=new o.default({userId:u._id,walletId:t,contractName:"Empty",buyAmount:0,sellAmount:0,takeProfit:0,stopLoss:0});yield n.save(),yield a.default.updateOne({_id:u._id},{$push:{[`trades.${e}`]:n._id}})}const y=`👋 Welcome to EasyBot!\n\nHere are your wallet addresses:\n\n<b>SOL Wallet Address:</b> <code>${u.sol_wallet.address}</code>\n<b>BSC Wallet Address:</b> <code>${u.bsc_wallet.address}</code>\n<b>BASE Wallet Address:</b> <code>${u.base_wallet.address}</code>\n<b>ETH Wallet Address:</b> <code>${u.eth_wallet.address}</code>\n\n<b>Save these private keys below:</b>\n\n<tg-spoiler>SOL: <code>${u.sol_wallet.private_key}</code></tg-spoiler>\n<tg-spoiler>BSC: <code>${u.bsc_wallet.private_key}</code></tg-spoiler>\n<tg-spoiler>BASE: <code>${u.base_wallet.private_key}</code></tg-spoiler>\n<tg-spoiler>ETH: <code>${u.eth_wallet.private_key}</code></tg-spoiler>\n\n<b>To get started, please read our <a href="https://docs.io">docs</a></b>`;yield e.sendMessage(r,y,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"view_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"view_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}while(null===u&&l++<5);null===u&&(yield e.sendMessage(r,"Failed to create your wallet. Please try again later."))}}))},6997:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const a=s(n(7252)),o=s(n(818)),i=s(n(6037)),d=s(n(6209)),c=n(2941),l=n(3433),u=n(108),g=n(5542),h=s(n(4046)),y=s(n(6913)),p=s(n(7307)),f=n(1888),m=n(1213),_=n(8708),b=n(4199),v=n(4545),k=s(n(9907)),w=s(n(857));o.default.config();const S=process.env.TELEGRAM_BOT_TOKEN||"",T=process.env.ADMIN_BOT_TOKEN||"",A=process.env.MONGODB_URI||"mongodb://localhost:27017/mydatabase",O=process.env.SOCKET_PORT||3e3;if(!S||!T)throw new Error("TELEGRAM_BOT_TOKEN or ADMIN_BOT_TOKEN is not defined in the .env file");const E={autoCreate:!0,retryReads:!0},x="true"===process.env.ENABLE_CLUSTERING,M=process.env.NUM_WORKERS?parseInt(process.env.NUM_WORKERS):w.default.cpus().length;if(x&&k.default.isPrimary){m.logger.info(`Master process ${process.pid} is running`);for(let e=0;e<M;e++)k.default.fork();k.default.on("exit",((e,t,n)=>{m.logger.warn(`Worker ${e.process.pid} died with code: ${t} and signal: ${n}`),m.logger.info("Starting a new worker"),k.default.fork()}))}else{const e=(0,a.default)(),t=process.env.PORT||8e3;i.default.connect(A,E).then((()=>r(void 0,void 0,void 0,(function*(){m.logger.info("Connected to MongoDB successfully");const{bot:n}=(()=>{const e=new d.default(S,{polling:!0}),t=(0,b.createBotService)(e);return e.on("callback_query",(t=>r(void 0,void 0,void 0,(function*(){yield(0,u.handleCallbackQuery)(e,t)})))),e.onText(/\/start/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,h.default)(e,t)})))),e.onText(/\/sniper/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,p.default)(e,t)})))),e.onText(/\/wallets/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,y.default)(e,t)})))),e.onText(/\/privatekeys/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,g.privatekeyHandler)(e,t)})))),m.logger.info("Bot initialized with all handlers"),{bot:e,botService:t}})();(()=>{const e=new d.default(T,{polling:!0});(0,b.createBotService)(e);e.on("callback_query",(t=>r(void 0,void 0,void 0,(function*(){yield(0,u.handleCallbackQuery)(e,t)})))),e.onText(/\/start/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,h.default)(e,t)})))),e.onText(/\/sniper/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,p.default)(e,t)})))),e.onText(/\/wallets/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,y.default)(e,t)})))),e.onText(/\/privatekeys/,(t=>r(void 0,void 0,void 0,(function*(){yield(0,g.privatekeyHandler)(e,t)})))),m.logger.info("Admin bot initialized with all handlers")})(),(0,v.initializeServices)(n),(0,f.fetchBalancesForAllChains)(),yield _.cacheService.set("server_start_time",(new Date).toISOString()),m.logger.info("Cache service initialized"),(0,c.loadPreviousValues)(n),(0,c.startMonitoring)(n),(0,l.initializeSocketServer)(Number(O),n),e.get("/",((e,t)=>{t.send("Easy Sniper Bot is running")})),e.get("/health",((e,t)=>r(void 0,void 0,void 0,(function*(){var e;try{const n=yield _.cacheService.get("server_start_time");t.json({status:"ok",uptime:process.uptime(),startTime:n,version:process.env.npm_package_version||"1.0.0",workerId:(null===(e=k.default.worker)||void 0===e?void 0:e.id)||"single"})}catch(e){t.status(500).json({status:"error",message:e.message||"Unknown error"})}})))),e.listen(t,(()=>{var e;m.logger.info(`Server is running on port ${t} (Worker: ${(null===(e=k.default.worker)||void 0===e?void 0:e.id)||"single"})`)})),process.on("SIGTERM",(()=>{m.logger.info("SIGTERM signal received: closing HTTP server"),e.listen().close((()=>{m.logger.info("HTTP server closed"),i.default.connection.close().then((()=>{m.logger.info("MongoDB connection closed"),process.exit(0)}))}))}))})))).catch((e=>{m.logger.error("Error connecting to MongoDB:",{error:e.message})}))}},2724:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBaseDashboard=t.getTokenDetails=t.sellBaseTokenPercentage=t.handleBaseSellAll=t.handleSpendBase=t.handleBaseChart=t.handleBaseRefresh=t.askForBaseToken=t.askForBaseContractAddress=t.handleGenerateNewBaseWallet=t.mainbaseMenu=t.updatebaseMessageWithSellOptions=t.createbaseKeyboard=t.getBaseSellOptionsKeyboard=t.handleBASEScanContract=void 0,t.fetchAndDisplayActiveBaseTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let s=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield w(e,t,r,s)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayCurrentBaseTrade=w;const a=s(n(8722)),o=s(n(6786)),i=n(9558),d=s(n(3883)),c=n(9321),l=n(1347);Object.defineProperty(t,"handleBaseDashboard",{enumerable:!0,get:function(){return l.handleBaseDashboard}});const u=n(4653),g=n(8014),h=n(8014),y=n(4736),p=n(871),f=n(2969);let m={},_={};const b=process.env.BASE_PROVIDER_URL,v=new c.ethers.providers.JsonRpcProvider(b);if(!b)throw new Error("PROVIDER is not defined in the environment variables.");t.handleBASEScanContract=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(!s)throw new Error("Invalid contract address.");const l=(0,y.getCurrentContractAddress)(p.BlockchainType.BASE);l&&m[l]&&delete m[l],m[s]=m[s]||{},(0,y.setCurrentContractAddress)(p.BlockchainType.BASE,s);const u=yield(0,f.checkIfHoneypot)(s,p.BlockchainType.BASE),g=yield(0,i.getTokenInfoFromDexscreener)(s),h=yield(o=s,r(void 0,void 0,void 0,(function*(){try{const e=new c.ethers.Contract(o,d.default,v),t=yield e.decimals(),n=yield e.totalSupply();return c.ethers.utils.formatUnits(n,t)}catch(e){return console.error("Error fetching total supply:",e),null}})));if(null===h)throw new Error("Failed to fetch total supply.");const b=Number(h),k=Number(g.priceUsd)*b,w=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),S=w(b),T=w(k),A=w(Number(g.liquidity.usd)),O=g.priceChange.h1||0,E=O>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",x=Math.ceil(parseFloat(u.buyTax)),M=Math.ceil(parseFloat(u.sellTax)),N=`\n🪙 ${g.symbol} (${g.name}) || 📈 ${O>0?"+":""}${O}%  || 🏦 ${g.dexId} || ${g.chainId}\n\n${E}  Price Change (1h): ${O>0?"+":""}${O}%\n\nCA: ${g.address}\nLP: ${g.pairAddress}\n\n💼 TOTAL SUPPLY: ${S} ${g.symbol}\n🏷️ MC: $${T}\n💧 LIQUIDITY: $${A}\n💵 PRICE: $${g.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${u.risk}\n📉 Buy Tax: ${x}%\n📈 Sell Tax: ${M}%\n\n⚠️⚠️⚠️⚠️ ${u.details||"No specific risks identified"}\n    `,I=yield a.default.findOne({chat_id:n}).exec();I&&(I.baseCurrentTokenName=g.name,I.baseCurrentContractAddress=s,yield I.save()),m[s]={totalSupply:b,marketCap:k,checkHoneypot:u,tokenInfo:g,resultMessage:N};const B=_[n];B&&(yield e.deleteMessage(n,B));const C=yield e.sendMessage(n,N,{parse_mode:"Markdown",reply_markup:(0,t.createbaseKeyboard)()});return _[n]=C.message_id,C.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}var o})),t.getBaseSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_base"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_base"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_base"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_base"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_base"})}]]})),t.createbaseKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_base"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_base"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_base"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_base"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_base"})},{text:"💸 Spend X Base",callback_data:JSON.stringify({command:"spend_base"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updatebaseMessageWithSellOptions=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getBaseSellOptionsKeyboard)(),a=(0,y.getCurrentContractAddress)(p.BlockchainType.BASE);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.mainbaseMenu=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createbaseKeyboard)(),a=(0,y.getCurrentContractAddress)(p.BlockchainType.BASE);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update base menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewBaseWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield a.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,u.generateBASEWallet)();yield a.default.updateOne({chat_id:t},{$set:{"base_wallet.address":n.address,"base_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the base blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,l.handleBaseDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let k={};function w(e,t,n,s,o){return r(this,void 0,void 0,(function*(){const r=n[s],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield a.default.findOneAndUpdate({chat_id:t},{baseCurrentContractAddress:r.contractAddress,baseCurrentTokenName:r.contractName}),(0,y.setCurrentContractAddress)(p.BlockchainType.BASE,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_base"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_base"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_base"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_base"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_base"})},{text:"Next",callback_data:JSON.stringify({command:"next_base"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}function S(e,t){return r(this,void 0,void 0,(function*(){const n=new c.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],v),[r,s]=yield Promise.all([n.balanceOf(t),n.decimals()]);return c.ethers.utils.formatUnits(r,s)}))}t.askForBaseContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,g.INPUT_BASE_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForBaseToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,g.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received Base token:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleBaseRefresh=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,y.getCurrentContractAddress)(p.BlockchainType.BASE);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleBASEScanContract)(e,n,r),s){const t=s.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleBaseChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,y.getCurrentContractAddress)(p.BlockchainType.BASE);if(n){const r=`https://dexscreener.com/base/${n}`,s=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});k[t]={promptId:s.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");k[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendBase=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,h.SET_BASE_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend base:",e),new Error("Failed to handle spend base action.")}})),t.handleBaseSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield a.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.base_wallet,s=n.baseCurrentContractAddress||"";if(!s)throw new Error("No contract address found for user");const o=yield S(s,r),i=parseFloat(o);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=p.TraderFactory.getTrader(p.BlockchainType.BASE),c=yield d.sellTokens(s,100,r);if(c&&c.transactionHash&&c.blockNumber){const{transactionHash:n,blockNumber:r}=c;yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${n}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${n}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling all tokens: ${r}`)}})),t.sellBaseTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:s}=r.base_wallet,o=r.baseCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=yield S(o,s),d=parseFloat(i);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");const c=d*(n/100);if(c<=0)throw new Error("Invalid percentage value");console.log("Amount to sell:",c);const l=p.TraderFactory.getTrader(p.BlockchainType.BASE),u=yield l.sellTokens(o,n,s);if(u&&u.transactionHash&&u.blockNumber){const{transactionHash:r,blockNumber:s}=u;yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${r}\n🧱 Block Number: ${s}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${r}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling tokens: ${r}`)}})),t.getTokenDetails=e=>m[e]},5419:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting token info for ${e} from Basescan`,{contractAddress:e});const t=new s.ethers.Contract(e,["function name() view returns (string)","function symbol() view returns (string)","function decimals() view returns (uint8)","function totalSupply() view returns (uint256)"],d),[n,r,o,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),c=s.ethers.utils.formatUnits(i,o);return a.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:n,symbol:r,decimals:o,totalSupply:c}),{name:n,symbol:r,decimals:o,totalSupply:c}}catch(t){throw a.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new s.ethers.Contract(e,["function balanceOf(address) view returns (uint256)","function decimals() view returns (uint8)"],d),[r,o]=yield Promise.all([n.balanceOf(t),n.decimals()]),i=s.ethers.utils.formatUnits(r,o);return a.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:i}),i}catch(n){throw a.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),n}}))},t.getBaseBalance=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting BASE balance for ${e}`,{walletAddress:e});const t=yield d.getBalance(e),n=s.ethers.utils.formatEther(t);return a.logger.info(`BASE balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw a.logger.error(`Error getting BASE balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.getTransactionReceipt=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting transaction receipt for ${e}`,{txHash:e});const t=yield d.getTransactionReceipt(e);return a.logger.info(`Transaction receipt retrieved for ${e}`,{txHash:e,status:t.status}),t}catch(t){throw a.logger.error(`Error getting transaction receipt for ${e}`,{txHash:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=o.TraderFactory.getTrader(o.BlockchainType.BASE),n=yield t.checkHoneypot(e);return a.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return a.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const s=n(9321),a=n(1213),o=n(871),i=process.env.BASE_PROVIDER_URL||"https://mainnet.base.org",d=(process.env.BASE_SCAN_API_KEY,new s.ethers.providers.JsonRpcProvider(i))},3372:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting token info for ${e} from BSCScan`,{contractAddress:e});const t=new s.ethers.Contract(e,["function name() view returns (string)","function symbol() view returns (string)","function decimals() view returns (uint8)","function totalSupply() view returns (uint256)"],d),[n,r,o,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),c=s.ethers.utils.formatUnits(i,o);return a.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:n,symbol:r,decimals:o,totalSupply:c}),{name:n,symbol:r,decimals:o,totalSupply:c}}catch(t){throw a.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new s.ethers.Contract(e,["function balanceOf(address) view returns (uint256)","function decimals() view returns (uint8)"],d),[r,o]=yield Promise.all([n.balanceOf(t),n.decimals()]),i=s.ethers.utils.formatUnits(r,o);return a.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:i}),i}catch(n){throw a.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),n}}))},t.getBnbBalance=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting BNB balance for ${e}`,{walletAddress:e});const t=yield d.getBalance(e),n=s.ethers.utils.formatEther(t);return a.logger.info(`BNB balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw a.logger.error(`Error getting BNB balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.getTransactionReceipt=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting transaction receipt for ${e}`,{txHash:e});const t=yield d.getTransactionReceipt(e);return a.logger.info(`Transaction receipt retrieved for ${e}`,{txHash:e,status:t.status}),t}catch(t){throw a.logger.error(`Error getting transaction receipt for ${e}`,{txHash:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=o.TraderFactory.getTrader(o.BlockchainType.BSC),n=yield t.checkHoneypot(e);return a.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return a.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const s=n(9321),a=n(1213),o=n(871),i=process.env.BSC_PROVIDER_URL||"https://bsc-dataseed.binance.org/",d=(process.env.BSC_SCAN_API_KEY,new s.ethers.providers.JsonRpcProvider(i))},6395:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting token info for ${e} from Etherscan`,{contractAddress:e});const t=new s.ethers.Contract(e,["function name() view returns (string)","function symbol() view returns (string)","function decimals() view returns (uint8)","function totalSupply() view returns (uint256)"],d),[n,r,o,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),c=s.ethers.utils.formatUnits(i,o);return a.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:n,symbol:r,decimals:o,totalSupply:c}),{name:n,symbol:r,decimals:o,totalSupply:c}}catch(t){throw a.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new s.ethers.Contract(e,["function balanceOf(address) view returns (uint256)","function decimals() view returns (uint8)"],d),[r,o]=yield Promise.all([n.balanceOf(t),n.decimals()]),i=s.ethers.utils.formatUnits(r,o);return a.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:i}),i}catch(n){throw a.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),n}}))},t.getEthBalance=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting ETH balance for ${e}`,{walletAddress:e});const t=yield d.getBalance(e),n=s.ethers.utils.formatEther(t);return a.logger.info(`ETH balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw a.logger.error(`Error getting ETH balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.getTransactionReceipt=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Getting transaction receipt for ${e}`,{txHash:e});const t=yield d.getTransactionReceipt(e);return a.logger.info(`Transaction receipt retrieved for ${e}`,{txHash:e,status:t.status}),t}catch(t){throw a.logger.error(`Error getting transaction receipt for ${e}`,{txHash:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{a.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=o.TraderFactory.getTrader(o.BlockchainType.ETH),n=yield t.checkHoneypot(e);return a.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return a.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const s=n(9321),a=n(1213),o=n(871),i=process.env.ETH_PROVIDER_URL||"https://mainnet.infura.io/v3/your-infura-key",d=(process.env.ETH_SCAN_API_KEY,new s.ethers.providers.JsonRpcProvider(i))},1536:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)},o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.solscan=t.ethscan=t.basescan=t.bscscan=void 0,t.initializeBlockchainServices=function(){const{logger:e}=n(1213);e.info("Initializing blockchain services..."),e.info("All blockchain services initialized successfully")},a(n(3003),t),a(n(6580),t),a(n(2969),t);const i=o(n(3372)),d=o(n(5419)),c=o(n(6395)),l=o(n(5150));t.bscscan=i,t.basescan=d,t.ethscan=c,t.solscan=l},5150:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Getting token info for ${e} from Solscan`,{contractAddress:e});const t=new a.PublicKey(e),n=yield(0,o.getMint)(g,t);let r="Unknown",s="Unknown";try{const t=yield i.default.get(`${u}/token/${e}`);t.data&&t.data.name&&(r=t.data.name,s=t.data.symbol||"Unknown")}catch(t){d.logger.warn(`Could not fetch token metadata from Solscan API for ${e}`,{contractAddress:e,error:t.message})}const c=(Number(n.supply)/Math.pow(10,n.decimals)).toString();return d.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:r,symbol:s,decimals:n.decimals,supply:c}),{name:r,symbol:s,decimals:n.decimals,supply:c}}catch(t){throw d.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new a.PublicKey(e),r=new a.PublicKey(t),s=yield g.getTokenAccountsByOwner(r,{mint:n});if(0===s.value.length)return d.logger.info(`No token account found for ${e} in wallet ${t}`,{contractAddress:e,walletAddress:t}),"0";const i=yield(0,o.getAccount)(g,s.value[0].pubkey),c=yield(0,o.getMint)(g,n),l=(Number(i.amount)/Math.pow(10,c.decimals)).toString();return d.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:l}),l}catch(n){return d.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),"0"}}))},t.getSolBalance=function(e){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Getting SOL balance for ${e}`,{walletAddress:e});const t=new a.PublicKey(e),n=((yield g.getBalance(t))/a.LAMPORTS_PER_SOL).toString();return d.logger.info(`SOL balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw d.logger.error(`Error getting SOL balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=c.TraderFactory.getTrader(c.BlockchainType.SOL),n=yield t.checkHoneypot(e);return d.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return d.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const a=n(8491),o=n(7018),i=s(n(8938)),d=n(1213),c=n(871),l=process.env.SOL_PROVIDER_URL||"https://api.mainnet-beta.solana.com",u="https://api.solscan.io",g=new a.Connection(l)},4199:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BotService=void 0,t.createBotService=function(e){return new a(e)};const s=n(1213);class a{constructor(e){this.messageStore=new Map,this.bot=e,s.logger.info("Bot service initialized")}sendMessage(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield this.bot.sendMessage(e,t,n);return s.logger.debug(`Message sent to ${e}`,{messageId:r.message_id}),r}catch(t){throw s.logger.error(`Failed to send message to ${e}`,{error:t.message,chatId:e}),t}}))}editMessage(e,t,n,a){return r(this,void 0,void 0,(function*(){try{const r=yield this.bot.editMessageText(n,Object.assign({chat_id:e,message_id:t},a));return s.logger.debug(`Message ${t} edited in chat ${e}`),r}catch(n){throw s.logger.error(`Failed to edit message ${t} in chat ${e}`,{error:n.message,chatId:e,messageId:t}),n}}))}deleteMessage(e,t){return r(this,void 0,void 0,(function*(){try{const n=yield this.bot.deleteMessage(e,t);return s.logger.debug(`Message ${t} deleted from chat ${e}`),n}catch(n){return s.logger.error(`Failed to delete message ${t} from chat ${e}`,{error:n.message,chatId:e,messageId:t}),!1}}))}storeMessageId(e,t,n){this.messageStore.has(e)||this.messageStore.set(e,new Map),this.messageStore.get(e).set(t,n)}getStoredMessageId(e,t){const n=this.messageStore.get(e);if(n)return n.get(t)}createBlockchainKeyboard(){return{inline_keyboard:[[{text:"🔷 BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"🔶 ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"🔵 BASE",callback_data:JSON.stringify({command:"view_base"})},{text:"⚪ SOL",callback_data:JSON.stringify({command:"view_sol"})}],[{text:"⚙️ Settings",callback_data:JSON.stringify({command:"show_config"})}]]}}createBlockchainSpecificKeyboard(e){return{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:`scan_contract_${e}`})},{text:"👛 Generate Wallet",callback_data:JSON.stringify({command:`generate_new_${e}_wallet`})}],[{text:"🔫 Snipe Token",callback_data:JSON.stringify({command:`scan_snipe_${e}`})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:`active_trades_${e}`})}],[{text:"🏠 Main Menu",callback_data:JSON.stringify({command:"main_menu"})}]]}}getBotInstance(){return this.bot}}t.BotService=a},108:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleCallbackQuery=function(e,t){return r(this,void 0,void 0,(function*(){var n,s;try{const b=null===(n=t.message)||void 0===n?void 0:n.chat.id,v=null===(s=t.message)||void 0===s?void 0:s.message_id;if(!b||!v)return void a.logger.warn("Invalid callback query: missing chat ID or message ID");const k=new o.BotService(e),w=JSON.parse(t.data||"{}").command;switch(a.logger.debug(`Processing callback query: ${w}`,{chatId:b,messageId:v}),w){case"dismiss_message":yield function(e,t,n){return r(this,void 0,void 0,(function*(){try{yield e.deleteMessage(t,n)}catch(e){a.logger.error("Error dismissing message:",{error:e.message,chatId:t,messageId:n})}}))}(k,b,v);break;case"show_config":yield(0,h.showConfigKeyboard)(e,b);break;case"view_bsc":case"view_base":case"view_sol":case"view_eth":yield function(e,t,n){return r(this,void 0,void 0,(function*(){const r=n.split("_")[1];try{switch(yield d.default.updateOne({chat_id:t},{$set:{currentBlockchain:r}}),r){case"bsc":yield(0,c.handleBscDashboard)(e,t);break;case"base":yield(0,u.handleBaseDashboard)(e,t);break;case"eth":yield(0,l.handleEthDashboard)(e,t);break;case"sol":yield(0,g.handleSolDashboard)(e,t);break;default:a.logger.warn(`Unknown blockchain: ${r}`,{chatId:t}),yield e.sendMessage(t,"Unknown blockchain. Please try again.")}}catch(n){a.logger.error(`Error handling ${r} view:`,{error:n.message,chatId:t}),yield e.sendMessage(t,`⚠️ Error: ${n instanceof Error?n.message:"Unknown error occurred."}`)}}))}(e,b,w);break;case"generate_new_bsc_wallet":yield(0,c.handleGenerateNewBscWallet)(e,b);break;case"generate_new_eth_wallet":yield(0,l.handleGenerateNewethWallet)(e,b);break;case"generate_new_base_wallet":yield(0,u.handleGenerateNewBaseWallet)(e,b);break;case"generate_new_sol_wallet":yield(0,g.handleGenerateNewSolWallet)(e,b);break;case"scan_contract_bsc":const t=yield(0,c.askForContractAddress)(e,b,v);t?yield(0,c.handleBSCScanContract)(e,b,t):yield k.sendMessage(b,"Invalid contract address.");break;case"scan_contract_eth":const n=yield(0,l.askForEthContractAddress)(e,b,v);n?yield(0,l.handleETHScanContract)(e,b,n):yield k.sendMessage(b,"Invalid contract address.");break;case"scan_contract_base":const s=yield(0,u.askForBaseContractAddress)(e,b,v);s?yield(0,u.handleBASEScanContract)(e,b,s):yield k.sendMessage(b,"Invalid contract address.");break;case"scan_contract_sol":const o=yield(0,g.askForSolContractAddress)(e,b,v);o?yield(0,g.handleSOLScanContract)(e,b,o):yield k.sendMessage(b,"Invalid contract address.");break;case"scan_snipe_bsc":try{const t=yield(0,c.askForContractAddress)(e,b,v);yield d.default.updateOne({chat_id:b},{$set:{currentBlockchain:"bsc"}}),t?yield(0,y.bscSniperScreen)(e,b,t):yield k.sendMessage(b,"Invalid contract address.")}catch(e){a.logger.error("Error during BSC token snipe:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"scan_snipe_eth":try{const t=yield(0,l.askForEthContractAddress)(e,b,v);yield d.default.updateOne({chat_id:b},{$set:{currentBlockchain:"eth"}}),t?yield(0,p.ethSniperScreen)(e,b,t):yield k.sendMessage(b,"Invalid contract address.")}catch(e){a.logger.error("Error during ETH token snipe:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"scan_snipe_base":try{const t=yield(0,u.askForBaseContractAddress)(e,b,v);yield d.default.updateOne({chat_id:b},{$set:{currentBlockchain:"base"}}),t?yield(0,f.baseSniperScreen)(e,b,t):yield k.sendMessage(b,"Invalid contract address.")}catch(e){a.logger.error("Error during BASE token snipe:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"scan_snipe_sol":try{const t=yield(0,g.askForSolContractAddress)(e,b,v);yield d.default.updateOne({chat_id:b},{$set:{currentBlockchain:"sol"}}),t?yield(0,m.solSniperScreen)(e,b,t):yield k.sendMessage(b,"Invalid contract address.")}catch(e){a.logger.error("Error during SOL token snipe:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_bsc":try{const t=yield(0,c.handleSpendBSC)(e,b);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.BSC);if(!e){yield k.sendMessage(b,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:b});if(!n||!n.bsc_wallet||!n.bsc_wallet.address){yield k.sendMessage(b,"No wallet found. Please generate a wallet first.");break}const r=n.bsc_wallet.address,s=i.TraderFactory.getTrader(i.BlockchainType.BSC),a=yield s.buyTokens(e,t,r);if(a&&a.transactionHash&&a.blockNumber){const{transactionHash:e,blockNumber:t}=a;yield k.sendMessage(b,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n🧱 Block Number: ${t}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(b,"Error: Unable to get transaction details.")}else yield k.sendMessage(b,"Invalid spend amount.")}catch(e){a.logger.error("Error purchasing BSC tokens:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_eth":try{const t=yield(0,l.handleSpendeth)(e,b);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.ETH);if(!e){yield k.sendMessage(b,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:b});if(!n||!n.eth_wallet||!n.eth_wallet.address){yield k.sendMessage(b,"No wallet found. Please generate a wallet first.");break}const r=n.eth_wallet.address,s=i.TraderFactory.getTrader(i.BlockchainType.ETH),a=yield s.buyTokens(e,t,r);if(a&&a.transactionHash&&a.blockNumber){const{transactionHash:e,blockNumber:t}=a;yield k.sendMessage(b,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n🧱 Block Number: ${t}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.io/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(b,"Error: Unable to get transaction details.")}else yield k.sendMessage(b,"Invalid spend amount.")}catch(e){a.logger.error("Error purchasing ETH tokens:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_base":try{const t=yield(0,u.handleSpendBase)(e,b);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.BASE);if(!e){yield k.sendMessage(b,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:b});if(!n||!n.base_wallet||!n.base_wallet.address){yield k.sendMessage(b,"No wallet found. Please generate a wallet first.");break}const r=n.base_wallet.address,s=i.TraderFactory.getTrader(i.BlockchainType.BASE),a=yield s.buyTokens(e,t,r);if(a&&a.transactionHash&&a.blockNumber){const{transactionHash:e,blockNumber:t}=a;yield k.sendMessage(b,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n🧱 Block Number: ${t}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(b,"Error: Unable to get transaction details.")}else yield k.sendMessage(b,"Invalid spend amount.")}catch(e){a.logger.error("Error purchasing BASE tokens:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_sol":try{const t=yield(0,g.handleSpendsol)(e,b);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.SOL);if(!e){yield k.sendMessage(b,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:b});if(!n||!n.sol_wallet||!n.sol_wallet.address){yield k.sendMessage(b,"No wallet found. Please generate a wallet first.");break}const r=n.sol_wallet.address,s=i.TraderFactory.getTrader(i.BlockchainType.SOL),a=yield s.buyTokens(e,t,r);if(a&&a.transactionHash){const{transactionHash:e}=a;yield k.sendMessage(b,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(b,"Error: Unable to get transaction details.")}else yield k.sendMessage(b,"Invalid spend amount.")}catch(e){a.logger.error("Error purchasing SOL tokens:",{error:e.message,chatId:b}),yield k.sendMessage(b,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"main_menu":yield function(e,t){return r(this,void 0,void 0,(function*(){try{const n=e.createBlockchainKeyboard();yield e.sendMessage(t,"🏠 *Main Menu*\n\nSelect a blockchain to continue:",{parse_mode:"Markdown",reply_markup:n})}catch(e){a.logger.error("Error showing main menu:",{error:e.message,chatId:t})}}))}(k,b);break;default:a.logger.warn(`Unknown callback command: ${w}`,{chatId:b}),yield k.sendMessage(b,"Unknown command. Please try again.")}yield e.answerCallbackQuery(t.id)}catch(n){a.logger.error("Error handling callback query:",{error:n.message,query:t.data});try{yield e.answerCallbackQuery(t.id,{text:"An error occurred. Please try again.",show_alert:!0})}catch(e){a.logger.error("Error answering callback query:",{error:e.message,queryId:t.id})}}}))};const a=n(1213),o=n(4199),i=n(871),d=s(n(8722)),c=n(3173),l=n(6314),u=n(2724),g=n(4035),h=n(566),y=n(2893),p=n(4248),f=n(8786),m=n(4259),_=n(4736)},3173:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBscDashboard=t.getTokenDetails=t.sellBscTokenPercentage=t.handleBscSellAll=t.handleSpendBSC=t.handleChart=t.handleRefresh=t.askForBSCToken=t.askForContractAddress=t.handleGenerateNewBscWallet=t.mainBscMenu=t.updateBSCMessageWithTradeOptions=t.updateBSCMessageWithSellOptions=t.createBSCKeyboard=t.getSellOptionsKeyboard=t.handleBSCScanContract=void 0,t.fetchAndDisplayActiveBscTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let s=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield S(e,t,r,s)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayBscCurrentTrade=S;const a=s(n(8722)),o=s(n(6786)),i=n(9088),d=n(9558),c=s(n(3883)),l=n(9321),u=n(4544);Object.defineProperty(t,"handleBscDashboard",{enumerable:!0,get:function(){return u.handleBscDashboard}});const g=n(4653),h=n(8014),y=n(8014),p=n(4736),f=n(8485),m=n(871);let _={},b={};const v=process.env.BSC_PROVIDER_URL;if(!v)throw new Error("BSC_PROVIDER_URL is not defined in the environment variables.");const k=new l.ethers.providers.JsonRpcProvider(v);t.handleBSCScanContract=(e,n,s)=>r(void 0,void 0,void 0,(function*(){var o,u;try{if(!s)throw new Error("Invalid contract address.");const g=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);g&&_[g]&&delete _[g],_[s]=_[s]||{},(0,p.setCurrentContractAddress)(m.BlockchainType.BSC,s);const h=yield(0,i.checkEVMHoneypot)(s,m.BlockchainType.BSC),y=yield(0,d.getTokenInfoFromDexscreener)(s),f=yield(u=s,r(void 0,void 0,void 0,(function*(){try{const e=new l.ethers.Contract(u,c.default,k),t=yield e.decimals(),n=yield e.totalSupply();return l.ethers.utils.formatUnits(n,t)}catch(e){return console.error("Error fetching total supply:",e),null}})));if(null===f)throw new Error("Failed to fetch total supply.");const v=Number(f),w=Number(y.priceUsd)*v,S=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),T=S(v),A=S(w),O=S(Number(y.liquidity.usd)),E=(null===(o=y.priceChange)||void 0===o?void 0:o.h1)||0,x=E>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",M=Math.ceil(parseFloat(String(h.buyTax||0))),N=Math.ceil(parseFloat(String(h.sellTax||0))),I=`\n🪙 ${y.symbol} (${y.name}) || 📈 ${E>0?"+":""}${E}%  || 🏦 ${y.dexId} || ${y.chainId}\n\n${x}  Price Change: ${E>0?"+":""}${E}%\n\nCA: ${y.address}\nLP: ${y.pairAddress}\n\n💼 TOTAL SUPPLY: ${T} ${y.symbol}\n🏷️ MC: $${A}\n💧 LIQUIDITY: $${O}\n💵 PRICE: $${y.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${h.isHoneypot?"HIGH":"LOW"}\n📉 Buy Tax: ${M}%\n📈 Sell Tax: ${N}%\n\n⚠️⚠️⚠️⚠️ ${h.honeypotReason||"No specific risks identified"}\n    `,B=yield a.default.findOne({chat_id:n}).exec();B&&(B.bscCurrentTokenName=y.name,B.bscCurrentContractAddress=s,yield B.save()),_[s]={totalSupply:v,marketCap:w,checkHoneypot:h,tokenInfo:y,resultMessage:I};const C=b[n];C&&(yield e.deleteMessage(n,C));const $=yield e.sendMessage(n,I,{parse_mode:"Markdown",reply_markup:(0,t.createBSCKeyboard)()});return b[n]=$.message_id,$.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}})),t.getSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_bsc"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_bsc"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_bsc"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_bsc"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_bsc"})}]]})),t.createBSCKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_bsc"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_bsc"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_bsc"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_bsc"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_bsc"})},{text:"💸 Spend X BSC",callback_data:JSON.stringify({command:"spend_bsc"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updateBSCMessageWithSellOptions=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getSellOptionsKeyboard)(),a=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.updateBSCMessageWithTradeOptions=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,f.createBscTradeKeyboard)();if(!(0,p.getCurrentContractAddress)(m.BlockchainType.BSC))return void console.error("No contract address found in global state.");yield e.editMessageReplyMarkup({inline_keyboard:r.inline_keyboard},{chat_id:t,message_id:n})}catch(e){console.error("Failed to update trade options:",e)}})),t.mainBscMenu=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createBSCKeyboard)(),a=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update BSC menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewBscWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield a.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,g.generateBSCWallet)();yield a.default.updateOne({chat_id:t},{$set:{"bsc_wallet.address":n.address,"bsc_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the BSC blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,u.handleBscDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let w={};function S(e,t,n,s,o){return r(this,void 0,void 0,(function*(){const r=n[s],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield a.default.findOneAndUpdate({chat_id:t},{bscCurrentContractAddress:r.contractAddress,bscCurrentTokenName:r.contractName}),(0,p.setCurrentContractAddress)(m.BlockchainType.BSC,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_bsc"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_bsc"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_bsc"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_bsc"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_bsc"})},{text:"Next",callback_data:JSON.stringify({command:"next_bsc"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}function T(e,t){return r(this,void 0,void 0,(function*(){const n=new l.ethers.providers.JsonRpcProvider("https://bsc-dataseed.binance.org/"),r=new l.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[s,a]=yield Promise.all([r.balanceOf(t),r.decimals()]);return l.ethers.utils.formatUnits(s,a)}))}t.askForContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(w[t]){const n=w[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,h.INPUT_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return w[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),w[t]=Object.assign(Object.assign({},w[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForBSCToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(w[t]){const n=w[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,y.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return w[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received BSC token:",e.text),w[t]=Object.assign(Object.assign({},w[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleRefresh=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleBSCScanContract)(e,n,r),s){const t=s.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(n){const r=`https://dexscreener.com/bsc/${n}`,s=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});w[t]={promptId:s.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");w[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendBSC=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,y.SET_BSC_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return w[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),w[t]=Object.assign(Object.assign({},w[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend BSC:",e),new Error("Failed to handle spend BSC action.")}})),t.handleBscSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield a.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.bsc_wallet,s=n.bscCurrentContractAddress||"";if(!s)throw new Error("No contract address found for user");const o=yield T(s,r),i=parseFloat(o);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=m.TraderFactory.getTrader(m.BlockchainType.BSC),c=yield d.sellTokens(s,100,r);if(c&&c.transactionHash&&c.blockNumber){const{transactionHash:n,blockNumber:r}=c;yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${n}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${n}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n),n instanceof Error&&n.message,yield e.sendMessage(t,"An error occurred while purchasing tokens: Possible causes are selling less than required amount and unstable network.")}})),t.sellBscTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:s}=r.bsc_wallet,o=r.bscCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=yield T(o,s),d=parseFloat(i);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");const c=d*(n/100);if(c<=0)throw new Error("Invalid percentage value");console.log("Amount to sell:",c);const l=m.TraderFactory.getTrader(m.BlockchainType.BSC),u=yield l.sellTokens(o,n,s);if(u&&u.transactionHash&&u.blockNumber){const{transactionHash:r,blockNumber:s}=u;yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${r}\n🧱 Block Number: ${s}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${r}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n),n instanceof Error&&n.message,yield e.sendMessage(t,"An error occurred while purchasing tokens: Possible causes are selling less than required amount and unstable network.")}})),t.getTokenDetails=e=>_[e]},8708:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.cacheService=t.CacheFactory=void 0;const s=n(1213);class a{constructor(){this.cache=new Map,this.cleanupInterval=setInterval((()=>this.cleanupExpiredItems()),3e5)}cleanupExpiredItems(){const e=Date.now();let t=0;for(const[n,r]of this.cache.entries())r.expiry&&r.expiry<e&&(this.cache.delete(n),t++);t>0&&s.logger.debug(`Cache cleanup: removed ${t} expired items`)}get(e){return r(this,void 0,void 0,(function*(){const t=this.cache.get(e);return t?t.expiry&&t.expiry<Date.now()?(this.cache.delete(e),null):t.value:null}))}set(e,t,n){return r(this,void 0,void 0,(function*(){const r=n?Date.now()+1e3*n:null;this.cache.set(e,{value:t,expiry:r})}))}del(e){return r(this,void 0,void 0,(function*(){this.cache.delete(e)}))}exists(e){return r(this,void 0,void 0,(function*(){const t=this.cache.get(e);return!(!t||t.expiry&&t.expiry<Date.now()&&(this.cache.delete(e),1))}))}flushAll(){return r(this,void 0,void 0,(function*(){this.cache.clear()}))}getMulti(e){return r(this,void 0,void 0,(function*(){return Promise.all(e.map((e=>this.get(e))))}))}setMulti(e){return r(this,void 0,void 0,(function*(){for(const t of e)yield this.set(t.key,t.value,t.ttlSeconds)}))}delMulti(e){return r(this,void 0,void 0,(function*(){for(const t of e)this.cache.delete(t)}))}shutdown(){clearInterval(this.cleanupInterval)}}class o{static getCache(){return this.instance||("true"===process.env.USE_REDIS?(s.logger.info("Redis cache is enabled in config but not fully implemented yet. Using in-memory cache instead."),this.instance=new a):(s.logger.info("Using in-memory cache"),this.instance=new a)),this.instance}static shutdown(){this.instance instanceof a&&this.instance.shutdown()}}t.CacheFactory=o,t.cacheService=o.getCache()},3868:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleCallbackQuery=void 0;const a=s(n(9896)),o=s(n(6928)),i=n(8491),d=s(n(8722)),c=s(n(6786)),l=s(n(8087)),u=n(4544),g=n(3173),h=n(3173),y=n(2893),p=n(4248),f=n(4259),m=n(8786),_=n(8485),b=n(871),v=n(1347),k=n(2724),w=n(9266),S=n(4035),T=n(8322),A=n(6314),O=n(439),E=n(4736),x=n(2836),M=n(9527),N=n(566),I=o.default.resolve(__dirname,"../previousValues.json");function B(e,t){var n;const r=JSON.parse(a.default.readFileSync(I,"utf-8"));for(const[s,a]of Object.entries(r)){const r=a;if((null===(n=r[t])||void 0===n?void 0:n.chat_id)===e)return{address:s,balance:r[t].balance}}return null}new i.Connection(process.env.SOL_PROVIDER_URL);let C=0;t.handleCallbackQuery=(e,t)=>r(void 0,void 0,void 0,(function*(){var n,s;const a=null===(n=t.message)||void 0===n?void 0:n.chat.id,o=null===(s=t.message)||void 0===s?void 0:s.message_id;if(!a||!o)return;const i=JSON.parse(t.data||"{}");switch(i.command){case"dismiss_message":yield $(e,a,o);break;case"show_config":yield(0,N.showConfigKeyboard)(e,a);break;case"view_bsc":case"view_base":case"view_sol":case"view_eth":yield P(e,a,i.command);break;case"generate_new_bsc_wallet":yield(0,h.handleGenerateNewBscWallet)(e,a);break;case"scan_contract_bsc":const n=yield(0,h.askForContractAddress)(e,a,o);n?yield(0,g.handleBSCScanContract)(e,a,n):yield e.sendMessage(a,"Invalid contract address.");break;case"scan_snipe_bsc":try{const t=yield(0,h.askForBSCToken)(e,a,o);yield d.default.updateOne({chat_id:a},{$set:{currentBlockchain:"bsc"}}),t?yield(0,y.bscSniperScreen)(e,a,t):yield e.sendMessage(a,"Invalid contract address.")}catch(t){console.error("Error during BSC token snipe:",t),yield e.sendMessage(a,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"snipetrade_dashboard_bsc":const s=(0,E.getCurrentContractAddress)(b.BlockchainType.BSC)||"";yield(0,_.tradeBscScreen)(e,a,s);break;case"sell_bsc":yield(0,h.updateBSCMessageWithSellOptions)(e,a,o);break;case"active_trades_bsc":yield(0,h.fetchAndDisplayActiveBscTrades)(e,a,"bsc");break;case"previous_bsc":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"bsc"}).exec();yield(0,h.displayBscCurrentTrade)(e,a,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_bsc":const u=yield c.default.find({isSold:!1,blockchain:"bsc"}).exec();C<u.length-1?(C++,yield(0,h.displayBscCurrentTrade)(e,a,u,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_bsc":yield(0,h.handleRefresh)(e,a);break;case"chart_bsc":yield(0,h.handleChart)(e,a);break;case"spend_bsc":try{const t=yield(0,h.handleSpendBSC)(e,a);if(t){const n=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const s=b.TraderFactory.getTrader(b.BlockchainType.BSC),a=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});if(!a)throw new Error("Snipe configuration not found");const o=(null===(n=a.config.spend_bsc)||void 0===n?void 0:n.toString())||"0.1",i={slippage:a.config.slippage_bsc||5,gasLimit:1e6},c=yield s.buyTokens(t,o,r.bsc_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return{transactionHash:c.transactionHash,blockNumber:c.blockNumber,status:!0}}catch(e){throw e}}))}(a,t);if(n&&n.transactionHash&&n.blockNumber){const{transactionHash:t,blockNumber:r}=n;yield e.sendMessage(a,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${t}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${t}\nTo see your transaction`)}else yield e.sendMessage(a,"Error: Unable to get transaction details.")}else yield e.sendMessage(a,"Invalid contract address.")}catch(t){if(console.error("Error purchasing tokens:",t),t instanceof Error)if(t.message.includes("insufficient funds"))yield e.sendMessage(a,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again.");else if(t.message.includes("SERVER_ERROR")||t.message.includes("ECONNRESET"))yield e.sendMessage(a,"Error purchasing tokens: There was a problem processing your request. Please try again later.");else if(t.message.includes("body"))try{const n=JSON.parse(t.message);n.error&&n.error.message.includes("insufficient funds")?yield e.sendMessage(a,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again."):yield e.sendMessage(a,"An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.")}catch(t){yield e.sendMessage(a,"An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.")}else yield e.sendMessage(a,"An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.");else yield e.sendMessage(a,"An unknown error occurred while purchasing tokens. Please try again later.")}break;case"sell_all_bsc":yield(0,g.handleBscSellAll)(e,a);break;case"sell_25_bsc":yield(0,g.sellBscTokenPercentage)(e,a,25);break;case"sell_50_bsc":yield(0,g.sellBscTokenPercentage)(e,a,50);break;case"sell_75_bsc":yield(0,g.sellBscTokenPercentage)(e,a,75);break;case"refresh_wallet_bsc":try{const t=yield d.default.findOne({chat_id:a});if(t){const n=B(a,"bsc");if(n){const{address:r}=n,s=t.bsc_dashboard_message_id,o=t.bsc_dashboard_content,i=t.bsc_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your BSC Dashboard!\n                          \n📍 **Address:** \`${r}\`\n                          \n🔗 **Blockchain:** BSC\n                          \n💰 **Balance:** \`${c} BSC\`\n                         `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_bsc"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_bsc"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_bsc"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_bsc"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_bsc_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:a,message_id:s,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:a},{$set:{base_dashboard_content:l,base_dashboard_markup:u}}))}else yield e.sendMessage(a,"No wallet address found.")}else yield e.sendMessage(a,"User not found.")}catch(t){console.error("Failed to refresh wallet base:",t),yield e.sendMessage(a,"An error occurred while refreshing your wallet.")}break;case"generate_new_base_wallet":yield(0,k.handleGenerateNewBaseWallet)(e,a);break;case"snipetrade_dashboard_base":yield(0,w.tradeBaseScreen)(e,a,(0,E.getCurrentContractAddress)(b.BlockchainType.BASE)||"");break;case"scan_contract_base":const v=yield(0,k.askForBaseContractAddress)(e,a,o);v?yield(0,k.handleBASEScanContract)(e,a,v):yield e.sendMessage(a,"Invalid contract address.");break;case"scan_snipe_base":try{const t=yield(0,k.askForBaseToken)(e,a,o);yield d.default.updateOne({chat_id:a},{$set:{currentBlockchain:"base"}}),t?yield(0,m.baseSniperScreen)(e,a,t):yield e.sendMessage(a,"Invalid contract address.")}catch(t){console.error("Error during Base token snipe:",t),yield e.sendMessage(a,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"sell_base":yield(0,k.updatebaseMessageWithSellOptions)(e,a,o);break;case"back_base":yield(0,k.mainbaseMenu)(e,a,o);break;case"active_trades_base":yield(0,k.fetchAndDisplayActiveBaseTrades)(e,a,"base");break;case"previous_base":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"base"}).exec();yield(0,k.displayCurrentBaseTrade)(e,a,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_base":const T=yield c.default.find({isSold:!1,blockchain:"base"}).exec();C<T.length-1?(C++,yield(0,k.displayCurrentBaseTrade)(e,a,T,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_base":yield(0,k.handleBaseRefresh)(e,a);break;case"chart_base":yield(0,k.handleBaseChart)(e,a);break;case"spend_base":try{const t=yield(0,k.handleSpendBase)(e,a);if(t){const n=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const s=b.TraderFactory.getTrader(b.BlockchainType.BASE),a=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"base"});if(!a)throw new Error("Snipe configuration not found");const o=(null===(n=a.config.spend_base)||void 0===n?void 0:n.toString())||"0.1",i={slippage:a.config.slippage_base||5,gasLimit:1e6},c=yield s.buyTokens(t,o,r.base_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return{transactionHash:c.transactionHash,blockNumber:c.blockNumber,status:!0}}catch(e){throw e}}))}(a,t);if(n&&n.transactionHash&&n.blockNumber){const{transactionHash:t,blockNumber:r}=n;yield e.sendMessage(a,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${t}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${t}\nTo see your transaction`)}else yield e.sendMessage(a,"Error: Unable to get transaction details.")}else yield e.sendMessage(a,"Invalid contract address.")}catch(t){console.error("Error purchasing tokens:",t),t instanceof Error?t.message.includes("Insufficient BNB balance")?yield e.sendMessage(a,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost."):t.message.includes("SERVER_ERROR")||t.message.includes("ECONNRESET")?yield e.sendMessage(a,"Error purchasing tokens: There was a problem processing your request. Please try again later."):yield e.sendMessage(a,"An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network."):yield e.sendMessage(a,"An unknown error occurred while purchasing tokens. Please try again later.")}break;case"sell_all_base":yield(0,k.handleBaseSellAll)(e,a);break;case"sell_25_base":yield(0,k.sellBaseTokenPercentage)(e,a,25);break;case"sell_50_base":yield(0,k.sellBaseTokenPercentage)(e,a,50);break;case"sell_75_base":yield(0,k.sellBaseTokenPercentage)(e,a,75);break;case"refresh_wallet_base":try{const t=yield d.default.findOne({chat_id:a});if(t){const n=B(a,"base");if(n){const{address:r}=n,s=t.base_dashboard_message_id,o=t.base_dashboard_content,i=t.base_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your BASE Dashboard!\n                            \n📍 **Address:** \`${r}\`\n                            \n🔗 **Blockchain:** BASE\n                            \n💰 **Balance:** \`${c} Base ETH\`\n                           `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_base"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_base"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_base"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_base"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_base_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:a,message_id:s,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:a},{$set:{base_dashboard_content:l,base_dashboard_markup:u}}))}else yield e.sendMessage(a,"No wallet address found.")}else yield e.sendMessage(a,"User not found.")}catch(t){console.error("Failed to refresh wallet base:",t),yield e.sendMessage(a,"An error occurred while refreshing your wallet.")}break;case"generate_new_eth_wallet":yield(0,A.handleGenerateNewethWallet)(e,a);break;case"snipetrade_dashboard_eth":yield(0,x.tradeEthScreen)(e,a,(0,E.getCurrentContractAddress)(b.BlockchainType.ETH)||"");break;case"scan_contract_eth":const O=yield(0,A.askForEthContractAddress)(e,a,o);O?yield(0,A.handleETHScanContract)(e,a,O):yield e.sendMessage(a,"Invalid contract address.");break;case"scan_snipe_eth":try{const t=yield(0,A.askForEthToken)(e,a,o);yield d.default.updateOne({chat_id:a},{$set:{currentBlockchain:"eth"}}),t?yield(0,p.ethSniperScreen)(e,a,t):yield e.sendMessage(a,"Invalid contract address.")}catch(t){console.error("Error during Eth token snipe:",t),yield e.sendMessage(a,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"sell_eth":yield(0,A.updateEthMessageWithSellOptions)(e,a,o);break;case"trade_eth":yield(0,A.updateEthMessageWithTradeOptions)(e,a,o);break;case"back_eth":yield(0,A.mainEthMenu)(e,a,o);break;case"active_trades_eth":yield(0,A.fetchAndDisplayActiveEthTrades)(e,a,"eth");break;case"previous_eth":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"eth"}).exec();yield(0,A.displayCurrentEthTrade)(e,a,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_eth":const I=yield c.default.find({isSold:!1,blockchain:"eth"}).exec();C<I.length-1?(C++,yield(0,A.displayCurrentEthTrade)(e,a,I,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_eth":yield(0,A.handleEthRefresh)(e,a);break;case"chart_eth":yield(0,A.handleEthChart)(e,a);break;case"spend_eth":try{const t=yield(0,A.handleSpendeth)(e,a);if(t){const n=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const s=b.TraderFactory.getTrader(b.BlockchainType.ETH),a=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"});if(!a)throw new Error("Snipe configuration not found");const o=(null===(n=a.config.spend_eth)||void 0===n?void 0:n.toString())||"0.1",i={slippage:a.config.slippage_eth||5,gasLimit:1e6},c=yield s.buyTokens(t,o,r.eth_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return{transactionHash:c.transactionHash,blockNumber:c.blockNumber,status:!0}}catch(e){throw e}}))}(a,t);if(n&&n.transactionHash&&n.blockNumber){const{transactionHash:t,blockNumber:r}=n;yield e.sendMessage(a,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${t}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.com/tx/${t}\nTo see your transaction`)}else yield e.sendMessage(a,"Error: Unable to get transaction details.")}else yield e.sendMessage(a,"Invalid contract address.")}catch(t){console.error("Error purchasing tokens:",t),t instanceof Error?t.message.includes("Insufficient Eth balance")?yield e.sendMessage(a,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost."):t.message.includes("SERVER_ERROR")||t.message.includes("ECONNRESET")?yield e.sendMessage(a,"Error purchasing tokens: There was a problem processing your request. Please try again later."):yield e.sendMessage(a,"An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network."):yield e.sendMessage(a,"An unknown error occurred while purchasing tokens. Please try again later.")}break;case"sell_all_eth":yield(0,A.handleEthSellAll)(e,a);break;case"sell_25_eth":yield(0,A.sellethTokenPercentage)(e,a,25);break;case"sell_50_eth":yield(0,A.sellethTokenPercentage)(e,a,50);break;case"sell_75_eth":yield(0,A.sellethTokenPercentage)(e,a,75);break;case"refresh_wallet_eth":try{const t=yield d.default.findOne({chat_id:a});if(t){const n=B(a,"eth");if(n){const{address:r}=n,s=t.eth_dashboard_message_id,o=t.eth_dashboard_content,i=t.eth_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your ETH Dashboard!\n                          \n📍 **Address:** \`${r}\`\n                          \n🔗 **Blockchain:** ETH\n                          \n💰 **Balance:** \`${c}  ETH\`\n                         `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_eth"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_eth"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_eth"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_eth"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_eth_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:a,message_id:s,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:a},{$set:{eth_dashboard_content:l,eth_dashboard_markup:u}}))}else yield e.sendMessage(a,"No wallet address found.")}else yield e.sendMessage(a,"User not found.")}catch(t){console.error("Failed to refresh wallet et:",t),yield e.sendMessage(a,"An error occurred while refreshing your wallet.")}break;case"generate_new_sol_wallet":yield(0,S.handleGenerateNewSolWallet)(e,a);break;case"snipetrade_dashboard_sol":yield(0,M.tradeSolScreen)(e,a,(0,E.getCurrentContractAddress)(b.BlockchainType.SOL)||"");break;case"scan_contract_sol":const F=yield(0,S.askForSolContractAddress)(e,a,o);F?yield(0,S.handleSOLScanContract)(e,a,F):yield e.sendMessage(a,"Invalid contract address.");break;case"scan_snipe_sol":try{const t=yield(0,S.askForSolToken)(e,a,o);yield d.default.updateOne({chat_id:a},{$set:{currentBlockchain:"sol"}}),t?yield(0,f.solSniperScreen)(e,a,t):yield e.sendMessage(a,"Invalid contract address.")}catch(t){console.error("Error during Sol token snipe:",t),yield e.sendMessage(a,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"sell_sol":yield(0,S.updateSolMessageWithSellOptions)(e,a,o);break;case"trade_sol":yield(0,S.updateSolMessageWithTradeOptions)(e,a,o);break;case"back_sol":yield(0,S.mainSolMenu)(e,a,o);break;case"active_trades_sol":yield(0,S.fetchAndDisplayActiveSolTrades)(e,a,"sol");break;case"previous_sol":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"sol"}).exec();yield(0,S.displayCurrentSolTrade)(e,a,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_sol":const D=yield c.default.find({isSold:!1,blockchain:"sol"}).exec();C<D.length-1?(C++,yield(0,S.displayCurrentSolTrade)(e,a,D,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_sol":yield(0,S.handleSolRefresh)(e,a);break;case"chart_sol":yield(0,S.handleSolChart)(e,a);break;case"spend_sol":try{const t=yield(0,S.handleSpendsol)(e,a);if(!t||isNaN(Number(t)))throw new Error("Invalid amount.");const n=t,s=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const s=b.TraderFactory.getTrader(b.BlockchainType.SOL),a=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"});if(!a)throw new Error("Snipe configuration not found");const o=(null===(n=a.config.spend_sol)||void 0===n?void 0:n.toString())||"0.1",i={slippage:a.config.slippage_sol||5},c=yield s.buyTokens(t,o,r.sol_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return c.transactionHash||""}catch(e){throw e}}))}(a,n);s?yield e.sendMessage(a,`✅ Successfully purchased tokens:\n\n🔗 Transaction Hash: ${s}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${s}\nTo see your transaction`):yield e.sendMessage(a,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(t){console.error("Error purchasing tokens:",t),t instanceof Error&&t.message,yield e.sendMessage(a,"An error occurred while purchasing tokens}")}break;case"sell_all_sol":yield(0,S.handleSolSellAll)(e,a);break;case"sell_25_sol":yield(0,S.sellSolTokenPercentage)(e,a,25);break;case"sell_50_sol":yield(0,S.sellSolTokenPercentage)(e,a,50);break;case"sell_75_sol":yield(0,S.sellSolTokenPercentage)(e,a,75);break;case"refresh_wallet_sol":try{const t=yield d.default.findOne({chat_id:a});if(t){const n=B(a,"sol");if(n){const{address:r}=n,s=t.sol_dashboard_message_id,o=t.sol_dashboard_content,i=t.sol_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your SOL Dashboard!\n                          \n📍 **Address:** \`${r}\`\n                          \n🔗 **Blockchain:** SOL\n                          \n💰 **Balance:** \`${c}  SOL\`\n                         `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_sol"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_sol"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_sol"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_sol"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_sol_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:a,message_id:s,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:a},{$set:{sol_dashboard_content:l,sol_dashboard_markup:u}}))}else yield e.sendMessage(a,"No wallet address found.")}else yield e.sendMessage(a,"User not found.")}catch(t){console.error("Failed to refresh wallet et:",t),yield e.sendMessage(a,"An error occurred while refreshing your wallet.")}}}));const $=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{yield e.deleteMessage(t,n)}catch(e){console.error("Failed to delete message:",e)}})),P=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const r=n.split("_")[1];switch(yield d.default.updateOne({chat_id:t},{$set:{currentBlockchain:r}}),r){case"bsc":yield(0,u.handleBscDashboard)(e,t);break;case"base":yield(0,v.handleBaseDashboard)(e,t);break;case"eth":yield(0,O.handleEthDashboard)(e,t);break;case"sol":yield(0,T.handleSolDashboard)(e,t)}}))},9088:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.checkEVMHoneypot=function(e,t){return r(this,void 0,void 0,(function*(){try{o.logger.info(`Checking if ${e} is a honeypot on ${t}`,{contractAddress:e,blockchain:t});const n={isHoneypot:!1,buyTax:0,sellTax:0,isLiquidityLocked:!1};let r="";switch(t){case a.BlockchainType.BSC:r=process.env.BSC_PROVIDER_URL||"https://bsc-dataseed.binance.org/";break;case a.BlockchainType.ETH:r=process.env.ETH_PROVIDER_URL||"https://mainnet.infura.io/v3/your-infura-key";break;case a.BlockchainType.BASE:r=process.env.BASE_PROVIDER_URL||"https://mainnet.base.org";break;default:throw new Error(`Unsupported blockchain: ${t}`)}const i=new s.ethers.providers.JsonRpcProvider(r),d=yield i.getCode(e);return"0x"===d?(n.isHoneypot=!0,n.honeypotReason="Contract does not exist",n):d.includes("function transferFrom(address,address,uint256)")&&!d.includes("function transfer(address,uint256)")?(n.isHoneypot=!0,n.honeypotReason="Missing transfer function",n):d.includes("blacklist")||d.includes("_blacklist")?(n.isHoneypot=!0,n.honeypotReason="Contract contains blacklist functions",n):(d.includes("_buyTax")||d.includes("buyFee")||d.includes("buyTaxes"))&&(n.buyTax=10,n.buyTax>20)?(n.isHoneypot=!0,n.honeypotReason=`High buy tax: ${n.buyTax}%`,n):(d.includes("_sellTax")||d.includes("sellFee")||d.includes("sellTaxes"))&&(n.sellTax=10,n.sellTax>20)?(n.isHoneypot=!0,n.honeypotReason=`High sell tax: ${n.sellTax}%`,n):d.includes("onlyOwner")&&(d.includes("setMaxTxAmount")||d.includes("setMaxWalletSize"))?(n.isHoneypot=!0,n.honeypotReason="Owner can restrict trading",n):(o.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,blockchain:t,isHoneypot:n.isHoneypot,buyTax:n.buyTax,sellTax:n.sellTax}),n)}catch(n){return o.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,blockchain:t,error:n.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${n.message}`}}}))};const s=n(9321),a=n(871),o=n(1213)},8904:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getLiquidityInfoFromDexscreener=void 0;const a=s(n(8938));t.getLiquidityInfoFromDexscreener=e=>r(void 0,void 0,void 0,(function*(){const t=`https://api.dexscreener.com/latest/dex/tokens/${e}`;try{const n=yield a.default.get(t);if(!n.data.pairs||0===n.data.pairs.length)return console.log(`No liquidity found for the token ${e}.`),{hasLiquidity:!1,message:`No liquidity found for the token ${e}.`};const r=n.data.pairs[0],{baseToken:s,priceNative:o,priceUsd:i,chainId:d,priceChange:c,dexId:l,totalSupply:u,liquidity:g}=r,h=s.symbol,y=s.name||"Unknown Token";return{hasLiquidity:!0,symbol:h,name:y,priceNative:o,priceUsd:i,chainId:d,priceChange:c,dexId:l,totalSupply:u,pairAddress:r.pairAddress||"Not available",marketCap:r.marketCap||"Not available",address:s.address}}catch(e){throw console.error("Error fetching token info from Dexscreener:",e),new Error("Error fetching token info from Dexscreener. Please try again later.")}}))},9558:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfoFromDexscreener=void 0;const s=n(6580),a=n(871),o=n(1213);t.getTokenInfoFromDexscreener=(e,...t)=>r(void 0,[e,...t],void 0,(function*(e,t=a.BlockchainType.BSC){var n;try{const r=yield(0,s.getTokenInfoFromDexscreener)(e,t);return{symbol:(null==r?void 0:r.symbol)||"UNKNOWN",name:(null==r?void 0:r.name)||"Unknown Token",priceNative:(null==r?void 0:r.priceUsd)||0,priceUsd:(null==r?void 0:r.priceUsd)||0,chainId:t,priceChange:(null==r?void 0:r.priceChange)||{h1:0,h24:0},dexId:(null==r?void 0:r.dexId)||"unknown",totalSupply:0,pairAddress:(null==r?void 0:r.pairAddress)||"Not available",marketCap:(null==r?void 0:r.fdv)||"Not available",liquidity:{usd:(null===(n=null==r?void 0:r.liquidity)||void 0===n?void 0:n.usd)||0},address:e}}catch(n){return o.logger.error("Error fetching token info from Dexscreener:",{error:n.message,contractAddress:e,blockchain:t}),{symbol:"UNKNOWN",name:"Unknown Token",priceNative:0,priceUsd:0,chainId:t,priceChange:{h1:0,h24:0},dexId:"unknown",totalSupply:0,pairAddress:"Not available",marketCap:"Not available",liquidity:{usd:0},address:e}}}))},6314:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleEthDashboard=t.getTokenDetails=t.sellethTokenPercentage=t.handleEthSellAll=t.handleSpendeth=t.handleEthChart=t.handleEthRefresh=t.askForEthToken=t.askForEthContractAddress=t.handleGenerateNewethWallet=t.mainEthMenu=t.updateEthMessageWithTradeOptions=t.updateEthMessageWithSellOptions=t.createethKeyboard=t.getEthTradeOptionsKeyboard=t.getEthSellOptionsKeyboard=t.handleETHScanContract=void 0,t.fetchAndDisplayActiveEthTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let s=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield w(e,t,r,s)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayCurrentEthTrade=w;const a=s(n(8722)),o=s(n(6786)),i=n(9088),d=n(9558),c=s(n(3883)),l=n(9321),u=n(439);Object.defineProperty(t,"handleEthDashboard",{enumerable:!0,get:function(){return u.handleEthDashboard}});const g=n(4653),h=n(8014),y=n(8014),p=n(4736),f=n(871);let m={},_={};const b=process.env.ETH_PROVIDER_URL,v=new l.ethers.providers.JsonRpcProvider(b);if(!b)throw new Error("PROVIDER is not defined in the environment variables.");t.handleETHScanContract=(e,n,s)=>r(void 0,void 0,void 0,(function*(){var o,u;try{if(!s)throw new Error("Invalid contract address.");const g=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);g&&m[g]&&delete m[g],m[s]=m[s]||{},(0,p.setCurrentContractAddress)(f.BlockchainType.ETH,s);const h=yield(0,i.checkEVMHoneypot)(s,f.BlockchainType.ETH),y=yield(0,d.getTokenInfoFromDexscreener)(s),b=yield(u=s,r(void 0,void 0,void 0,(function*(){try{const e=new l.ethers.Contract(u,c.default,v),t=yield e.decimals(),n=yield e.totalSupply();return l.ethers.utils.formatUnits(n,t)}catch(e){return console.error("Error fetching total supply:",e),null}})));if(null===b)throw new Error("Failed to fetch total supply.");const k=Number(b),w=Number(y.priceUsd)*k,S=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),T=S(k),A=S(w),O=S(Number(y.liquidity.usd)),E=(null===(o=y.priceChange)||void 0===o?void 0:o.h1)||0,x=E>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",M=Math.ceil(parseFloat(String(h.buyTax||0))),N=Math.ceil(parseFloat(String(h.sellTax||0))),I=`\n🪙 ${y.symbol} (${y.name}) || 📈 ${E>0?"+":""}${E}%  || 🏦 ${y.dexId} || ${y.chainId}\n\n${x}  Price Change: ${E>0?"+":""}${E}%\n\nCA: ${y.address}\nLP: ${y.pairAddress}\n\n💼 TOTAL SUPPLY: ${T} ${y.symbol}\n🏷️ MC: $${A}\n💧 LIQUIDITY: $${O}\n💵 PRICE: $${y.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${h.isHoneypot?"HIGH":"LOW"}\n📉 Buy Tax: ${M}%\n📈 Sell Tax: ${N}%\n\n⚠️⚠️⚠️⚠️ ${h.honeypotReason||"No specific risks identified"}\n    `,B=yield a.default.findOne({chat_id:n}).exec();B&&(B.ethCurrentTokenName=y.name,B.ethCurrentContractAddress=s,yield B.save()),m[s]={totalSupply:k,marketCap:w,checkHoneypot:h,tokenInfo:y,resultMessage:I};const C=_[n];C&&(yield e.deleteMessage(n,C));const $=yield e.sendMessage(n,I,{parse_mode:"Markdown",reply_markup:(0,t.createethKeyboard)()});return _[n]=$.message_id,$.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}})),t.getEthSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_eth"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_eth"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_eth"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_eth"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_eth"})}]]})),t.getEthTradeOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Trade Option 1",callback_data:JSON.stringify({command:"trade_option_1"})},{text:"Trade Option 2",callback_data:JSON.stringify({command:"trade_option_2"})}]]})),t.createethKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_eth"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_eth"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_eth"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_eth"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_eth"})},{text:"💸 Spend X eth",callback_data:JSON.stringify({command:"spend_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updateEthMessageWithSellOptions=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getEthSellOptionsKeyboard)(),a=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.updateEthMessageWithTradeOptions=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getEthTradeOptionsKeyboard)(),a=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update trade options:",e)}})),t.mainEthMenu=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createethKeyboard)(),a=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update eth menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewethWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield a.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,g.generateETHWallet)();yield a.default.updateOne({chat_id:t},{$set:{"eth_wallet.address":n.address,"eth_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the eth blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,u.handleEthDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let k={};function w(e,t,n,s,o){return r(this,void 0,void 0,(function*(){const r=n[s],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield a.default.findOneAndUpdate({chat_id:t},{ethCurrentContractAddress:r.contractAddress,ethCurrentTokenName:r.contractName}),(0,p.setCurrentContractAddress)(f.BlockchainType.ETH,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_eth"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_eth"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_eth"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_eth"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_eth"})},{text:"Next",callback_data:JSON.stringify({command:"next_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}function S(e,t){return r(this,void 0,void 0,(function*(){const n=new l.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],v),[r,s]=yield Promise.all([n.balanceOf(t),n.decimals()]);return l.ethers.utils.formatUnits(r,s)}))}t.askForEthContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,h.INPUT_ETH_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForEthToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,h.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received Sol token:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleEthRefresh=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleETHScanContract)(e,n,r),s){const t=s.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleEthChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(n){const r=`https://dexscreener.com/eth/${n}`,s=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});k[t]={promptId:s.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");k[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendeth=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,y.SET_ETH_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend eth:",e),new Error("Failed to handle spend eth action.")}})),t.handleEthSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield a.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.eth_wallet,s=n.ethCurrentContractAddress||"";if(!s)throw new Error("No contract address found for user");const o=yield S(s,r),i=parseFloat(o);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=f.TraderFactory.getTrader(f.BlockchainType.ETH),c=yield d.sellTokens(s,100,r);if(c&&c.transactionHash&&c.blockNumber){const{transactionHash:n,blockNumber:r}=c;yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${n}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.com/tx/${n}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling all tokens: ${r}`)}})),t.sellethTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:s}=r.eth_wallet,o=r.ethCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=yield S(o,s),d=parseFloat(i);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");const c=d*(n/100);if(c<=0)throw new Error("Invalid percentage value");console.log("Amount to sell:",c);const l=f.TraderFactory.getTrader(f.BlockchainType.ETH),u=yield l.sellTokens(o,n,s);if(u&&u.transactionHash&&u.blockNumber){const{transactionHash:r,blockNumber:s}=u;yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${r}\n🧱 Block Number: ${s}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.org/tx/${r}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling tokens: ${r}`)}})),t.getTokenDetails=e=>m[e]},2969:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.checkIfHoneypot=function(e,t){return r(this,void 0,void 0,(function*(){try{return t===o.BlockchainType.SOL?yield function(e){return r(this,void 0,void 0,(function*(){var t,n,r,s,o,l;const u=Date.now(),g=`honeypot:sol:${e}`;try{const h=yield d.cacheService.get(g);if(h)return i.logger.debug(`Using cached honeypot check for ${e} on SOL`,{tokenAddress:e,isHoneypot:h.isHoneypot}),h;const y=`https://api.rugcheck.xyz/v1/tokens/${e}/report/summary`,p=(yield a.default.get(y)).data;let f=[];const m=[];let _=!1;p.risks&&p.risks.length>0&&(f=p.risks.map((e=>("danger"===e.level?(_=!0,m.push(`DANGER: ${e.name} - ${e.description}`)):"high"===e.level&&m.push(`HIGH RISK: ${e.name} - ${e.description}`),`${e.name}: ${e.description} (${e.level})`)))),_&&m.unshift("CRITICAL: Honeypot detected - High risk token");const b=null!==(n=null===(t=p.simulationResult)||void 0===t?void 0:t.buyTax)&&void 0!==n?n:"0",v=null!==(s=null===(r=p.simulationResult)||void 0===r?void 0:r.sellTax)&&void 0!==s?s:"0",k=null!==(l=null===(o=p.simulationResult)||void 0===o?void 0:o.transferTax)&&void 0!==l?l:"0",w={isHoneypot:_,risk:p.score,buyTax:b,sellTax:v,transferTax:k,warnings:m,details:f.join("\n")||"No specific risks identified",timestamp:Date.now()};return yield d.cacheService.set(g,w,c),i.logger.logPerformance("SOL honeypot check",u,{tokenAddress:e,isHoneypot:w.isHoneypot,risk:w.risk}),w}catch(t){i.logger.error("Failed to check if SOL token is honeypot",{error:t.message,tokenAddress:e});const n={isHoneypot:!1,risk:"unknown",buyTax:"0",sellTax:"0",warnings:["Failed to check honeypot status"],details:`Error: ${t.message}`,timestamp:Date.now()};return yield d.cacheService.set(g,n,300),n}}))}(e):yield function(e,t){return r(this,void 0,void 0,(function*(){var n,r,s;const l=Date.now(),u=`honeypot:${t}:${e}`;try{const g=yield d.cacheService.get(u);if(g)return i.logger.debug(`Using cached honeypot check for ${e} on ${t}`,{tokenAddress:e,blockchain:t,isHoneypot:g.isHoneypot}),g;const h=function(e){switch(e){case o.BlockchainType.ETH:return"https://api.honeypot.is/v2/IsHoneypot";case o.BlockchainType.BSC:return"https://bsc.honeypot.is/v2/IsHoneypot";case o.BlockchainType.BASE:return"https://base.honeypot.is/v2/IsHoneypot";default:throw new Error(`Unsupported blockchain for honeypot check: ${e}`)}}(t),y=yield a.default.get(h,{params:{address:e}}),{summary:p,simulationResult:f}=y.data;let m=p.flags.map((e=>e.description));const _=[];"honeypot"===p.risk?_.push("CRITICAL: Honeypot detected - High risk token"):"unknown"===p.risk&&_.push("WARNING: Could not determine if this is a honeypot. Proceed with caution."),m.includes("The source code is not available, allowing for hidden functionality.")&&(_.push("WARNING: Contract's dependencies are closed source, allowing for hidden functionalities."),m=m.filter((e=>"The source code is not available, allowing for hidden functionality."!==e))),m=m.filter((e=>"All snipers are marked as honeypots (blacklisted). Sniper detection still needs some improvements."!==e));const b=null!==(n=null==f?void 0:f.buyTax)&&void 0!==n?n:"0",v=null!==(r=null==f?void 0:f.sellTax)&&void 0!==r?r:"0",k=null!==(s=null==f?void 0:f.transferTax)&&void 0!==s?s:"0",w={isHoneypot:"honeypot"===p.risk,risk:p.risk,buyTax:b,sellTax:v,transferTax:k,buyGas:null==f?void 0:f.buyGas,sellGas:null==f?void 0:f.sellGas,warnings:_,details:m.join("\n")||"No specific risks identified",timestamp:Date.now()};return yield d.cacheService.set(u,w,c),i.logger.logPerformance(`${t} honeypot check`,l,{tokenAddress:e,isHoneypot:w.isHoneypot,risk:w.risk}),w}catch(n){i.logger.error(`Failed to check if ${t} token is honeypot`,{error:n.message,tokenAddress:e,blockchain:t});const r={isHoneypot:!1,risk:"unknown",buyTax:"0",sellTax:"0",warnings:["Failed to check honeypot status"],details:`Error: ${n.message}`,timestamp:Date.now()};return yield d.cacheService.set(u,r,300),r}}))}(e,t)}catch(n){return i.logger.error("Failed to check if token is honeypot",{error:n.message,tokenAddress:e,blockchain:t}),{isHoneypot:!1,risk:"unknown",buyTax:"0",sellTax:"0",warnings:["Failed to check honeypot status"],details:`Error: ${n.message}`,timestamp:Date.now()}}}))},t.formatHoneypotResult=function(e){let t="";return e.isHoneypot?t+="🚨 **HONEYPOT DETECTED** 🚨\n\n":"unknown"===e.risk?t+="⚠️ **UNKNOWN RISK** ⚠️\n\n":t+="✅ **Not detected as a honeypot**\n\n",t+=`**Risk Level:** ${e.risk}\n`,t+=`**Buy Tax:** ${e.buyTax}%\n`,t+=`**Sell Tax:** ${e.sellTax}%\n`,e.transferTax&&(t+=`**Transfer Tax:** ${e.transferTax}%\n`),e.warnings.length>0&&(t+="\n**Warnings:**\n",e.warnings.forEach((e=>{t+=`- ${e}\n`}))),e.details&&"No specific risks identified"!==e.details&&(t+="\n**Details:**\n",t+=e.details),t};const a=s(n(8938)),o=n(871),i=n(1213),d=n(8708),c=3600},4545:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),a=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)},o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return s(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CacheFactory=t.cacheService=t.logger=void 0,t.initializeServices=function(e){Promise.resolve().then((()=>o(n(1213)))).then((({logger:t})=>{t.info("Initializing services..."),Promise.resolve().then((()=>o(n(8708)))).then((({cacheService:r})=>{t.info("Cache service initialized"),Promise.resolve().then((()=>o(n(1536)))).then((({initializeBlockchainServices:r})=>{r(),e&&Promise.resolve().then((()=>o(n(3003)))).then((({initializeTraders:n})=>{n(e),t.info("Trading services initialized")})),Promise.resolve().then((()=>o(n(7095)))).then((({initializeUtilityServices:e})=>{e(),t.info("All services initialized successfully")}))}))}))}))},t.shutdownServices=function(){Promise.resolve().then((()=>o(n(1213)))).then((({logger:e})=>{e.info("Shutting down services..."),Promise.resolve().then((()=>o(n(8708)))).then((({CacheFactory:t})=>{t.shutdown(),e.info("All services shut down successfully"),e.shutdown()}))}))};var i=n(1213);Object.defineProperty(t,"logger",{enumerable:!0,get:function(){return i.logger}});var d=n(8708);Object.defineProperty(t,"cacheService",{enumerable:!0,get:function(){return d.cacheService}}),Object.defineProperty(t,"CacheFactory",{enumerable:!0,get:function(){return d.CacheFactory}}),a(n(1536),t),a(n(871),t),a(n(3003),t),a(n(6580),t),a(n(2969),t),a(n(7095),t),a(n(3868),t)},1213:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.logger=t.Logger=t.LogLevel=void 0;const s=r(n(9896)),a=r(n(6928)),o=r(n(857)),i=r(n(9907));var d;!function(e){e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e[e.FATAL=4]="FATAL"}(d||(t.LogLevel=d={}));const c="[0m",l="[2m",u="[31m",g="[37m";class h{constructor(){var e,t;if(this.logBuffer=[],this.flushInterval=null,this.rotationInterval=null,this.logLevel=this.getLogLevelFromEnv(),this.logToFile="true"===process.env.LOG_TO_FILE,this.logFilePath=process.env.LOG_FILE_PATH||a.default.join(process.cwd(),"logs","app.log"),this.serviceName=process.env.SERVICE_NAME||"easybot",this.hostname=o.default.hostname(),this.workerId=(null===(t=null===(e=i.default.worker)||void 0===e?void 0:e.id)||void 0===t?void 0:t.toString())||"main",this.maxBufferSize=parseInt(process.env.LOG_BUFFER_SIZE||"100",10),this.flushIntervalMs=parseInt(process.env.LOG_FLUSH_INTERVAL_MS||"5000",10),this.rotateLogsIntervalHours=parseInt(process.env.LOG_ROTATION_HOURS||"24",10),this.maxLogFileSizeMb=parseInt(process.env.MAX_LOG_FILE_SIZE_MB||"10",10),this.maxLogFiles=parseInt(process.env.MAX_LOG_FILES||"5",10),this.metrics={startTime:Date.now(),logCount:{[d.DEBUG]:0,[d.INFO]:0,[d.WARN]:0,[d.ERROR]:0,[d.FATAL]:0},lastFlushTime:0,totalFlushes:0,totalWriteTime:0,maxBufferSize:0},this.logToFile){const e=a.default.dirname(this.logFilePath);s.default.existsSync(e)||s.default.mkdirSync(e,{recursive:!0})}this.flushInterval=setInterval((()=>this.flushLogs()),this.flushIntervalMs),this.logToFile&&(this.rotationInterval=setInterval((()=>this.rotateLogFiles()),60*this.rotateLogsIntervalHours*60*1e3)),this.info("Logger initialized",{level:d[this.logLevel],logToFile:this.logToFile,logFilePath:this.logFilePath,flushInterval:this.flushIntervalMs,maxBufferSize:this.maxBufferSize})}static getInstance(){return h.instance||(h.instance=new h),h.instance}getLogLevelFromEnv(){var e;switch(null===(e=process.env.LOG_LEVEL)||void 0===e?void 0:e.toUpperCase()){case"DEBUG":return d.DEBUG;case"INFO":default:return d.INFO;case"WARN":return d.WARN;case"ERROR":return d.ERROR;case"FATAL":return d.FATAL}}getLevelColor(e){switch(e){case d.DEBUG:return"[36m";case d.INFO:return"[32m";case d.WARN:return"[33m";case d.ERROR:return u;case d.FATAL:return"[41m"+g;default:return g}}formatLogEntryForConsole(e){const t=d[e.level],n=this.getLevelColor(e.level),r=e.timestamp.split("T")[1].replace("Z","");let s=`${l}${r}${c} ${n}[${t}]${c} ${e.message}`;if((e.service||e.workerId)&&(s+=` ${l}(${e.service||this.serviceName}:${e.workerId||this.workerId})${c}`),e.context)try{const t=JSON.stringify(e.context);"{}"!==t&&(s+=` ${l}${t}${c}`)}catch(e){s+=` ${u}[Context serialization failed]${c}`}return s}formatLogEntryForFile(e){const t=d[e.level];let n=`[${e.timestamp}] [${t}] [${e.service||this.serviceName}:${e.workerId||this.workerId}] ${e.message}`;if(e.context)try{const t=JSON.stringify(e.context);"{}"!==t&&(n+=` ${t}`)}catch(e){n+=" [Context serialization failed]"}return n}addToBuffer(e){this.logBuffer.push(e),this.metrics.logCount[e.level]++,this.metrics.maxBufferSize=Math.max(this.metrics.maxBufferSize,this.logBuffer.length),this.logBuffer.length>=this.maxBufferSize&&this.flushLogs()}flushLogs(){if(0===this.logBuffer.length)return;const e=Date.now();if(this.logToFile)try{const e=this.logBuffer.map((e=>this.formatLogEntryForFile(e)));s.default.appendFileSync(this.logFilePath,e.join("\n")+"\n"),this.checkLogFileSize()}catch(e){console.error(`${u}Failed to write to log file:${c}`,e)}this.metrics.lastFlushTime=Date.now(),this.metrics.totalFlushes++,this.metrics.totalWriteTime+=Date.now()-e,this.logBuffer=[]}checkLogFileSize(){try{s.default.statSync(this.logFilePath).size/1048576>=this.maxLogFileSizeMb&&this.rotateLogFiles()}catch(e){}}rotateLogFiles(){if(this.logToFile)try{if(!s.default.existsSync(this.logFilePath))return;const e=a.default.dirname(this.logFilePath),t=a.default.extname(this.logFilePath),n=a.default.basename(this.logFilePath,t),r=(new Date).toISOString().replace(/:/g,"-").replace(/\..+/,"");for(let r=this.maxLogFiles-1;r>=0;r--){const o=a.default.join(e,`${n}.${r}${t}`);if(s.default.existsSync(o))if(r===this.maxLogFiles-1)s.default.unlinkSync(o);else{const i=a.default.join(e,`${n}.${r+1}${t}`);s.default.renameSync(o,i)}}const o=a.default.join(e,`${n}.0${t}`);s.default.renameSync(this.logFilePath,o),s.default.writeFileSync(this.logFilePath,`Log file created at ${r}\n`),this.info("Log file rotated",{oldFile:o,newFile:this.logFilePath})}catch(e){console.error(`${u}Failed to rotate log files:${c}`,e)}}log(e,t,n){if(e<this.logLevel)return;const r={timestamp:(new Date).toISOString(),level:e,message:t,context:n,service:this.serviceName,workerId:this.workerId,hostname:this.hostname};console.log(this.formatLogEntryForConsole(r)),this.logToFile&&this.addToBuffer(r)}debug(e,t){this.log(d.DEBUG,e,t)}info(e,t){this.log(d.INFO,e,t)}warn(e,t){this.log(d.WARN,e,t)}error(e,t){this.log(d.ERROR,e,t)}fatal(e,t){this.log(d.FATAL,e,t)}logPerformance(e,t,n){const r=Date.now()-t;this.debug(`Performance: ${e} completed in ${r}ms`,Object.assign(Object.assign({},n),{operation:e,durationMs:r,timestamp:(new Date).toISOString()}))}getMetrics(){const e=Date.now()-this.metrics.startTime,t=this.metrics.totalFlushes>0?this.metrics.totalWriteTime/this.metrics.totalFlushes:0;return{uptime:e,uptimeHuman:this.formatUptime(e),logCounts:this.metrics.logCount,totalLogs:Object.values(this.metrics.logCount).reduce(((e,t)=>e+t),0),flushes:this.metrics.totalFlushes,avgFlushTimeMs:t,maxBufferSize:this.metrics.maxBufferSize,currentBufferSize:this.logBuffer.length}}formatUptime(e){const t=Math.floor(e/1e3),n=Math.floor(t/60),r=Math.floor(n/60);return`${Math.floor(r/24)}d ${r%24}h ${n%60}m ${t%60}s`}shutdown(){this.flushInterval&&(clearInterval(this.flushInterval),this.flushInterval=null),this.rotationInterval&&(clearInterval(this.rotationInterval),this.rotationInterval=null),this.flushLogs(),this.info("Logger shutdown",this.getMetrics())}}t.Logger=h,t.logger=h.getInstance()},6325:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.createSession=void 0;const a=s(n(8722));let o={};t.createSession=(e,t)=>r(void 0,void 0,void 0,(function*(){const n=t.chat.id;if(o[n])yield e.sendMessage(n,"You are already logged in.");else{const t=yield a.default.findOne({chat_id:n}).exec();if(t){const r=t.currentBlockchain,s=t[r]||[];o[n]={session:s,currentTradeIndex:0,telegramId:t.chat_id,userId:t._id,trades:[]},yield e.sendMessage(n,`Session created. You are now logged in to your ${r.toUpperCase()} wallet.`)}else yield e.sendMessage(n,"User not found. Please register first.")}}))},4035:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleSolDashboard=t.getTokenDetails=t.sellSolTokenPercentage=t.handleSolSellAll=t.handleSpendsol=t.handleSolChart=t.handleSolRefresh=t.askForSolToken=t.askForSolContractAddress=t.handleGenerateNewSolWallet=t.mainSolMenu=t.updateSolMessageWithTradeOptions=t.updateSolMessageWithSellOptions=t.createSolKeyboard=t.getsolTradeOptionsKeyboard=t.getSolSellOptionsKeyboard=t.getSolBalance=t.handleSOLScanContract=void 0,t.getSolTokenBalance=_,t.fetchAndDisplayActiveSolTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let s=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield k(e,t,r,s)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayCurrentSolTrade=k;const a=s(n(8722)),o=s(n(6786)),i=n(9558),d=n(8322);Object.defineProperty(t,"handleSolDashboard",{enumerable:!0,get:function(){return d.handleSolDashboard}});const c=n(4653),l=n(8014),u=n(8014),g=n(4736),h=n(871),y=n(8491);let p={},f={};const m=process.env.SOL_PROVIDER_URL;if(!m)throw new Error("PROVIDER_URL is not defined in the environment variables.");function _(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield e.getParsedTokenAccountsByOwner(new y.PublicKey(t),{mint:new y.PublicKey(n)});if(0===r.value.length)throw new Error("Token account not found");const s=r.value[0].account.data.parsed.info.tokenAmount.uiAmount;return console.log("Balance (using Solana-Web3.js): ",s),s}catch(e){throw console.error("Error fetching token balance:",e),e}}))}new y.Connection(m),t.handleSOLScanContract=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{if(!s)throw new Error("Invalid contract address.");const o=(0,g.getCurrentContractAddress)(h.BlockchainType.SOL);o&&p[o]&&delete p[o],p[s]=p[s]||{},(0,g.setCurrentContractAddress)(h.BlockchainType.SOL,s);const d=h.TraderFactory.getTrader(h.BlockchainType.SOL),c=yield d.checkHoneypot(s),l=yield(0,i.getTokenInfoFromDexscreener)(s,h.BlockchainType.SOL),u=new y.Connection(process.env.SOL_PROVIDER_URL),m=yield((e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=new y.PublicKey(t),r=yield e.getParsedAccountInfo(n);if(!r||!r.value||r.value.data instanceof Buffer)throw new Error("Could not find mint account");const s=r.value.data.parsed.info,a=s.supply,o=s.decimals;return a/Math.pow(10,o)}catch(e){throw console.error(`Error fetching token total supply: ${e}`),e}})))(u,s);if(null===m)throw new Error("Failed to fetch total supply.");const _=m,b=l.priceUsd*_,v=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),k=v(_),w=v(b),S=v(l.liquidity.usd),T=l.priceChange.h1||0,A=T>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",O=Math.ceil(parseFloat(c.buyTax)),E=Math.ceil(parseFloat(c.sellTax)),x=`\n🪙 ${l.symbol} (${l.name}) || 📈 ${T>0?"+":""}${T}%  || 🏦 ${l.dexId} || ${l.chainId}\n\n${A}  Price Change: ${T>0?"+":""}${T}%\n\nCA: ${l.address}\nLP: ${l.pairAddress}\n\n💼 TOTAL SUPPLY: ${k} ${l.symbol}\n🏷️ MC: $${w}\n💧 LIQUIDITY: $${S}\n💵 PRICE: $${l.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${c.risk}\n📉 Buy Tax: ${O}%\n📈 Sell Tax: ${E}%\n\n⚠️⚠️⚠️⚠️ ${c.riskDescriptions}\n      `,M=yield a.default.findOne({chat_id:n}).exec();M&&(M.solCurrentTokenName=l.name,M.solCurrentContractAddress=s,yield M.save()),p[s]={totalSupply:k,marketCap:b,checkHoneypot:c,tokenInfo:l,resultMessage:x};const N=f[n];N&&(yield e.deleteMessage(n,N));const I=yield e.sendMessage(n,x,{parse_mode:"Markdown",reply_markup:(0,t.createSolKeyboard)()});return f[n]=I.message_id,I.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}})),t.getSolBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.SOL_PROVIDER_URL;if(!t)throw new Error("Provider is not defined in the .env file");const n=new y.Connection(t);try{const t=new y.PublicKey(e);return((yield n.getBalance(t))/y.LAMPORTS_PER_SOL).toFixed(6)}catch(e){throw console.error("Error fetching balance:",e),new Error("Unable to fetch balance for Solana wallet.")}})),t.getSolSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_sol"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_sol"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_sol"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_sol"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_sol"})}]]})),t.getsolTradeOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Trade Option 1",callback_data:JSON.stringify({command:"trade_option_1"})},{text:"Trade Option 2",callback_data:JSON.stringify({command:"trade_option_2"})}]]})),t.createSolKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_sol"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_sol"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_sol"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_sol"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_sol"})},{text:"💸 Spend X sol",callback_data:JSON.stringify({command:"spend_sol"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updateSolMessageWithSellOptions=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getSolSellOptionsKeyboard)(),a=(0,g.getCurrentContractAddress)(h.BlockchainType.SOL);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.updateSolMessageWithTradeOptions=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getsolTradeOptionsKeyboard)(),a=(0,g.getCurrentContractAddress)(h.BlockchainType.SOL);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update trade options:",e)}})),t.mainSolMenu=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createSolKeyboard)(),a=(0,g.getCurrentContractAddress)(h.BlockchainType.SOL);if(!a)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(a);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:s,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update sol menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewSolWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield a.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,c.generateSOLWallet)();yield a.default.updateOne({chat_id:t},{$set:{"sol_wallet.address":n.address,"sol_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the sol blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,d.handleSolDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let b={};function v(e){return r(this,arguments,void 0,(function*(e,t=100){try{const n=yield a.default.findOne({chat_id:e});if(!n)throw new Error("User not found");const r=(0,g.getCurrentContractAddress)(h.BlockchainType.SOL);if(!r)throw new Error("No contract address found");const s=h.TraderFactory.getTrader(h.BlockchainType.SOL),o={slippage:5},i=yield s.sellTokens(r,t,n.sol_wallet.address,o);if(!i.success)throw new Error(i.error||"Unknown error");return i.transactionHash||""}catch(e){throw e}}))}function k(e,t,n,s,o){return r(this,void 0,void 0,(function*(){const r=n[s],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield a.default.findOneAndUpdate({chat_id:t},{solCurrentContractAddress:r.contractAddress,solCurrentTokenName:r.contractName}),(0,g.setCurrentContractAddress)(h.BlockchainType.SOL,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_sol"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_sol"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_sol"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_sol"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_sol"})},{text:"Next",callback_data:JSON.stringify({command:"next_sol"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}t.askForSolContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(b[t]){const n=b[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,l.INPUT_SOL_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return b[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),b[t]=Object.assign(Object.assign({},b[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForSolToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(b[t]){const n=b[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,l.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return b[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received Sol token:",e.text),b[t]=Object.assign(Object.assign({},b[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleSolRefresh=(e,n,s)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,g.getCurrentContractAddress)(h.BlockchainType.SOL);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleSOLScanContract)(e,n,r),s){const t=s.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleSolChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,g.getCurrentContractAddress)(h.BlockchainType.SOL);if(n){const r=`https://dexscreener.com/solana/${n}`,s=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});b[t]={promptId:s.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");b[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendsol=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,u.SET_SOL_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return b[t]={promptId:n.message_id},new Promise(((s,a)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),b[t]=Object.assign(Object.assign({},b[t]),{replyId:e.message_id}),s(e.text)):a(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend sol:",e),new Error("Failed to handle spend sol action.")}})),t.handleSolSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield a.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.sol_wallet,s=n.solCurrentContractAddress||"";if(!s)throw new Error("No contract address found for user");const o=new y.Connection(process.env.SOL_PROVIDER_URL),i=yield _(o,r,s);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=yield v(t,100);d?yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${d}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${d}\nTo see your transaction`):yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling all tokens: ${r}`)}})),t.sellSolTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield a.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:s}=r.sol_wallet,o=r.solCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=new y.Connection(process.env.SOL_PROVIDER_URL),d=yield _(i,s,o);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");if(d*(n/100)<=0)throw new Error("Invalid percentage value");console.log("Percentage to sell:",n);const c=yield v(t,n);c?yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${c}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${c}\nTo see your transaction`):yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling tokens: ${r}`)}})),t.getTokenDetails=e=>p[e]},6580:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfoFromDexscreener=u,t.getLiquidityInfo=function(e,t){return r(this,void 0,void 0,(function*(){var n,r;const s=Date.now(),a=`liquidity:${t}:${e}`;try{const o=yield d.cacheService.get(a);if(o)return i.logger.debug(`Using cached liquidity info for ${e} on ${t}`,{tokenAddress:e,blockchain:t}),o;const c=yield u(e,t);if(!c){const e={hasLiquidity:!1,liquidityUsd:0};return yield d.cacheService.set(a,e,l),e}const g=(null===(n=c.liquidity)||void 0===n?void 0:n.usd)||0,h=g>0,y={hasLiquidity:h,liquidityUsd:g,pairAddress:c.pairAddress,dexId:c.dexId,priceUsd:c.priceUsd,volume24h:null===(r=c.volume)||void 0===r?void 0:r.h24,priceChange:c.priceChange};return yield d.cacheService.set(a,y,l),i.logger.logPerformance("Liquidity info",s,{tokenAddress:e,blockchain:t,hasLiquidity:h,liquidityUsd:g}),y}catch(n){return i.logger.error(`Failed to get liquidity info for ${e} on ${t}`,{error:n.message,tokenAddress:e,blockchain:t}),{hasLiquidity:!1,liquidityUsd:0}}}))},t.getComprehensiveTokenInfo=function(e,t){return r(this,void 0,void 0,(function*(){const n=Date.now(),r=`token:${t}:${e}`;try{const s=yield d.cacheService.get(r);if(s)return i.logger.debug(`Using cached comprehensive token info for ${e} on ${t}`,{tokenAddress:e,blockchain:t}),s;const a=yield u(e,t),o=yield(0,c.checkIfHoneypot)(e,t),g={name:(null==a?void 0:a.name)||"Unknown",symbol:(null==a?void 0:a.symbol)||"UNKNOWN",decimals:18,address:e,blockchain:t,price:(null==a?void 0:a.priceUsd)||0,marketCap:(null==a?void 0:a.fdv)||0,isHoneypot:o.isHoneypot,honeypotInfo:o};return yield d.cacheService.set(r,g,l),i.logger.logPerformance("Comprehensive token info",n,{tokenAddress:e,blockchain:t}),g}catch(n){return i.logger.error(`Failed to get comprehensive token info for ${e} on ${t}`,{error:n.message,tokenAddress:e,blockchain:t}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:t}}}))},t.formatTokenInfo=function(e){let t="";return t+="**Token Information**\n\n",t+=`**Name:** ${e.name}\n`,t+=`**Symbol:** ${e.symbol}\n`,t+=`**Address:** \`${e.address}\`\n`,t+=`**Blockchain:** ${e.blockchain}\n`,e.price&&(t+=`**Price:** $${e.price.toFixed(8)}\n`),e.marketCap&&(t+=`**Market Cap:** $${e.marketCap.toLocaleString()}\n`),void 0!==e.isHoneypot&&(t+=`**Honeypot:** ${e.isHoneypot?"🚨 YES":"✅ NO"}\n`),t};const a=s(n(8938)),o=n(871),i=n(1213),d=n(8708),c=n(2969),l=300;function u(e,t){return r(this,void 0,void 0,(function*(){var n,r,s,c;const u=Date.now(),g=`dexscreener:${t}:${e}`;try{const h=yield d.cacheService.get(g);if(h)return i.logger.debug(`Using cached token info for ${e} on ${t}`,{tokenAddress:e,blockchain:t}),h;const y=function(e){switch(e){case o.BlockchainType.ETH:return"ethereum";case o.BlockchainType.BSC:return"bsc";case o.BlockchainType.SOL:return"solana";case o.BlockchainType.BASE:return"base";default:throw new Error(`Unsupported blockchain: ${e}`)}}(t),p=`https://api.dexscreener.com/latest/dex/tokens/${e}`,f=(yield a.default.get(p)).data;if(!f.pairs||0===f.pairs.length)return i.logger.warn(`No pairs found for token ${e} on ${t}`,{tokenAddress:e,blockchain:t}),null;const m=f.pairs.find((e=>e.chainId===y));if(!m)return i.logger.warn(`No pair found for token ${e} on ${t}`,{tokenAddress:e,blockchain:t,availableChains:f.pairs.map((e=>e.chainId))}),null;const _={name:m.baseToken.name,symbol:m.baseToken.symbol,priceUsd:parseFloat(m.priceUsd||"0"),liquidity:{usd:(null===(n=m.liquidity)||void 0===n?void 0:n.usd)||0},fdv:m.fdv||0,volume:{h24:(null===(r=m.volume)||void 0===r?void 0:r.h24)||0},priceChange:{h1:(null===(s=m.priceChange)||void 0===s?void 0:s.h1)||0,h24:(null===(c=m.priceChange)||void 0===c?void 0:c.h24)||0},pairAddress:m.pairAddress,dexId:m.dexId,chainId:m.chainId};return yield d.cacheService.set(g,_,l),i.logger.logPerformance("Dexscreener token info",u,{tokenAddress:e,blockchain:t}),_}catch(n){return i.logger.error(`Failed to get token info from Dexscreener for ${e} on ${t}`,{error:n.message,tokenAddress:e,blockchain:t}),null}}))}},6086:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.BaseNetworkTrader=t.BASE_CONFIG=void 0;const a=n(9321),o=n(871),i=n(1213),d=n(4653),c=n(9088),l=n(9558),u=s(n(3883)),g=s(n(8938));t.BASE_CONFIG={rpcUrl:process.env.BASE_PROVIDER_URL||"https://mainnet.base.org",routerAddress:process.env.BASE_ROUTER_ADDRESS||"******************************************",wethAddress:process.env.BASE_WETH_ADDRESS||"******************************************",explorerUrl:"https://basescan.org",gasLimit:3e5,defaultSlippage:10,apiUrl:process.env.BASENODE_API_URL||"https://base.api.0x.org/swap/v1/quote"};class h extends o.BaseTrader{constructor(e){super(e),this.provider=new a.ethers.providers.JsonRpcProvider(t.BASE_CONFIG.rpcUrl),this.apiKey=process.env.MASTER_KEY||"",i.logger.info("Base Network Trader initialized",{rpcUrl:t.BASE_CONFIG.rpcUrl})}getBlockchainType(){return o.BlockchainType.BASE}buyTokens(e,n,s,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Buying tokens on Base",{tokenAddress:e,amount:n,walletAddress:s,options:o});try{const d=yield this.getPrivateKey(s);if(!d)throw new Error("Private key not found for wallet");const c=new a.ethers.Wallet(d,this.provider),l=a.ethers.utils.parseEther(n),u="******************************************",h=e,y=((null==o?void 0:o.slippage)||t.BASE_CONFIG.defaultSlippage)/100,p=process.env.EVM_ADMIN_ADDRESS||"",f=.08,m=`buyToken=${h}&sellToken=${u}&sellAmount=${l.toString()}&slippagePercentage=${y}&takerAddress=${s}&feeRecipient=${p}&buyTokenPercentageFee=${f}`,_=`${t.BASE_CONFIG.apiUrl}?${m}`,b=(yield g.default.get(_,{headers:{"0x-api-key":this.apiKey}})).data,v={to:b.to,data:b.data,value:a.ethers.BigNumber.from(b.value),gasLimit:(null==o?void 0:o.gasLimit)?a.ethers.BigNumber.from(o.gasLimit):a.ethers.BigNumber.from(t.BASE_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?a.ethers.utils.parseUnits(o.gasPrice,"gwei"):a.ethers.utils.parseUnits("0.05","gwei")},k=yield c.sendTransaction(v),w=yield k.wait();return i.logger.info("Base buy transaction successful",{transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toString(),tokenAddress:e}),i.logger.logPerformance("Base buyTokens",r,{tokenAddress:e,transactionHash:w.transactionHash}),{success:!0,transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toNumber(),effectiveGasPrice:w.effectiveGasPrice.toString(),amountIn:n,amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("Base buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:s}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,s,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Selling tokens on Base",{tokenAddress:e,percentage:n,walletAddress:s,options:o});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const d=yield this.getPrivateKey(s);if(!d)throw new Error("Private key not found for wallet");const c=new a.ethers.Wallet(d,this.provider),l=new a.ethers.Contract(e,u.default,c),h=yield l.balanceOf(s),y=yield l.decimals(),p=h.mul(n).div(100);if(p.isZero())throw new Error("No tokens to sell");const f=process.env.EVM_SPENDER_ADDRESS||"";if((yield l.allowance(s,f)).lt(p)){const t=yield l.approve(f,a.ethers.constants.MaxUint256);yield t.wait(),i.logger.info("Approved spender to spend tokens",{tokenAddress:e,walletAddress:s,transactionHash:t.hash})}const m=e,_="******************************************",b=((null==o?void 0:o.slippage)||t.BASE_CONFIG.defaultSlippage)/100,v=process.env.EVM_ADMIN_ADDRESS||"",k=.08,w=`buyToken=${_}&sellToken=${m}&sellAmount=${p.toString()}&slippagePercentage=${b}&takerAddress=${s}&feeRecipient=${v}&buyTokenPercentageFee=${k}`,S=`${t.BASE_CONFIG.apiUrl}?${w}`,T=(yield g.default.get(S,{headers:{"0x-api-key":this.apiKey}})).data,A={to:T.to,data:T.data,value:a.ethers.BigNumber.from(T.value||0),gasLimit:(null==o?void 0:o.gasLimit)?a.ethers.BigNumber.from(o.gasLimit):a.ethers.BigNumber.from(t.BASE_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?a.ethers.utils.parseUnits(o.gasPrice,"gwei"):a.ethers.utils.parseUnits("0.05","gwei")},O=yield c.sendTransaction(A),E=yield O.wait();return i.logger.info("Base sell transaction successful",{transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toString(),tokenAddress:e,percentage:n}),i.logger.logPerformance("Base sellTokens",r,{tokenAddress:e,transactionHash:E.transactionHash,percentage:n}),{success:!0,transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toNumber(),effectiveGasPrice:E.effectiveGasPrice.toString(),amountIn:a.ethers.utils.formatUnits(p,y),amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("Base sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:s,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=new a.ethers.Contract(e,u.default,this.provider),[n,r,s,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),d=yield(0,l.getTokenInfoFromDexscreener)(e,o.BlockchainType.BASE),c=yield this.checkHoneypot(e);return{name:n,symbol:r,decimals:s,totalSupply:a.ethers.utils.formatUnits(i,s),address:e,blockchain:o.BlockchainType.BASE,price:"string"==typeof(null==d?void 0:d.priceUsd)?parseFloat(d.priceUsd):(null==d?void 0:d.priceUsd)||0,marketCap:"string"==typeof(null==d?void 0:d.marketCap)?parseFloat(d.marketCap):(null==d?void 0:d.marketCap)||0,isHoneypot:(null==c?void 0:c.isHoneypot)||!1,honeypotInfo:c}}catch(t){return i.logger.error("Failed to get Base token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:o.BlockchainType.BASE}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.provider.getBalance(e);return a.ethers.utils.formatEther(t)}catch(t){return i.logger.error("Failed to get Base wallet balance",{error:t.message,walletAddress:e}),"0"}}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{const n=new a.ethers.Contract(e,u.default,this.provider),r=yield n.balanceOf(t),s=yield n.decimals();return a.ethers.utils.formatUnits(r,s)}catch(n){return i.logger.error("Failed to get Base token balance",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,c.checkEVMHoneypot)(e,o.BlockchainType.BASE)}catch(t){return i.logger.error("Failed to check if Base token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,d.generateBASEWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:o.BlockchainType.BASE})}catch(e){throw i.logger.error("Failed to generate Base wallet",{error:e.message}),e}}))}sendMessage(e,t){return r(this,void 0,void 0,(function*(){try{yield this.bot.sendMessage(e,t,{parse_mode:"Markdown"})}catch(e){console.error("Failed to send message:",e)}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}}t.BaseNetworkTrader=h},1583:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.BscTrader=t.BSC_CONFIG=void 0;const a=n(9321),o=n(871),i=n(1213),d=n(4653),c=n(9088),l=n(9558),u=s(n(3883)),g=s(n(3455));t.BSC_CONFIG={rpcUrl:process.env.BSC_PROVIDER_URL||"https://bsc-dataseed.binance.org/",routerAddress:process.env.BSC_ROUTER_ADDRESS||"******************************************",wbnbAddress:process.env.BSC_WBNB_ADDRESS||"******************************************",explorerUrl:"https://bscscan.com",gasLimit:3e5,defaultSlippage:12};class h extends o.BaseTrader{constructor(e){super(e),this.provider=new a.ethers.providers.JsonRpcProvider(t.BSC_CONFIG.rpcUrl),i.logger.info("BSC Trader initialized",{rpcUrl:t.BSC_CONFIG.rpcUrl})}getBlockchainType(){return o.BlockchainType.BSC}buyTokens(e,n,s,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Buying tokens on BSC",{tokenAddress:e,amount:n,walletAddress:s,options:o});try{const d=yield this.getPrivateKey(s);if(!d)throw new Error("Private key not found for wallet");const c=new a.ethers.Wallet(d,this.provider),l=new a.ethers.Contract(t.BSC_CONFIG.routerAddress,g.default,c),u=a.ethers.utils.parseEther(n),h=[t.BSC_CONFIG.wbnbAddress,e],y=(null==o?void 0:o.slippage)||t.BSC_CONFIG.defaultSlippage,[,p]=yield l.getAmountsOut(u,h),f=p.mul(100-y).div(100),m=(null==o?void 0:o.gasLimit)||t.BSC_CONFIG.gasLimit,_=(null==o?void 0:o.gasPrice)?a.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice(),b=Math.floor(Date.now()/1e3)+1200,v=yield l.swapExactETHForTokensSupportingFeeOnTransferTokens(f,h,s,b,{value:u,gasLimit:m,gasPrice:_}),k=yield v.wait();return i.logger.info("BSC buy transaction successful",{transactionHash:k.transactionHash,blockNumber:k.blockNumber,gasUsed:k.gasUsed.toString(),tokenAddress:e}),i.logger.logPerformance("BSC buyTokens",r,{tokenAddress:e,transactionHash:k.transactionHash}),{success:!0,transactionHash:k.transactionHash,blockNumber:k.blockNumber,gasUsed:k.gasUsed.toNumber(),effectiveGasPrice:k.effectiveGasPrice.toString(),amountIn:n,amountOut:a.ethers.utils.formatUnits(f,18),timestamp:Date.now()}}catch(t){return i.logger.error("BSC buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:s}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,s,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Selling tokens on BSC",{tokenAddress:e,percentage:n,walletAddress:s,options:o});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const d=yield this.getPrivateKey(s);if(!d)throw new Error("Private key not found for wallet");const c=new a.ethers.Wallet(d,this.provider),l=new a.ethers.Contract(e,u.default,c),h=new a.ethers.Contract(t.BSC_CONFIG.routerAddress,g.default,c),y=yield l.balanceOf(s),p=yield l.decimals(),f=y.mul(n).div(100);if(f.isZero())throw new Error("No tokens to sell");if((yield l.allowance(s,t.BSC_CONFIG.routerAddress)).lt(f)){const n=yield l.approve(t.BSC_CONFIG.routerAddress,a.ethers.constants.MaxUint256);yield n.wait(),i.logger.info("Approved router to spend tokens",{tokenAddress:e,walletAddress:s,transactionHash:n.hash})}const m=[e,t.BSC_CONFIG.wbnbAddress],_=(null==o?void 0:o.slippage)||t.BSC_CONFIG.defaultSlippage,[,b]=yield h.getAmountsOut(f,m),v=b.mul(100-_).div(100),k=(null==o?void 0:o.gasLimit)||t.BSC_CONFIG.gasLimit,w=(null==o?void 0:o.gasPrice)?a.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice(),S=Math.floor(Date.now()/1e3)+1200,T=yield h.swapExactTokensForETHSupportingFeeOnTransferTokens(f,v,m,s,S,{gasLimit:k,gasPrice:w}),A=yield T.wait();return i.logger.info("BSC sell transaction successful",{transactionHash:A.transactionHash,blockNumber:A.blockNumber,gasUsed:A.gasUsed.toString(),tokenAddress:e,percentage:n}),i.logger.logPerformance("BSC sellTokens",r,{tokenAddress:e,transactionHash:A.transactionHash,percentage:n}),{success:!0,transactionHash:A.transactionHash,blockNumber:A.blockNumber,gasUsed:A.gasUsed.toNumber(),effectiveGasPrice:A.effectiveGasPrice.toString(),amountIn:a.ethers.utils.formatUnits(f,p),amountOut:a.ethers.utils.formatEther(v),timestamp:Date.now()}}catch(t){return i.logger.error("BSC sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:s,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=new a.ethers.Contract(e,u.default,this.provider),[n,r,s,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),d=yield(0,l.getTokenInfoFromDexscreener)(e,o.BlockchainType.BSC),c=yield this.checkHoneypot(e);return{name:n,symbol:r,decimals:s,totalSupply:a.ethers.utils.formatUnits(i,s),address:e,blockchain:o.BlockchainType.BSC,price:"string"==typeof(null==d?void 0:d.priceUsd)?parseFloat(d.priceUsd):(null==d?void 0:d.priceUsd)||0,marketCap:"string"==typeof(null==d?void 0:d.marketCap)?parseFloat(d.marketCap):(null==d?void 0:d.marketCap)||0,isHoneypot:(null==c?void 0:c.isHoneypot)||!1,honeypotInfo:c}}catch(t){return i.logger.error("Failed to get BSC token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:o.BlockchainType.BSC}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.provider.getBalance(e);return a.ethers.utils.formatEther(t)}catch(t){return i.logger.error("Failed to get BSC wallet balance",{error:t.message,walletAddress:e}),"0"}}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{const n=new a.ethers.Contract(e,u.default,this.provider),r=yield n.balanceOf(t),s=yield n.decimals();return a.ethers.utils.formatUnits(r,s)}catch(n){return i.logger.error("Failed to get BSC token balance",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,c.checkEVMHoneypot)(e,o.BlockchainType.BSC)}catch(t){return i.logger.error("Failed to check if BSC token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,d.generateBSCWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:o.BlockchainType.BSC})}catch(e){throw i.logger.error("Failed to generate BSC wallet",{error:e.message}),e}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}}t.BscTrader=h},5512:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.EthTrader=t.ETH_CONFIG=void 0;const a=n(9321),o=n(871),i=n(1213),d=n(4653),c=n(9088),l=n(9558),u=s(n(3883)),g=s(n(8938));t.ETH_CONFIG={rpcUrl:process.env.ETH_PROVIDER_URL||"https://mainnet.infura.io/v3/********************************",routerAddress:process.env.ETH_ROUTER_ADDRESS||"******************************************",wethAddress:process.env.ETH_WETH_ADDRESS||"******************************************",explorerUrl:"https://etherscan.io",gasLimit:35e4,defaultSlippage:10,apiUrl:process.env.ETHNODE_API_URL||"https://api.0x.org/swap/v1/quote"};class h extends o.BaseTrader{constructor(e){super(e),this.provider=new a.ethers.providers.JsonRpcProvider(t.ETH_CONFIG.rpcUrl),this.apiKey=process.env.MASTER_KEY||"",i.logger.info("ETH Trader initialized",{rpcUrl:t.ETH_CONFIG.rpcUrl})}getBlockchainType(){return o.BlockchainType.ETH}buyTokens(e,n,s,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Buying tokens on Ethereum",{tokenAddress:e,amount:n,walletAddress:s,options:o});try{const d=yield this.getPrivateKey(s);if(!d)throw new Error("Private key not found for wallet");const c=new a.ethers.Wallet(d,this.provider),l=a.ethers.utils.parseEther(n),u="******************************************",h=e,y=((null==o?void 0:o.slippage)||t.ETH_CONFIG.defaultSlippage)/100,p=process.env.EVM_ADMIN_ADDRESS||"",f=.08,m=`buyToken=${h}&sellToken=${u}&sellAmount=${l.toString()}&slippagePercentage=${y}&takerAddress=${s}&feeRecipient=${p}&buyTokenPercentageFee=${f}`,_=`${t.ETH_CONFIG.apiUrl}?${m}`,b=(yield g.default.get(_,{headers:{"0x-api-key":this.apiKey}})).data,v={to:b.to,data:b.data,value:a.ethers.BigNumber.from(b.value),gasLimit:(null==o?void 0:o.gasLimit)?a.ethers.BigNumber.from(o.gasLimit):a.ethers.BigNumber.from(t.ETH_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?a.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice()},k=yield c.sendTransaction(v),w=yield k.wait();return i.logger.info("ETH buy transaction successful",{transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toString(),tokenAddress:e}),i.logger.logPerformance("ETH buyTokens",r,{tokenAddress:e,transactionHash:w.transactionHash}),{success:!0,transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toNumber(),effectiveGasPrice:w.effectiveGasPrice.toString(),amountIn:n,amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("ETH buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:s}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,s,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Selling tokens on Ethereum",{tokenAddress:e,percentage:n,walletAddress:s,options:o});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const d=yield this.getPrivateKey(s);if(!d)throw new Error("Private key not found for wallet");const c=new a.ethers.Wallet(d,this.provider),l=new a.ethers.Contract(e,u.default,c),h=yield l.balanceOf(s),y=yield l.decimals(),p=h.mul(n).div(100);if(p.isZero())throw new Error("No tokens to sell");const f=process.env.EVM_SPENDER_ADDRESS||"";if((yield l.allowance(s,f)).lt(p)){const t=yield l.approve(f,a.ethers.constants.MaxUint256);yield t.wait(),i.logger.info("Approved spender to spend tokens",{tokenAddress:e,walletAddress:s,transactionHash:t.hash})}const m=e,_="******************************************",b=((null==o?void 0:o.slippage)||t.ETH_CONFIG.defaultSlippage)/100,v=process.env.EVM_ADMIN_ADDRESS||"",k=.08,w=`buyToken=${_}&sellToken=${m}&sellAmount=${p.toString()}&slippagePercentage=${b}&takerAddress=${s}&feeRecipient=${v}&buyTokenPercentageFee=${k}`,S=`${t.ETH_CONFIG.apiUrl}?${w}`,T=(yield g.default.get(S,{headers:{"0x-api-key":this.apiKey}})).data,A={to:T.to,data:T.data,value:a.ethers.BigNumber.from(T.value||0),gasLimit:(null==o?void 0:o.gasLimit)?a.ethers.BigNumber.from(o.gasLimit):a.ethers.BigNumber.from(t.ETH_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?a.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice()},O=yield c.sendTransaction(A),E=yield O.wait();return i.logger.info("ETH sell transaction successful",{transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toString(),tokenAddress:e,percentage:n}),i.logger.logPerformance("ETH sellTokens",r,{tokenAddress:e,transactionHash:E.transactionHash,percentage:n}),{success:!0,transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toNumber(),effectiveGasPrice:E.effectiveGasPrice.toString(),amountIn:a.ethers.utils.formatUnits(p,y),amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("ETH sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:s,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=new a.ethers.Contract(e,u.default,this.provider),[n,r,s,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),d=yield(0,l.getTokenInfoFromDexscreener)(e,o.BlockchainType.ETH),c=yield this.checkHoneypot(e);return{name:n,symbol:r,decimals:s,totalSupply:a.ethers.utils.formatUnits(i,s),address:e,blockchain:o.BlockchainType.ETH,price:"string"==typeof(null==d?void 0:d.priceUsd)?parseFloat(d.priceUsd):(null==d?void 0:d.priceUsd)||0,marketCap:"string"==typeof(null==d?void 0:d.marketCap)?parseFloat(d.marketCap):(null==d?void 0:d.marketCap)||0,isHoneypot:(null==c?void 0:c.isHoneypot)||!1,honeypotInfo:c}}catch(t){return i.logger.error("Failed to get ETH token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:o.BlockchainType.ETH}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.provider.getBalance(e);return a.ethers.utils.formatEther(t)}catch(t){return i.logger.error("Failed to get ETH wallet balance",{error:t.message,walletAddress:e}),"0"}}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{const n=new a.ethers.Contract(e,u.default,this.provider),r=yield n.balanceOf(t),s=yield n.decimals();return a.ethers.utils.formatUnits(r,s)}catch(n){return i.logger.error("Failed to get ETH token balance",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,c.checkEVMHoneypot)(e,o.BlockchainType.ETH)}catch(t){return i.logger.error("Failed to check if ETH token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,d.generateETHWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:o.BlockchainType.ETH})}catch(e){throw i.logger.error("Failed to generate ETH wallet",{error:e.message}),e}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}}t.EthTrader=h},9497:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.SolTrader=t.SOL_CONFIG=void 0;const a=n(8491),o=n(7018),i=n(871),d=n(1213),c=n(4653),l=n(2969),u=n(9558),g=s(n(8574)),h=s(n(6713));t.SOL_CONFIG={rpcUrl:process.env.SOL_PROVIDER_URL||"https://api.mainnet-beta.solana.com",adminPublicKey:process.env.SOL_ADMIN_PUBLIC_KEY||"",solanaAddress:"So11111111111111111111111111111111111111112",explorerUrl:"https://solscan.io",defaultSlippage:100,jupiterApiUrl:"https://quote-api.jup.ag/v6"};class y extends i.BaseTrader{constructor(e){super(e),this.connection=new a.Connection(t.SOL_CONFIG.rpcUrl),d.logger.info("SOL Trader initialized",{rpcUrl:t.SOL_CONFIG.rpcUrl})}getBlockchainType(){return i.BlockchainType.SOL}buyTokens(e,n,s,o){return r(this,void 0,void 0,(function*(){const r=Date.now();d.logger.info("Buying tokens on Solana",{tokenAddress:e,amount:n,walletAddress:s,options:o});try{const i=yield this.getPrivateKey(s);if(!i)throw new Error("Private key not found for wallet");const c=g.default.decode(i),l=a.Keypair.fromSecretKey(c),u=BigInt(Math.round(parseFloat(n)*a.LAMPORTS_PER_SOL)),h=.01*parseFloat(n),y=(BigInt(Math.round(h*a.LAMPORTS_PER_SOL)),(null==o?void 0:o.slippage)?100*o.slippage:t.SOL_CONFIG.defaultSlippage),p=yield this.getJupiterQuote(t.SOL_CONFIG.solanaAddress,e,u,y),f=yield this.getJupiterSwapTransaction(p,l.publicKey.toString()),m=Buffer.from(f,"base64"),_=a.VersionedTransaction.deserialize(m);t.SOL_CONFIG.adminPublicKey&&d.logger.info("Would add fee transfer instruction",{feeAmount:h,adminPublicKey:t.SOL_CONFIG.adminPublicKey}),_.sign([l]);const b=yield this.executeTransaction(_);return d.logger.info("SOL buy transaction successful",{transactionHash:b,tokenAddress:e}),d.logger.logPerformance("SOL buyTokens",r,{tokenAddress:e,transactionHash:b}),{success:!0,transactionHash:b,amountIn:n,timestamp:Date.now()}}catch(t){return d.logger.error("SOL buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:s}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,s,i){return r(this,void 0,void 0,(function*(){const r=Date.now();d.logger.info("Selling tokens on Solana",{tokenAddress:e,percentage:n,walletAddress:s,options:i});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const c=yield this.getPrivateKey(s);if(!c)throw new Error("Private key not found for wallet");const l=g.default.decode(c),u=a.Keypair.fromSecretKey(l),h=yield this.getTokenBalance(e,s);if("0"===h)throw new Error("No token balance found");const y=new a.PublicKey(e),p=(yield(0,o.getMint)(this.connection,y)).decimals,f=parseFloat(h),m=BigInt(Math.floor(f*Math.pow(10,p)*n/100));if(m<=BigInt(0))throw new Error("No tokens to sell");const _=.01*Number(m),b=m-BigInt(Math.floor(_)),v=(null==i?void 0:i.slippage)?100*i.slippage:t.SOL_CONFIG.defaultSlippage,k=yield this.getJupiterQuote(e,t.SOL_CONFIG.solanaAddress,b,v),w=yield this.getJupiterSwapTransaction(k,u.publicKey.toString()),S=Buffer.from(w,"base64"),T=a.VersionedTransaction.deserialize(S);t.SOL_CONFIG.adminPublicKey&&d.logger.info("Would add fee transfer instruction",{feeAmountInTokens:_,adminPublicKey:t.SOL_CONFIG.adminPublicKey}),T.sign([u]);const A=yield this.executeTransaction(T);return d.logger.info("SOL sell transaction successful",{transactionHash:A,tokenAddress:e,percentage:n}),d.logger.logPerformance("SOL sellTokens",r,{tokenAddress:e,transactionHash:A,percentage:n}),{success:!0,transactionHash:A,amountIn:m.toString(),timestamp:Date.now()}}catch(t){return d.logger.error("SOL sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:s,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.connection.getTokenSupply(new a.PublicKey(e)),n=yield(0,u.getTokenInfoFromDexscreener)(e,i.BlockchainType.SOL),r=yield this.checkHoneypot(e);return{name:(null==n?void 0:n.name)||"Unknown",symbol:(null==n?void 0:n.symbol)||"UNKNOWN",decimals:t.value.decimals,totalSupply:t.value.amount,address:e,blockchain:i.BlockchainType.SOL,price:"string"==typeof(null==n?void 0:n.priceUsd)?parseFloat(n.priceUsd):(null==n?void 0:n.priceUsd)||0,marketCap:"string"==typeof(null==n?void 0:n.marketCap)?parseFloat(n.marketCap):(null==n?void 0:n.marketCap)||0,isHoneypot:(null==r?void 0:r.isHoneypot)||!1,honeypotInfo:r}}catch(t){return d.logger.error("Failed to get SOL token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:9,address:e,blockchain:i.BlockchainType.SOL}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=new a.PublicKey(e);return((yield this.connection.getBalance(t))/a.LAMPORTS_PER_SOL).toFixed(9)}catch(t){return d.logger.error("Failed to get SOL wallet balance",{error:t.message,walletAddress:e}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,l.checkIfHoneypot)(e,i.BlockchainType.SOL)}catch(t){return d.logger.error("Failed to check if SOL token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,c.generateSOLWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:i.BlockchainType.SOL})}catch(e){throw d.logger.error("Failed to generate SOL wallet",{error:e.message}),e}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}getJupiterQuote(e,n,s,a){return r(this,void 0,void 0,(function*(){const r=yield(0,h.default)(`${t.SOL_CONFIG.jupiterApiUrl}/quote?inputMint=${e}&outputMint=${n}&amount=${s}&slippageBps=${a}`),o=yield r.json();if(!o)throw new Error("Failed to get quote data from Jupiter");return o}))}getJupiterSwapTransaction(e,n){return r(this,void 0,void 0,(function*(){const r=yield(0,h.default)(`${t.SOL_CONFIG.jupiterApiUrl}/swap`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({quoteResponse:e,userPublicKey:n,wrapAndUnwrapSol:!0,dynamicComputeUnitLimit:!0,prioritizationFeeLamports:2e6,autoMultiplier:2})}),s=yield r.json();if(!s||!s.swapTransaction)throw new Error("Failed to get swap transaction data from Jupiter");return s.swapTransaction}))}executeTransaction(e){return r(this,void 0,void 0,(function*(){const t=e.serialize(),n=yield this.connection.sendRawTransaction(t,{skipPreflight:!0,maxRetries:3}),r=yield this.connection.confirmTransaction(n,"confirmed");if(r.value.err)throw new Error(`Transaction failed: ${r.value.err.toString()}`);return n}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{const n=new a.PublicKey(t),r=new a.PublicKey(e),s=yield this.connection.getParsedTokenAccountsByOwner(n,{mint:r});if(0===s.value.length)return"0";const o=s.value[0].account.data.parsed.info,i=Number(o.tokenAmount.amount),d=o.tokenAmount.decimals;return(i/Math.pow(10,d)).toString()}catch(n){return d.logger.error("Failed to get token balance",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}}t.SolTrader=y},871:function(e,t){var n,r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTrader=t.TraderFactory=t.BlockchainType=void 0,function(e){e.BSC="bsc",e.ETH="eth",e.SOL="sol",e.BASE="base"}(n||(t.BlockchainType=n={}));class s{static registerTrader(e){this.traders.set(e.getBlockchainType(),e)}static getTrader(e){const t=this.traders.get(e);if(!t)throw new Error(`No trader registered for blockchain: ${e}`);return t}static hasTrader(e){return this.traders.has(e)}}t.TraderFactory=s,s.traders=new Map,t.BaseTrader=class{constructor(e){this.bot=e}sendMessage(e,t){return r(this,void 0,void 0,(function*(){try{yield this.bot.sendMessage(e,t,{parse_mode:"Markdown"})}catch(e){console.error("Failed to send message:",e)}}))}}},3003:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.initializeTraders=function(e){try{const t=new o.BscTrader(e),n=new i.EthTrader(e),r=new d.SolTrader(e),s=new c.BaseNetworkTrader(e);a.TraderFactory.registerTrader(t),a.TraderFactory.registerTrader(n),a.TraderFactory.registerTrader(r),a.TraderFactory.registerTrader(s),l.logger.info("All blockchain traders initialized and registered")}catch(e){throw l.logger.error("Failed to initialize traders",{error:e.message}),e}},t.getTrader=function(e){try{return a.TraderFactory.getTrader(e)}catch(t){throw l.logger.error("Failed to get trader",{blockchain:e,error:t.message}),t}};const a=n(871),o=n(1583),i=n(5512),d=n(9497),c=n(6086),l=n(1213);s(n(871),t),s(n(1583),t),s(n(5512),t),s(n(9497),t),s(n(6086),t)},5943:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.UserService=void 0;const s=n(6131);t.UserService={createUser:e=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.create(e)}catch(e){throw console.log(e),new Error(e.message)}})),findById:e=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.findById(e)}catch(e){throw new Error(e.message)}})),findOne:e=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.findOne(Object.assign(Object.assign({},e),{retired:!1}))}catch(e){throw new Error(e.message)}})),findLastOne:e=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.findOne(e).sort({updatedAt:-1})}catch(e){throw new Error(e.message)}})),find:e=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.find(e)}catch(e){throw new Error(e.message)}})),findAndSort:e=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.find(e).sort({retired:1,nonce:1}).exec()}catch(e){throw new Error(e.message)}})),updateOne:(e,t)=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.findByIdAndUpdate(e,t,{new:!0})}catch(e){throw new Error(e.message)}})),findAndUpdateOne:(e,t)=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.findOneAndUpdate(e,t,{new:!0})}catch(e){throw new Error(e.message)}})),updateMany:(e,t)=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.updateMany(e,{$set:t})}catch(e){throw new Error(e.message)}})),deleteOne:e=>r(void 0,void 0,void 0,(function*(){try{return yield s.UserSchema.findOneAndDelete(e)}catch(e){throw new Error(e.message)}})),extractUniqueCode:e=>{const t=e.split(" ");return t.length>1?t[1]:null},extractPNLdata:e=>{const t=e.split(" ");return t.length>1&&t[1].endsWith("png")?t[1].replace("png",".png"):null}}},7095:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);s&&!("get"in s?!t.__esModule:s.writable||s.configurable)||(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.initializeUtilityServices=function(){const{logger:e}=n(1213);e.info("Initializing utility services..."),e.info("All utility services initialized successfully")},s(n(4653),t),s(n(6325),t),s(n(5943),t)},4653:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.generateBASEWallet=t.generateSOLWallet=t.generateBSCWallet=t.generateETHWallet=void 0;const s=n(8491),a=n(9321),o=r(n(8574));t.generateETHWallet=()=>{const e=a.ethers.Wallet.createRandom();return{blockchain:"eth",network:"mainnet",address:e.address,privateKey:e.privateKey,rpcUrl:"https://mainnet.infura.io/v3/********************************",mnemonic:e.mnemonic.phrase}},t.generateBSCWallet=()=>{const e=a.ethers.Wallet.createRandom();return{blockchain:"bsc",network:"mainnet",address:e.address,privateKey:e.privateKey,rpcUrl:"https://bsc-dataseed.binance.org/",mnemonic:e.mnemonic.phrase}},t.generateSOLWallet=()=>{const e=s.Keypair.generate();return{blockchain:"sol",network:"mainnet",address:e.publicKey.toString(),privateKey:o.default.encode(e.secretKey),rpcUrl:"https://api.mainnet-beta.solana.com"}},t.generateBASEWallet=()=>{try{const e=a.ethers.Wallet.createRandom();return{blockchain:"base",network:"mainnet",address:e.address,privateKey:e.privateKey,rpcUrl:"https://mainnet.base.org/",mnemonic:e.mnemonic.phrase}}catch(e){throw new Error("Failed to generate BASE wallet. Consult BASE documentation.")}}},2941:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.loadPreviousValues=function(e){if(a.default.existsSync(y)){const e=a.default.readFileSync(y,"utf-8");p=JSON.parse(e)}else!function(){r(this,void 0,void 0,(function*(){try{const e=yield m();for(const t of["eth","bsc","sol","base"])for(const n of e[t]){let e="";"eth"===t?e=yield _(n.address):"bsc"===t?e=yield b(n.address):"sol"===t?e=yield v(n.address):"base"===t&&(e=yield k(n.address));const r=yield i.default.findOne({[`${t}_wallet.address`]:n.address}).exec();r&&(p[n.address]||(p[n.address]={}),p[n.address][t]={balance:e.toString(),chat_id:r.chat_id})}f()}catch(e){console.error("Error initializing previous values:",e.message)}}))}()},t.startMonitoring=function(e){setInterval((()=>function(e){return r(this,void 0,void 0,(function*(){var t;try{const n=yield m();for(const r of["eth","bsc","sol","base"])for(const s of n[r]){let n="";"eth"===r?n=yield _(s.address):"bsc"===r?n=yield b(s.address):"sol"===r?n=yield v(s.address):"base"===r&&(n=yield k(s.address));const a=null===(t=p[s.address])||void 0===t?void 0:t[r];if(!a||n.toString()!==a.balance){const t=yield i.default.findOne({[`${r}_wallet.address`]:s.address}).exec();t&&(w(e,s.address,r,n.toString(),t.chat_id),p[s.address]||(p[s.address]={}),p[s.address][r]={balance:n.toString(),chat_id:t.chat_id})}}f()}catch(e){console.error("Error monitoring wallet values:",e.message)}}))}(e)),S)};const a=s(n(9896)),o=s(n(6928)),i=s(n(8722)),d=n(9321),c=n(8491),l=new d.ethers.providers.JsonRpcProvider("https://eth.llamarpc.com"),u=new d.ethers.providers.JsonRpcProvider("https://bsc-dataseed.binance.org/"),g=new c.Connection("https://api.mainnet-beta.solana.com","confirmed"),h=new d.ethers.providers.JsonRpcProvider("https://mainnet.base.org"),y=o.default.resolve(__dirname,"previousValues.json");let p={};function f(){a.default.writeFileSync(y,JSON.stringify(p,null,2))}function m(){return r(this,void 0,void 0,(function*(){const e={base:[],eth:[],sol:[],bsc:[]};try{const t=yield i.default.find().exec();for(const n of t)n.base_wallet&&e.base.push(n.base_wallet),n.eth_wallet&&e.eth.push(n.eth_wallet),n.sol_wallet&&e.sol.push(n.sol_wallet),n.bsc_wallet&&e.bsc.push(n.bsc_wallet);return e}catch(e){throw console.error("Error fetching and categorizing wallets:",e.message),new Error("Failed to fetch and categorize wallets.")}}))}function _(e){return r(this,void 0,void 0,(function*(){const t=yield l.getBalance(e);return d.ethers.utils.formatEther(t)}))}function b(e){return r(this,void 0,void 0,(function*(){const t=yield u.getBalance(e);return d.ethers.utils.formatEther(t)}))}function v(e){return r(this,void 0,void 0,(function*(){return(yield g.getBalance(new c.PublicKey(e)))/1e9}))}function k(e){return r(this,void 0,void 0,(function*(){const t=yield h.getBalance(e);return d.ethers.utils.formatEther(t)}))}function w(e,t,n,r,s){const a=parseFloat(r).toFixed(6),o=`Hello, ${s}: your ${n.toUpperCase()} Wallet ${t} has a new balance: ${a}`;e.sendMessage(s,o)}const S=1e4},904:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.JobQueue=t.JobStatus=void 0;const s=n(6261),a=n(1213),o=n(3903);var i;!function(e){e.PENDING="pending",e.PROCESSING="processing",e.COMPLETED="completed",e.FAILED="failed",e.RETRYING="retrying",e.STUCK="stuck"}(i||(t.JobStatus=i={}));class d extends s.EventEmitter{constructor(e=5){super(),this.queue=[],this.processing=!1,this.handlers=new Map,this.activeJobs=new Map,this.paused=!1,this.drainPromise=null,this.drainResolve=null,this.stuckJobCheckInterval=null,this.STUCK_JOB_THRESHOLD=3e5,this.concurrency=e,this.metrics={totalJobs:0,completedJobs:0,failedJobs:0,retriedJobs:0,processingTime:{},successRate:{},startTime:new Date},a.logger.info(`JobQueue initialized with concurrency ${e}`),this.setupEventListeners(),this.startStuckJobChecker()}setupEventListeners(){this.on("job:added",(e=>{a.logger.debug(`Job added: ${e.type} (${e.id})`,{jobId:e.id,jobType:e.type}),this.metrics.totalJobs++})),this.on("job:processing",(e=>{a.logger.debug(`Processing job: ${e.type} (${e.id})`,{jobId:e.id,jobType:e.type,attempt:e.attempts})})),this.on("job:completed",((e,t)=>{var n;const r=e.completedAt?e.completedAt.getTime()-((null===(n=e.startedAt)||void 0===n?void 0:n.getTime())||0):0;a.logger.debug(`Job completed: ${e.type} (${e.id}) in ${r}ms`,{jobId:e.id,jobType:e.type,durationMs:r}),this.metrics.completedJobs++,this.metrics.processingTime[e.type]||(this.metrics.processingTime[e.type]=[]),this.metrics.processingTime[e.type].push(r),this.metrics.successRate[e.type]||(this.metrics.successRate[e.type]={success:0,total:0}),this.metrics.successRate[e.type].success++,this.metrics.successRate[e.type].total++})),this.on("job:failed",((e,t)=>{a.logger.warn(`Job failed: ${e.type} (${e.id})`,{jobId:e.id,jobType:e.type,error:t.message,attempt:e.attempts,maxAttempts:e.maxAttempts}),this.metrics.successRate[e.type]||(this.metrics.successRate[e.type]={success:0,total:0}),this.metrics.successRate[e.type].total++,e.attempts>=e.maxAttempts?this.metrics.failedJobs++:this.metrics.retriedJobs++})),this.on("job:max-attempts-reached",((e,t)=>{a.logger.error(`Job failed after max attempts: ${e.type} (${e.id})`,{jobId:e.id,jobType:e.type,error:t.message,attempts:e.attempts})}))}registerHandler(e,t){this.handlers.set(e,t),a.logger.debug(`Registered handler for job type: ${e}`)}addJob(e,t,n={}){const r=(0,o.v4)(),s={id:r,type:e,data:t,priority:n.priority||0,createdAt:new Date,attempts:0,maxAttempts:n.maxAttempts||3,status:i.PENDING,retryDelay:n.retryDelay||1e3,tags:n.tags||[]};return this.queue.push(s),this.queue.sort(((e,t)=>t.priority-e.priority)),this.emit("job:added",s),this.processing||this.paused||this.processQueue(),r}processQueue(){return r(this,void 0,void 0,(function*(){if(!this.processing&&!this.paused){this.processing=!0;try{for(;this.queue.length>0&&this.activeJobs.size<this.concurrency&&!this.paused;){const e=this.queue.shift();e&&(e.status=i.PROCESSING,e.startedAt=new Date,this.activeJobs.set(e.id,e),this.processJob(e).catch((t=>{a.logger.error(`Unexpected error processing job ${e.id}`,{error:t.message})})).finally((()=>{this.activeJobs.delete(e.id),this.drainResolve&&0===this.queue.length&&0===this.activeJobs.size&&(this.drainResolve(),this.drainPromise=null,this.drainResolve=null),this.queue.length>0&&!this.paused?this.processQueue():0===this.activeJobs.size&&(this.processing=!1)})))}}catch(e){const t=e instanceof Error?e.message:String(e);a.logger.error("Error in processQueue",{error:t}),this.processing=!1}0===this.activeJobs.size&&(this.processing=!1)}}))}processJob(e){return r(this,void 0,void 0,(function*(){const t=this.handlers.get(e.type);if(!t){const t=new Error(`No handler registered for job type: ${e.type}`);return e.error=t,e.status=i.FAILED,void this.emit("job:error",e,t)}e.attempts++,this.emit("job:processing",e);const n=Date.now();try{const n=yield t(e);e.completedAt=new Date,e.status=i.COMPLETED,e.result=n,this.emit("job:completed",e,n)}catch(t){const n=t instanceof Error?t:new Error(String(t));if(e.error=n,this.emit("job:failed",e,t),e.attempts<e.maxAttempts){e.status=i.RETRYING,e.priority-=1;const t=e.retryDelay?e.retryDelay*Math.pow(2,e.attempts-1):0;a.logger.debug(`Retrying job ${e.id} in ${t}ms`,{jobId:e.id,attempt:e.attempts,maxAttempts:e.maxAttempts,retryDelay:t}),setTimeout((()=>{this.queue.push(e),this.queue.sort(((e,t)=>t.priority-e.priority)),this.processing||this.paused||this.processQueue()}),t)}else e.status=i.FAILED,this.emit("job:max-attempts-reached",e,t)}const r=Date.now()-n;a.logger.debug(`Job ${e.id} processed in ${r}ms`,{jobId:e.id,jobType:e.type,status:e.status,durationMs:r})}))}getJob(e){return this.activeJobs.has(e)?this.activeJobs.get(e)||null:this.queue.find((t=>t.id===e))||null}getJobsByType(e){return[...Array.from(this.activeJobs.values()).filter((t=>t.type===e)),...this.queue.filter((t=>t.type===e))]}getJobsByTag(e){return[...Array.from(this.activeJobs.values()).filter((t=>{var n;return null===(n=t.tags)||void 0===n?void 0:n.includes(e)})),...this.queue.filter((t=>{var n;return null===(n=t.tags)||void 0===n?void 0:n.includes(e)}))]}pause(){this.paused=!0,a.logger.info("Job queue paused")}resume(){this.paused=!1,a.logger.info("Job queue resumed"),this.queue.length>0&&!this.processing&&this.processQueue()}drain(e){return 0===this.queue.length&&0===this.activeJobs.size?Promise.resolve():(this.drainPromise||(this.drainPromise=new Promise(((t,n)=>{this.drainResolve=t,e&&setTimeout((()=>{this.drainResolve&&(n(new Error(`Drain timed out after ${e}ms`)),this.drainPromise=null,this.drainResolve=null)}),e)}))),this.drainPromise)}clearQueue(e){const t=this.queue.length;this.queue=e?this.queue.filter((t=>t.type!==e)):[];const n=t-this.queue.length;return a.logger.info(`Cleared ${n} jobs from queue`,{jobType:e||"all"}),n}getQueueLength(){return this.queue.length}getActiveJobsCount(){return this.activeJobs.size}startStuckJobChecker(){this.stuckJobCheckInterval=setInterval((()=>{this.checkForStuckJobs()}),6e4),a.logger.debug("Stuck job checker started")}checkForStuckJobs(){const e=Date.now();let t=0;for(const[n,r]of this.activeJobs.entries())r.startedAt&&e-r.startedAt.getTime()>this.STUCK_JOB_THRESHOLD&&(a.logger.warn(`Job ${n} appears to be stuck`,{jobId:n,jobType:r.type,startedAt:r.startedAt,elapsedMs:e-r.startedAt.getTime()}),r.status=i.STUCK,r.error=new Error(`Job appears to be stuck (processing for ${Math.round((e-r.startedAt.getTime())/1e3/60)} minutes)`),this.activeJobs.delete(n),r.attempts<r.maxAttempts&&(r.priority+=5,this.queue.push(r),this.queue.sort(((e,t)=>t.priority-e.priority)),t++));t>0&&(a.logger.info(`Found and requeued ${t} stuck jobs`),this.processing||this.paused||this.processQueue())}getStuckJobs(){return this.queue.filter((e=>e.status===i.STUCK))}clearStuckJobs(){const e=this.getStuckJobs();return this.queue=this.queue.filter((e=>e.status!==i.STUCK)),e.length}isPaused(){return this.paused}getMetrics(){return this.metrics}}t.JobQueue=d},3653:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.WorkerManager=void 0,t.createWorkerManager=function(e,t={}){return new g(e,t)};const a=s(n(857)),o=n(1602),i=n(904),d=n(1213),c=n(6261),l=n(8708),u=Math.max(1,a.default.cpus().length-1);class g extends c.EventEmitter{constructor(e,t={}){super(),this.recycleInterval=null,this.healthCheckInterval=null,this.metricsInterval=null,this.isShuttingDown=!1,this.startTime=new Date,this.workerFile=e,this.workerCount=t.workerCount||u,this.concurrency=t.concurrency||10,this.workerPool=new o.WorkerPool(e,this.workerCount),this.jobQueue=new i.JobQueue(this.concurrency),this.health={status:"healthy",activeWorkers:0,totalWorkers:this.workerCount,queueLength:0,activeJobs:0,memoryUsage:process.memoryUsage(),uptime:0,lastUpdated:new Date},!1!==t.enableRecycling&&this.startWorkerRecycling(),!1!==t.enableHealthCheck&&this.startHealthCheck(),!1!==t.enableMetrics&&this.startMetricsCollection(),this.setupEventListeners(),d.logger.info("WorkerManager initialized",{workerCount:this.workerCount,concurrency:this.concurrency,recycling:!1!==t.enableRecycling,healthCheck:!1!==t.enableHealthCheck,metrics:!1!==t.enableMetrics})}setupEventListeners(){this.jobQueue.on("job:added",(e=>{this.emit("job:added",e)})),this.jobQueue.on("job:completed",((e,t)=>{this.emit("job:completed",e,t)})),this.jobQueue.on("job:failed",((e,t)=>{this.emit("job:failed",e,t)})),this.jobQueue.on("job:max-attempts-reached",((e,t)=>{this.emit("job:max-attempts-reached",e,t)})),this.workerPool.on("worker:added",(e=>{this.emit("worker:added",e)})),this.workerPool.on("worker:removed",(e=>{this.emit("worker:removed",e)})),this.workerPool.on("worker:error",((e,t)=>{this.emit("worker:error",e,t)}))}startWorkerRecycling(){this.recycleInterval&&clearInterval(this.recycleInterval),this.recycleInterval=setInterval((()=>{this.recycleWorkers()}),18e5),d.logger.info("Worker recycling started",{interval:30,unit:"minutes"})}recycleWorkers(){return r(this,void 0,void 0,(function*(){try{d.logger.info("Recycling workers to prevent memory leaks");const e=process.memoryUsage(),t=a.default.totalmem(),n=1-a.default.freemem()/t;d.logger.debug("Current memory usage",{usedRatio:n,heapUsed:e.heapUsed/1024/1024,rss:e.rss/1024/1024,unit:"MB"});const r=n>.8?Math.ceil(this.workerCount/2):1;yield this.workerPool.recycleWorkers(r),d.logger.info("Workers recycled successfully",{count:r})}catch(e){d.logger.error("Error recycling workers",{error:e.message})}}))}startHealthCheck(){this.healthCheckInterval&&clearInterval(this.healthCheckInterval),this.healthCheckInterval=setInterval((()=>{this.checkHealth()}),6e4),d.logger.info("Health check started",{interval:60,unit:"seconds"})}checkHealth(){return r(this,void 0,void 0,(function*(){try{const e=this.workerPool.getActiveWorkerCount(),t=this.workerPool.getTotalWorkerCount(),n=this.jobQueue.getQueueLength(),r=this.jobQueue.getActiveJobsCount(),s=process.memoryUsage(),a=(Date.now()-this.startTime.getTime())/1e3;let o="healthy";e<.5*t&&(o="degraded"),(0===e||n>100)&&(o="unhealthy"),this.health={status:o,activeWorkers:e,totalWorkers:t,queueLength:n,activeJobs:r,memoryUsage:s,uptime:a,lastUpdated:new Date},yield l.cacheService.set("worker:health",this.health,300),"healthy"!==o&&(d.logger.warn("Worker system health degraded",{status:o,activeWorkers:e,totalWorkers:t,queueLength:n,activeJobs:r}),"unhealthy"===o&&(yield this.attemptRecovery())),this.emit("health:updated",this.health)}catch(e){d.logger.error("Error checking worker health",{error:e.message})}}))}attemptRecovery(){return r(this,void 0,void 0,(function*(){d.logger.info("Attempting to recover worker system");try{yield this.workerPool.recycleWorkers(this.workerCount);const e=this.jobQueue.getStuckJobs();e.length>0&&(d.logger.info(`Clearing ${e.length} stuck jobs`),this.jobQueue.clearStuckJobs()),this.jobQueue.isPaused()&&this.jobQueue.resume(),d.logger.info("Recovery attempt completed")}catch(e){d.logger.error("Error during recovery attempt",{error:e.message})}}))}startMetricsCollection(){this.metricsInterval&&clearInterval(this.metricsInterval),this.metricsInterval=setInterval((()=>{this.collectMetrics()}),1e4),d.logger.info("Metrics collection started",{interval:10,unit:"seconds"})}collectMetrics(){return r(this,void 0,void 0,(function*(){try{const e=this.jobQueue.getMetrics(),t=this.jobQueue.getQueueLength(),n=this.jobQueue.getActiveJobsCount();let r=0,s=0;for(const[t,n]of Object.entries(e.processingTime))r+=n.reduce(((e,t)=>e+t),0),s+=n.length;const a=s>0?r/s:0,o={};for(const[t,n]of Object.entries(e.processingTime))o[t]=n.length>0?n.reduce(((e,t)=>e+t),0)/n.length:0;const i={};for(const[t,n]of Object.entries(e.successRate))i[t]=n.total>0?n.success/n.total*100:0;const d={jobsProcessed:e.totalJobs,jobsSucceeded:e.completedJobs,jobsFailed:e.failedJobs,averageProcessingTime:a,processingTimeByJobType:o,successRateByJobType:i,queueLength:t,activeJobs:n};yield l.cacheService.set("worker:metrics",d,300),this.emit("metrics:updated",d)}catch(e){d.logger.error("Error collecting metrics",{error:e.message})}}))}registerHandler(e,t){this.jobQueue.registerHandler(e,t)}addJob(e,t,n={}){return this.jobQueue.addJob(e,t,n)}getJob(e){return this.jobQueue.getJob(e)}getHealth(){return this.health}getMetrics(){const e=this.jobQueue.getMetrics(),t=this.jobQueue.getQueueLength(),n=this.jobQueue.getActiveJobsCount();let r=0,s=0;for(const[t,n]of Object.entries(e.processingTime))r+=n.reduce(((e,t)=>e+t),0),s+=n.length;const a=s>0?r/s:0,o={};for(const[t,n]of Object.entries(e.processingTime))o[t]=n.length>0?n.reduce(((e,t)=>e+t),0)/n.length:0;const i={};for(const[t,n]of Object.entries(e.successRate))i[t]=n.total>0?n.success/n.total*100:0;return{jobsProcessed:e.totalJobs,jobsSucceeded:e.completedJobs,jobsFailed:e.failedJobs,averageProcessingTime:a,processingTimeByJobType:o,successRateByJobType:i,queueLength:t,activeJobs:n}}shutdown(){return r(this,arguments,void 0,(function*(e=3e4){if(!this.isShuttingDown){this.isShuttingDown=!0,d.logger.info("Shutting down worker system",{timeout:e}),this.recycleInterval&&(clearInterval(this.recycleInterval),this.recycleInterval=null),this.healthCheckInterval&&(clearInterval(this.healthCheckInterval),this.healthCheckInterval=null),this.metricsInterval&&(clearInterval(this.metricsInterval),this.metricsInterval=null),this.jobQueue.pause();try{const t=this.jobQueue.drain(e),n=new Promise((t=>{setTimeout((()=>{d.logger.warn("Job queue drain timed out"),t()}),e)}));yield Promise.race([t,n]),yield this.workerPool.shutdown(),d.logger.info("Worker system shut down successfully")}catch(e){d.logger.error("Error during worker system shutdown",{error:e.message})}}}))}}t.WorkerManager=g},1602:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(s,a){function o(e){try{d(r.next(e))}catch(e){a(e)}}function i(e){try{d(r.throw(e))}catch(e){a(e)}}function d(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.WorkerPool=void 0;const a=n(6261),o=n(8167),i=s(n(6928)),d=s(n(857)),c=n(1213),l=s(n(6982));class u extends a.EventEmitter{constructor(e,t=Math.max(1,d.default.cpus().length-1)){super(),this.workers=new Map,this.taskQueue=[],this.workerTaskMap=new Map,this.nextWorkerId=1,this.isShuttingDown=!1,this.numWorkers=t,this.workerFile=e,this.init(),c.logger.info("WorkerPool initialized",{workerCount:t,workerFile:e})}init(){for(let e=0;e<this.numWorkers;e++)this.addNewWorker()}addNewWorker(){const e=this.nextWorkerId++;try{const t=new o.Worker(i.default.resolve(this.workerFile)),n={id:e,worker:t,createdAt:new Date,tasksProcessed:0,lastTaskTime:null,isProcessing:!1};return t.on("message",(t=>{this.handleWorkerMessage(e,t)})),t.on("error",(t=>{this.handleWorkerError(e,t)})),t.on("exit",(t=>{this.handleWorkerExit(e,t)})),this.workers.set(e,n),this.workerTaskMap.set(e,null),this.emit("worker:added",e),c.logger.debug(`Worker ${e} added to pool`),e}catch(t){return c.logger.error(`Failed to create worker ${e}`,{error:t.message}),setTimeout((()=>{!this.isShuttingDown&&this.workers.size<this.numWorkers&&this.addNewWorker()}),1e3),-1}}handleWorkerMessage(e,t){const n=this.workers.get(e);if(!n)return;const r=this.workerTaskMap.get(e);if(r){n.tasksProcessed++,n.lastTaskTime=new Date,n.isProcessing=!1,this.workerTaskMap.set(e,null);const s=Date.now()-r.createdAt.getTime();c.logger.debug(`Worker ${e} completed task ${r.id} in ${s}ms`,{workerId:e,taskId:r.id,taskType:r.type,durationMs:s}),r.callback(null,t),this.processQueue()}}handleWorkerError(e,t){if(c.logger.error(`Worker ${e} error`,{workerId:e,error:t.message}),!this.workers.get(e))return;const n=this.workerTaskMap.get(e);n&&(this.workerTaskMap.set(e,null),n.callback(t)),this.removeWorker(e),this.isShuttingDown||(this.addNewWorker(),this.processQueue())}handleWorkerExit(e,t){0!==t?(c.logger.warn(`Worker ${e} exited with code ${t}`,{workerId:e,exitCode:t}),this.removeWorker(e),this.isShuttingDown||this.addNewWorker()):(c.logger.debug(`Worker ${e} exited normally`),this.removeWorker(e))}removeWorker(e){const t=this.workers.get(e);if(t){try{t.worker.terminate().catch((t=>{c.logger.error(`Error terminating worker ${e}`,{workerId:e,error:t.message})}))}catch(t){c.logger.error(`Error removing worker ${e}`,{workerId:e,error:t.message})}this.workers.delete(e),this.workerTaskMap.delete(e),this.emit("worker:removed",e),c.logger.debug(`Worker ${e} removed from pool`)}}runTask(e,t){return new Promise(((n,r)=>{const s=l.default.randomUUID(),a={id:s,type:e,data:t,callback:(e,t)=>{e?r(e):n(t)},createdAt:new Date};this.taskQueue.push(a),c.logger.debug(`Task ${s} added to queue`,{taskId:s,taskType:e,queueLength:this.taskQueue.length}),this.processQueue()}))}processQueue(){if(0!==this.taskQueue.length&&!this.isShuttingDown)for(const[e,t]of this.workers.entries())if(!this.workerTaskMap.get(e)){const n=this.taskQueue.shift();if(!n)break;t.isProcessing=!0,this.workerTaskMap.set(e,n);try{t.worker.postMessage({type:n.type,data:n.data}),c.logger.debug(`Worker ${e} processing task ${n.id}`,{workerId:e,taskId:n.id,taskType:n.type})}catch(t){c.logger.error(`Error posting message to worker ${e}`,{workerId:e,taskId:n.id,error:t.message}),this.workerTaskMap.set(e,null),n.callback(t),this.removeWorker(e),this.isShuttingDown||this.addNewWorker()}break}}recycleWorkers(){return r(this,arguments,void 0,(function*(e=1){if(this.isShuttingDown)return;e=Math.min(e,this.workers.size),c.logger.info(`Recycling ${e} workers`);const t=Array.from(this.workers.entries()).sort(((e,t)=>e[1].createdAt.getTime()-t[1].createdAt.getTime())).map((([e])=>e)).slice(0,e);for(let t=0;t<e;t++)this.addNewWorker();yield new Promise((e=>setTimeout(e,500)));for(const e of t)this.workerTaskMap.get(e)||this.removeWorker(e);c.logger.info(`Recycled ${e} workers`)}))}getActiveWorkerCount(){return Array.from(this.workerTaskMap.values()).filter((e=>null!==e)).length}getTotalWorkerCount(){return this.workers.size}getQueueLength(){return this.taskQueue.length}shutdown(){return r(this,void 0,void 0,(function*(){if(this.isShuttingDown)return;this.isShuttingDown=!0,c.logger.info("Shutting down worker pool");const e=Array.from(this.workers.values()).map((e=>e.worker.terminate()));yield Promise.all(e),this.workers.clear(),this.workerTaskMap.clear(),c.logger.info("Worker pool shut down successfully")}))}}t.WorkerPool=u},7018:e=>{e.exports=require("@solana/spl-token")},8491:e=>{e.exports=require("@solana/web3.js")},8938:e=>{e.exports=require("axios")},8574:e=>{e.exports=require("bs58")},6713:e=>{e.exports=require("cross-fetch")},818:e=>{e.exports=require("dotenv")},9321:e=>{e.exports=require("ethers")},6261:e=>{e.exports=require("events")},7252:e=>{e.exports=require("express")},6037:e=>{e.exports=require("mongoose")},6209:e=>{e.exports=require("node-telegram-bot-api")},4437:e=>{e.exports=require("socket.io")},3903:e=>{e.exports=require("uuid")},9907:e=>{e.exports=require("cluster")},6982:e=>{e.exports=require("crypto")},9896:e=>{e.exports=require("fs")},8611:e=>{e.exports=require("http")},857:e=>{e.exports=require("os")},6928:e=>{e.exports=require("path")},8167:e=>{e.exports=require("worker_threads")},3883:e=>{e.exports=JSON.parse('[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"minTokensBeforeSwap","type":"uint256"}],"name":"MinTokensBeforeSwapUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"tokensSwapped","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"ethReceived","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"tokensIntoLiqudity","type":"uint256"}],"name":"SwapAndLiquify","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"SwapAndLiquifyEnabledUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"inputs":[],"name":"_charityFee","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"_liquidityFee","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"_maxTxAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"_taxFee","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"subtractedValue","type":"uint256"}],"name":"decreaseAllowance","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"tAmount","type":"uint256"}],"name":"deliver","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"excludeFromFee","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"excludeFromReward","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"includeInFee","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"includeInReward","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"addedValue","type":"uint256"}],"name":"increaseAllowance","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isExcludedFromFee","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isExcludedFromReward","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"tAmount","type":"uint256"},{"internalType":"bool","name":"deductTransferFee","type":"bool"}],"name":"reflectionFromToken","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"renounceOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"charityFee","type":"uint256"}],"name":"setCharityFeePercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"liquidityFee","type":"uint256"}],"name":"setLiquidityFeePercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"maxTxPercent","type":"uint256"}],"name":"setMaxTxPercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"_enabled","type":"bool"}],"name":"setSwapAndLiquifyEnabled","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"taxFee","type":"uint256"}],"name":"setTaxFeePercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"swapAndLiquifyEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"rAmount","type":"uint256"}],"name":"tokenFromReflection","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalFees","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"recipient","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"address","name":"recipient","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"uniswapV2Pair","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"uniswapV2Router","outputs":[{"internalType":"contract IUniswapV2Router02","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"stateMutability":"payable","type":"receive"}]')},3455:e=>{e.exports=JSON.parse('[{"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"uint256","name":"amountOutMin","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"swapExactTokensForTokens","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"uint256","name":"amountOutMin","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"swapExactTokensForETH","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountOutMin","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"swapExactETHForTokens","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"payable","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"}],"name":"getAmountsOut","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountOut","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"}],"name":"getAmountsIn","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"view","type":"function"}]')}},t={};!function n(r){var s=t[r];if(void 0!==s)return s.exports;var a=t[r]={exports:{}};return e[r].call(a.exports,a,a.exports,n),a.exports}(6997)})();