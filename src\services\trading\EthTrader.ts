/**
 * Ethereum Trader Implementation
 *
 * This module implements the BlockchainTrader interface for the Ethereum network.
 */

import TelegramBot from 'node-telegram-bot-api';
import { ethers } from 'ethers';
import {
  BaseTrader,
  BlockchainType,
  TokenInfo,
  TradeOptions,
  TradeResult,
  WalletInfo
} from './TradingInterface';
import { logger } from '../logger/LoggerService';
import { generateETHWallet } from '../wallet.service';
import { checkEVMHoneypot } from '../checkEVMHoneyPot.service';
import { getTokenInfoFromDexscreener } from '../dexscreener.service';
import tokenAbi from '../../abis/ERC20ABI.json';
import routerAbi from '../../abis/UniswapRouterABI.json';
import axios from 'axios';

/**
 * Ethereum-specific configuration
 */
export const ETH_CONFIG = {
  rpcUrl: process.env.ETH_PROVIDER_URL || 'https://mainnet.infura.io/v3/********************************',
  routerAddress: process.env.ETH_ROUTER_ADDRESS || '******************************************', // Uniswap V2 Router
  wethAddress: process.env.ETH_WETH_ADDRESS || '******************************************', // Wrapped ETH
  explorerUrl: 'https://etherscan.io',
  gasLimit: 350000,
  defaultSlippage: 10, // 10%
  apiUrl: process.env.ETHNODE_API_URL || 'https://api.0x.org/swap/v1/quote',
};

/**
 * Ethereum Trader implementation
 */
export class EthTrader extends BaseTrader {
  private provider: ethers.providers.JsonRpcProvider;
  private apiKey: string;

  constructor(bot: TelegramBot) {
    super(bot);
    this.provider = new ethers.providers.JsonRpcProvider(ETH_CONFIG.rpcUrl);
    this.apiKey = process.env.MASTER_KEY || '';
    logger.info('ETH Trader initialized', { rpcUrl: ETH_CONFIG.rpcUrl });
  }

  /**
   * Get the blockchain type
   */
  getBlockchainType(): BlockchainType {
    return BlockchainType.ETH;
  }

  /**
   * Buy tokens on Ethereum
   * @param tokenAddress The address of the token to buy
   * @param amount The amount of ETH to spend
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async buyTokens(
    tokenAddress: string,
    amount: string,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Buying tokens on Ethereum', {
      tokenAddress,
      amount,
      walletAddress,
      options
    });

    try {
      // Get the wallet's private key from the database
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a wallet instance
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // Calculate the amount in wei
      const amountInWei = ethers.utils.parseEther(amount);

      // Set up the 0x API parameters
      const sellToken = '******************************************'; // ETH
      const buyToken = tokenAddress;
      const slippagePercentage = (options?.slippage || ETH_CONFIG.defaultSlippage) / 100;
      const feeRecipient = process.env.EVM_ADMIN_ADDRESS || '';
      const buyTokenPercentageFee = 0.08; // 0.8% fee

      // Build the query parameters
      const queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${amountInWei.toString()}&slippagePercentage=${slippagePercentage}&takerAddress=${walletAddress}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;

      // Get the swap quote from 0x API
      const url = `${ETH_CONFIG.apiUrl}?${queryParams}`;
      const response = await axios.get(url, {
        headers: { '0x-api-key': this.apiKey }
      });

      const quote = response.data;

      // Build the transaction
      const buyTx = {
        to: quote.to,
        data: quote.data,
        value: ethers.BigNumber.from(quote.value),
        gasLimit: options?.gasLimit ? ethers.BigNumber.from(options.gasLimit) : ethers.BigNumber.from(ETH_CONFIG.gasLimit),
        gasPrice: options?.gasPrice
          ? ethers.utils.parseUnits(options.gasPrice, 'gwei')
          : await this.provider.getGasPrice(),
      };

      // Execute the transaction
      const txResponse = await wallet.sendTransaction(buyTx);

      // Wait for the transaction to be mined
      const receipt = await txResponse.wait();

      logger.info('ETH buy transaction successful', {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        tokenAddress
      });

      // Log performance
      logger.logPerformance('ETH buyTokens', startTime, {
        tokenAddress,
        transactionHash: receipt.transactionHash
      });

      return {
        success: true,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toNumber(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        amountIn: amount,
        amountOut: 'Unknown', // 0x API doesn't provide this directly
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('ETH buy transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Sell tokens on Ethereum
   * @param tokenAddress The address of the token to sell
   * @param percentage The percentage of tokens to sell (0-100)
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async sellTokens(
    tokenAddress: string,
    percentage: number,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Selling tokens on Ethereum', {
      tokenAddress,
      percentage,
      walletAddress,
      options
    });

    try {
      // Validate percentage
      if (percentage <= 0 || percentage > 100) {
        throw new Error('Percentage must be between 1 and 100');
      }

      // Get the wallet's private key
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a wallet instance
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        wallet
      );

      // Get token balance and decimals
      const balance = await tokenContract.balanceOf(walletAddress);
      const decimals = await tokenContract.decimals();

      // Calculate amount to sell based on percentage
      const amountToSell = balance.mul(percentage).div(100);

      if (amountToSell.isZero()) {
        throw new Error('No tokens to sell');
      }

      // Check if we need to approve the 0x Exchange Proxy
      const spenderAddress = process.env.EVM_SPENDER_ADDRESS || '';
      const allowance = await tokenContract.allowance(walletAddress, spenderAddress);

      if (allowance.lt(amountToSell)) {
        // Approve the spender to spend tokens
        const approveTx = await tokenContract.approve(
          spenderAddress,
          ethers.constants.MaxUint256
        );
        await approveTx.wait();
        logger.info('Approved spender to spend tokens', {
          tokenAddress,
          walletAddress,
          transactionHash: approveTx.hash
        });
      }

      // Set up the 0x API parameters
      const sellToken = tokenAddress;
      const buyToken = '******************************************'; // ETH
      const slippagePercentage = (options?.slippage || ETH_CONFIG.defaultSlippage) / 100;
      const feeRecipient = process.env.EVM_ADMIN_ADDRESS || '';
      const buyTokenPercentageFee = 0.08; // 0.8% fee

      // Build the query parameters
      const queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${amountToSell.toString()}&slippagePercentage=${slippagePercentage}&takerAddress=${walletAddress}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;

      // Get the swap quote from 0x API
      const url = `${ETH_CONFIG.apiUrl}?${queryParams}`;
      const response = await axios.get(url, {
        headers: { '0x-api-key': this.apiKey }
      });

      const quote = response.data;

      // Build the transaction
      const sellTx = {
        to: quote.to,
        data: quote.data,
        value: ethers.BigNumber.from(quote.value || 0),
        gasLimit: options?.gasLimit ? ethers.BigNumber.from(options.gasLimit) : ethers.BigNumber.from(ETH_CONFIG.gasLimit),
        gasPrice: options?.gasPrice
          ? ethers.utils.parseUnits(options.gasPrice, 'gwei')
          : await this.provider.getGasPrice(),
      };

      // Execute the transaction
      const txResponse = await wallet.sendTransaction(sellTx);

      // Wait for the transaction to be mined
      const receipt = await txResponse.wait();

      logger.info('ETH sell transaction successful', {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        tokenAddress,
        percentage
      });

      // Log performance
      logger.logPerformance('ETH sellTokens', startTime, {
        tokenAddress,
        transactionHash: receipt.transactionHash,
        percentage
      });

      return {
        success: true,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toNumber(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        amountIn: ethers.utils.formatUnits(amountToSell, decimals),
        amountOut: 'Unknown', // 0x API doesn't provide this directly
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('ETH sell transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress,
        percentage
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get token information
   * @param tokenAddress The address of the token
   */
  async getTokenInfo(tokenAddress: string): Promise<TokenInfo> {
    try {
      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        this.provider
      );

      // Get basic token information
      const [name, symbol, decimals, totalSupply] = await Promise.all([
        tokenContract.name(),
        tokenContract.symbol(),
        tokenContract.decimals(),
        tokenContract.totalSupply()
      ]);

      // Get additional information from Dexscreener
      const dexInfo = await getTokenInfoFromDexscreener(tokenAddress, BlockchainType.ETH);

      // Check if token is a honeypot
      const honeypotInfo = await this.checkHoneypot(tokenAddress);

      return {
        name,
        symbol,
        decimals,
        totalSupply: ethers.utils.formatUnits(totalSupply, decimals),
        address: tokenAddress,
        blockchain: BlockchainType.ETH,
        price: typeof dexInfo?.priceUsd === 'string' ? parseFloat(dexInfo.priceUsd) : (dexInfo?.priceUsd || 0),
        marketCap: typeof dexInfo?.marketCap === 'string' ? parseFloat(dexInfo.marketCap) : (dexInfo?.marketCap || 0),
        isHoneypot: honeypotInfo?.isHoneypot || false,
        honeypotInfo
      };
    } catch (error: any) {
      logger.error('Failed to get ETH token info', {
        error: error.message,
        tokenAddress
      });

      // Return minimal information
      return {
        name: 'Unknown',
        symbol: 'UNKNOWN',
        decimals: 18,
        address: tokenAddress,
        blockchain: BlockchainType.ETH
      };
    }
  }

  /**
   * Get wallet balance
   * @param walletAddress The wallet address
   */
  async getWalletBalance(walletAddress: string): Promise<string> {
    try {
      const balance = await this.provider.getBalance(walletAddress);
      return ethers.utils.formatEther(balance);
    } catch (error: any) {
      logger.error('Failed to get ETH wallet balance', {
        error: error.message,
        walletAddress
      });
      return '0';
    }
  }

  /**
   * Get token balance for a wallet
   * @param tokenAddress The address of the token
   * @param walletAddress The wallet address
   */
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string> {
    try {
      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        this.provider
      );

      // Get token balance and decimals
      const balance = await tokenContract.balanceOf(walletAddress);
      const decimals = await tokenContract.decimals();

      return ethers.utils.formatUnits(balance, decimals);
    } catch (error: any) {
      logger.error('Failed to get ETH token balance', {
        error: error.message,
        tokenAddress,
        walletAddress
      });
      return '0';
    }
  }

  /**
   * Check if a token is a honeypot
   * @param tokenAddress The address of the token
   */
  async checkHoneypot(tokenAddress: string): Promise<any> {
    try {
      return await checkEVMHoneypot(tokenAddress, BlockchainType.ETH);
    } catch (error: any) {
      logger.error('Failed to check if ETH token is honeypot', {
        error: error.message,
        tokenAddress
      });
      return { isHoneypot: false, message: 'Failed to check' };
    }
  }

  /**
   * Generate a new ETH wallet
   */
  async generateWallet(): Promise<WalletInfo> {
    try {
      const wallet = generateETHWallet();
      const balance = await this.getWalletBalance(wallet.address);

      return {
        ...wallet,
        balance,
        blockchain: BlockchainType.ETH
      };
    } catch (error: any) {
      logger.error('Failed to generate ETH wallet', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get a wallet's private key (this would be implemented securely in production)
   * @param walletAddress The wallet address
   */
  private async getPrivateKey(walletAddress: string): Promise<string | null> {
    // In a real implementation, you would retrieve this securely from a database
    // For now, we'll return null and assume it's handled elsewhere
    return null;
  }
}
