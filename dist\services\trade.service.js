"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeService = void 0;
const trade_model_1 = __importDefault(require("../models/trade.model"));
const user_models_1 = __importDefault(require("../models/user.models"));
exports.TradeService = {
    createTrade(client, tradeData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Ensure all required fields are present
                const { userId = '', walletId = '', contractAddress = '', contractName = '', buyAmount = 0, sellAmount = 0, takeProfit = 0, stopLoss = 0 } = tradeData;
                const trade = new trade_model_1.default({
                    user_id: userId,
                    wallet_id: walletId,
                    contract_address: contractAddress,
                    contract_name: contractName,
                    buy_amount: buyAmount,
                    sell_amount: sellAmount,
                    take_profit: takeProfit,
                    stop_loss: stopLoss,
                });
                const savedTrade = yield trade.save();
                yield user_models_1.default.findByIdAndUpdate(userId, { $push: { trades: savedTrade._id } });
                return savedTrade;
            }
            catch (error) {
                console.error('-createTrade-', error);
                throw error;
            }
        });
    },
    updateTrade(tradeId, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield trade_model_1.default.findByIdAndUpdate(tradeId, updateData, { new: true });
            }
            catch (error) {
                console.error('-updateTrade-', error);
                throw error;
            }
        });
    },
    getTrade(tradeId) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                return yield trade_model_1.default.findById(tradeId);
            }
            catch (error) {
                console.error('-getTrade-', error);
                throw error;
            }
        });
    },
};
