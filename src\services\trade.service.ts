import { MongoClient } from 'mongodb';
import Trade from '../models/trade.model';
import User from '../models/user.models';

export const TradeService = {
  async createTrade(client: MongoClient, tradeData: { userId?: string, walletId?: string, contractAddress?: string, contractName?: string, buyAmount?: number, sellAmount?: number, takeProfit?: number, stopLoss?: number }) {
    try {
      // Ensure all required fields are present
      const {
        userId = '',
        walletId = '',
        contractAddress = '',
        contractName = '',
        buyAmount = 0,
        sellAmount = 0,
        takeProfit = 0,
        stopLoss = 0
      } = tradeData;

      const trade = new Trade({
        user_id: userId,
        wallet_id: walletId,
        contract_address: contractAddress,
        contract_name: contractName,
        buy_amount: buyAmount,
        sell_amount: sellAmount,
        take_profit: takeProfit,
        stop_loss: stopLoss,
      });

      const savedTrade = await trade.save();
      await User.findByIdAndUpdate(userId, { $push: { trades: savedTrade._id } });

      return savedTrade;
    } catch (error) {
      console.error('-createTrade-', error);
      throw error;
    }
  },

  async updateTrade(tradeId: string, updateData: object) {
    try {
      return await Trade.findByIdAndUpdate(tradeId, updateData, { new: true });
    } catch (error) {
      console.error('-updateTrade-', error);
      throw error;
    }
  },

  async getTrade(tradeId: string) {
    try {
      return await Trade.findById(tradeId);
    } catch (error) {
      console.error('-getTrade-', error);
      throw error;
    }
  },
};
