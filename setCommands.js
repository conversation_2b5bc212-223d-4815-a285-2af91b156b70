const TelegramBot = require('node-telegram-bot-api');

const token = '**********************************************';
const bot = new TelegramBot(token, { polling: true });

const commands = [
  { command: 'start', description: 'starts bot' },
  { command: 'sniper', description: 'fire sniper' },
  { command: 'privatekeys', description: 'display private key' },
  { command: 'wallets', description: 'show wallets' },
];

const options = {
  language_code: 'en', 
  scope: { type: 'default' }, 
};

bot.setMyCommands(commands, options)
  .then(() => {
    console.log('Commands set successfully');
  })
  .catch((err) => {
    console.error('Error setting commands:', err);
  });
