/**
 * Global State Module
 *
 * This module provides a global state for the application with blockchain-specific contexts.
 */

import { BlockchainType } from './services/trading/TradingInterface';

/**
 * Global state interface
 */
interface GlobalState {
  // Store contract addresses by blockchain
  currentContractAddresses: {
    [key in BlockchainType]?: string | null;
  };
  // Other global state properties can be added here
}

/**
 * Initialize global state
 */
const globalState: GlobalState = {
  currentContractAddresses: {
    [BlockchainType.BSC]: null,
    [BlockchainType.ETH]: null,
    [BlockchainType.SOL]: null,
    [BlockchainType.BASE]: null
  }
};

/**
 * Get the current contract address for a specific blockchain
 * @param blockchain The blockchain type
 * @returns The current contract address or null
 */
export const getCurrentContractAddress = (blockchain: BlockchainType): string | null => {
  return globalState.currentContractAddresses[blockchain] || null;
};

/**
 * Set the current contract address for a specific blockchain
 * @param blockchain The blockchain type
 * @param address The contract address or null
 */
export const setCurrentContractAddress = (blockchain: BlockchainType, address: string | null): void => {
  globalState.currentContractAddresses[blockchain] = address;
};

/**
 * Get all current contract addresses
 * @returns Object with all current contract addresses by blockchain
 */
export const getAllCurrentContractAddresses = (): { [key in BlockchainType]?: string | null } => {
  return { ...globalState.currentContractAddresses };
};

/**
 * Clear all contract addresses
 */
export const clearAllContractAddresses = (): void => {
  Object.keys(globalState.currentContractAddresses).forEach(key => {
    globalState.currentContractAddresses[key as BlockchainType] = null;
  });
};

export default globalState;
