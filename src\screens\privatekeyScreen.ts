import TelegramBot from 'node-telegram-bot-api';
import User from '../models/user.models';

export const privatekeyHandler = async (bot: TelegramBot, msg: TelegramBot.Message) => {
  const { username, id: chat_id, first_name, last_name } = msg.chat;

  try {
    // Check if the user already exists in the database
    const user = await User.findOne({ chat_id });

    if (user) {
      const caption =
        `👋 View your Keys (Tap to copy)!\n\n` +
        `SOL: <code><tg-spoiler> ${user.sol_wallet.private_key}</tg-spoiler></code>\n` +
        `BSC: <code><tg-spoiler>${user.bsc_wallet.private_key}</tg-spoiler></code>\n` +
        `BASE: <code><tg-spoiler>${user.base_wallet.private_key}</tg-spoiler></code>\n` +
        `ETH: <code><tg-spoiler> ${user.eth_wallet.private_key}</tg-spoiler></code>\n\n`;

      await bot.sendMessage(chat_id, caption, {
        parse_mode: 'HTML',
        disable_web_page_preview: true,
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: "* Dismiss message",
                callback_data: JSON.stringify({
                  command: "dismiss_message",
                }),
              },
            ],
          ],
        },
      });
    } else {
      await bot.sendMessage(chat_id, 'User not found. Please register first.');
    }
  } catch (error) {
    console.error('Error handling wallet selection:', error);
    await bot.sendMessage(chat_id, 'An error occurred while processing your request. Please try again later.');
  }
};
