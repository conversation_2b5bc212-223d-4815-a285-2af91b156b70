"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buyBaseTokens = buyBaseTokens;
const ethers_1 = require("ethers");
const user_models_1 = __importDefault(require("../../models/user.models")); // Adjust the path as needed
const trade_model_1 = __importDefault(require("../../models/trade.model")); // Adjust the path as neededimport axios from 'axios';
const axios_1 = __importDefault(require("axios"));
const adminAddress = process.env.EVM_ADMIN_ADDRESS || '';
const providerUrl = process.env.BASE_PROVIDER_URL;
if (!adminAddress) {
    throw new Error('ADMIN_ADDRESS environment variable is not set');
}
if (!providerUrl) {
    throw new Error('base_PROVIDER_URL environment variable is not set');
}
function getTokenDecimals(tokenAddress, provider) {
    return __awaiter(this, void 0, void 0, function* () {
        const tokenAbi = ["function decimals() view returns (uint8)"];
        const tokenContract = new ethers_1.Contract(tokenAddress, tokenAbi, provider);
        const decimals = yield tokenContract.decimals();
        return decimals;
    });
}
function getSwapQuote(url) {
    return __awaiter(this, void 0, void 0, function* () {
        const response = yield axios_1.default.get(url);
        return response.data;
    });
}
function buyBaseTokens(chatId, contractAddress, config) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log("Starting token purchase on baseSwap");
        try {
            // Fetch user settings from the database
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user) {
                throw new Error('User not found');
            }
            const currentBlockchain = user.currentBlockchain;
            if (currentBlockchain !== 'base') {
                throw new Error(`Current blockchain is ${currentBlockchain}, not base.`);
            }
            const { private_key, address } = user.base_wallet;
            const baseCurrentTokenName = user.baseCurrentTokenName;
            if (!contractAddress) {
                throw new Error('Contract address is not set');
            }
            // Define buyToken based on the provided contract address
            const buyToken = contractAddress;
            // Initialize provider and wallet
            const provider = new ethers_1.providers.JsonRpcProvider(providerUrl);
            const wallet = new ethers_1.Wallet(private_key, provider);
            // Get token decimals
            const decimals = yield getTokenDecimals(buyToken, provider);
            console.log(`Token Decimals: ${decimals}`);
            // Convert spend_base to the smallest unit
            const spendBaseWei = ethers_1.ethers.utils.parseUnits(config.spend_base.toString(), decimals);
            console.log(`Spend Amount (in smallest unit): ${spendBaseWei.toString()}`);
            // Define sellToken as the native token (BNB for base)
            const sellToken = '******************************************';
            const feeRecipient = adminAddress;
            const buyTokenPercentageFee = 0.8;
            // Get slippage
            const slippagePercentage = config.slippage_base;
            // Build the query parameters
            let queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${spendBaseWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;
            // Define the endpoint URL with query parameters
            const url = `https://base.api.0x.org/swap/v1/quote?${queryParams}`;
            // Get the swap quote
            const quote = yield getSwapQuote(url);
            // Prepare the transaction to buy tokens
            const buyTx = {
                to: quote.to,
                data: quote.data,
                value: ethers_1.ethers.BigNumber.from(quote.value),
                gasLimit: ethers_1.ethers.BigNumber.from(500000), // Adjusted gas limit to a higher value
                gasPrice: ethers_1.ethers.utils.parseUnits('0.05', 'gwei'), // Adjusted gas price to a higher value
            };
            console.log('Signing and sending the buy transaction...');
            const buyTxResponse = yield wallet.sendTransaction(buyTx);
            console.log(`Buy Transaction Hash: ${buyTxResponse.hash}`);
            // Wait for the buy transaction to be mined
            const receipt = yield buyTxResponse.wait();
            console.log(`Buy Transaction Mined! Block Number: ${receipt.blockNumber}`);
            // Check if there is an existing trade for this contract address
            const existingTrade = yield trade_model_1.default.findOne({
                userId: user._id,
                contractAddress: buyToken,
                isSold: false,
            }).exec();
            if (existingTrade) {
                existingTrade.buyAmount += parseFloat(config.spend_base.toString());
                yield existingTrade.save();
                console.log('Updated existing trade with new buy amount');
            }
            else {
                const newTrade = new trade_model_1.default({
                    userId: user._id,
                    walletId: user.base_wallet.address,
                    contractAddress: buyToken,
                    contractName: baseCurrentTokenName,
                    buyAmount: parseFloat(config.spend_base.toString()),
                    sellAmount: 0,
                    takeProfit: 0,
                    stopLoss: 0,
                    isSold: false,
                    blockchain: 'base'
                });
                yield newTrade.save();
                console.log('Created new trade entry');
            }
            // Update the user's document with the new trade entry
            yield user_models_1.default.updateOne({ _id: user._id }, { $push: { [`trades.${currentBlockchain}`]: existingTrade ? existingTrade._id : user._id } });
            console.log('User trades updated successfully');
            return receipt; // Return the receipt object after the transaction is successful
        }
        catch (error) {
            console.error('Error purchasing tokens:', error);
            throw error; // Re-throw the error to handle it in the bot action
        }
    });
}
