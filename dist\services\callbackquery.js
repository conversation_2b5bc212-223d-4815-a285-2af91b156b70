"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleCallbackQuery = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const web3_js_1 = require("@solana/web3.js");
const user_models_1 = __importDefault(require("../models/user.models"));
const trade_model_1 = __importDefault(require("../models/trade.model")); // Adjust the path as needed
const bsc_dashboard_1 = require("../screens/dashboards/bsc.dashboard");
const bscscan_service_1 = require("./bscscan.service");
const bscscan_service_2 = require("../services/bscscan.service");
const bscboard_1 = require("../screens/sniperboards/bscboard");
const bsctrade_1 = require("../screens/trade/bsctrade");
const pancake_trader_1 = require("../executor/pancake.trader");
// import { handleBscSellAll, sellBscTokenPercentage  } from './bscscan.service';
const base_dashboard_1 = require("../screens/dashboards/base.dashboard");
const basescan_service_1 = require("./basescan.service");
const baseuniswap_trader_1 = require("../executor/baseuniswap.trader");
const basetrade_1 = require("../screens/trade/basetrade");
const solscan_service_1 = require("./solscan.service");
const sol_dashboard_1 = require("../screens/dashboards/sol.dashboard");
const solbuy_trader_1 = require("../executor/solbuy.trader");
const ethscan_service_1 = require("./ethscan.service");
const eth_dashboard_1 = require("../screens/dashboards/eth.dashboard");
const ethuniswap_trader_1 = require("../executor/ethuniswap.trader");
const baseboard_1 = require("../screens/sniperboards/baseboard");
const globalState_1 = require("../globalState");
const ethtrade_1 = require("../screens/trade/ethtrade");
const soltrade_1 = require("../screens/trade/soltrade");
const config_1 = require("../screens/config");
const previousValuesPath = path_1.default.resolve(__dirname, '../previousValues.json');
// Helper function to read previous values from JSON
function getPreviousValue(chatId, blockchain) {
    var _a;
    const data = JSON.parse(fs_1.default.readFileSync(previousValuesPath, 'utf-8'));
    for (const [address, value] of Object.entries(data)) {
        const blockchainData = value;
        if (((_a = blockchainData[blockchain]) === null || _a === void 0 ? void 0 : _a.chat_id) === chatId) {
            return {
                address,
                balance: blockchainData[blockchain].balance,
            };
        }
    }
    return null;
}
const connection = new web3_js_1.Connection(process.env.SOL_PROVIDER_URL);
let userSessions = {};
let tradesState = [];
let currentIndex = 0;
const promptMessageIdStore = {};
const handleCallbackQuery = (bot, query) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    const chatId = (_a = query.message) === null || _a === void 0 ? void 0 : _a.chat.id;
    const messageId = (_b = query.message) === null || _b === void 0 ? void 0 : _b.message_id;
    if (!chatId || !messageId)
        return;
    const data = JSON.parse(query.data || '{}');
    switch (data.command) {
        case 'dismiss_message':
            yield handleDismissMessage(bot, chatId, messageId);
            break;
        case 'show_config':
            yield (0, config_1.showConfigKeyboard)(bot, chatId);
            break;
        case 'view_bsc':
        case 'view_base':
        case 'view_sol':
        case 'view_eth':
            yield handleBlockchainView(bot, chatId, data.command);
            break;
        case 'generate_new_bsc_wallet':
            yield (0, bscscan_service_2.handleGenerateNewBscWallet)(bot, chatId);
            break;
        case 'scan_contract_bsc':
            const userbscAddress = yield (0, bscscan_service_2.askForContractAddress)(bot, chatId, messageId);
            if (userbscAddress) {
                yield (0, bscscan_service_1.handleBSCScanContract)(bot, chatId, userbscAddress);
            }
            else {
                yield bot.sendMessage(chatId, 'Invalid contract address.');
            }
            break;
        case 'scan_snipe_bsc':
            try {
                const bscAddressToSnipe = yield (0, bscscan_service_2.askForBSCToken)(bot, chatId, messageId);
                yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'bsc' } });
                if (bscAddressToSnipe) {
                    yield (0, bscboard_1.bscSniperScreen)(bot, chatId, bscAddressToSnipe);
                }
                else {
                    yield bot.sendMessage(chatId, 'Invalid contract address.');
                }
            }
            catch (error) {
                console.error('Error during BSC token snipe:', error);
                yield bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
            }
            break;
        case 'snipetrade_dashboard_bsc':
            const contractAddress = (0, globalState_1.getCurrentContractAddress)() || '';
            yield (0, bsctrade_1.tradeBscScreen)(bot, chatId, contractAddress);
            break;
        case 'sell_bsc':
            yield (0, bscscan_service_2.updateBSCMessageWithSellOptions)(bot, chatId, messageId);
            break;
        case 'active_trades_bsc':
            yield (0, bscscan_service_2.fetchAndDisplayActiveBscTrades)(bot, chatId, 'bsc');
            break;
        case 'previous_bsc':
            if (currentIndex > 0) {
                currentIndex--;
                const tradesState = yield trade_model_1.default.find({ isSold: false, blockchain: 'bsc' }).exec();
                yield (0, bscscan_service_2.displayBscCurrentTrade)(bot, chatId, tradesState, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
            }
            break;
        case 'next_bsc':
            const tradesState = yield trade_model_1.default.find({ isSold: false, blockchain: 'bsc' }).exec();
            if (currentIndex < tradesState.length - 1) {
                currentIndex++;
                yield (0, bscscan_service_2.displayBscCurrentTrade)(bot, chatId, tradesState, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
            }
            break;
        case 'refresh_bsc':
            yield (0, bscscan_service_2.handleRefresh)(bot, chatId);
            break;
        case 'chart_bsc':
            yield (0, bscscan_service_2.handleChart)(bot, chatId);
            break;
        case 'spend_bsc':
            try {
                const userBuy = yield (0, bscscan_service_2.handleSpendBSC)(bot, chatId);
                if (userBuy) {
                    const receipt = yield (0, pancake_trader_1.buyBscTokens)(chatId, userBuy);
                    if (receipt && receipt.transactionHash && receipt.blockNumber) {
                        const { transactionHash, blockNumber } = receipt;
                        yield bot.sendMessage(chatId, `✅ Tokens successfully purchased:\n\n` +
                            `🔗 Transaction Hash: ${transactionHash}\n` +
                            `🧱 Block Number: ${blockNumber}\n\n` +
                            `👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${transactionHash}\nTo see your transaction`);
                    }
                    else {
                        yield bot.sendMessage(chatId, `Error: Unable to get transaction details.`);
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'Invalid contract address.');
                }
            }
            catch (error) {
                console.error('Error purchasing tokens:', error);
                if (error instanceof Error) {
                    if (error.message.includes('insufficient funds')) {
                        yield bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again.`);
                    }
                    else if (error.message.includes('SERVER_ERROR') || error.message.includes('ECONNRESET')) {
                        yield bot.sendMessage(chatId, `Error purchasing tokens: There was a problem processing your request. Please try again later.`);
                    }
                    else if (error.message.includes('body')) {
                        try {
                            const errorBody = JSON.parse(error.message);
                            if (errorBody.error && errorBody.error.message.includes('insufficient funds')) {
                                yield bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again.`);
                            }
                            else {
                                yield bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.`);
                            }
                        }
                        catch (parseError) {
                            yield bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.`);
                        }
                    }
                    else {
                        yield bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.`);
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'An unknown error occurred while purchasing tokens. Please try again later.');
                }
            }
            break;
        case 'sell_all_bsc':
            yield (0, bscscan_service_1.handleBscSellAll)(bot, chatId);
            break;
        case 'sell_25_bsc':
            yield (0, bscscan_service_1.sellBscTokenPercentage)(bot, chatId, 25);
            break;
        case 'sell_50_bsc':
            yield (0, bscscan_service_1.sellBscTokenPercentage)(bot, chatId, 50);
            break;
        case 'sell_75_bsc':
            yield (0, bscscan_service_1.sellBscTokenPercentage)(bot, chatId, 75);
            break;
        case 'refresh_wallet_bsc':
            try {
                const user = yield user_models_1.default.findOne({ chat_id: chatId });
                if (user) {
                    const previousValue = getPreviousValue(chatId, 'bsc');
                    // console.log(previousValue)
                    if (previousValue) {
                        const { address } = previousValue;
                        // Fetch the stored message ID, content, and markup
                        const storedMessageId = user.bsc_dashboard_message_id;
                        const storedContent = user.bsc_dashboard_content;
                        const storedMarkup = user.bsc_dashboard_markup;
                        const balance = Number(previousValue.balance);
                        const formattedBalance = balance.toFixed(6);
                        // Construct new content and markup
                        const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your BSC Dashboard!
                          \n📍 **Address:** \`${address}\`
                          \n🔗 **Blockchain:** BSC
                          \n💰 **Balance:** \`${formattedBalance} BSC\`
                         `;
                        const newMarkup = {
                            inline_keyboard: [
                                [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_bsc' }) }],
                                [
                                    { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_bsc' }) },
                                    { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_bsc' }) }
                                ],
                                [
                                    { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_bsc' }) },
                                    { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_bsc_wallet' }) }
                                ],
                                [
                                    {
                                        text: "* Dismiss message",
                                        callback_data: JSON.stringify({
                                            command: "dismiss_message",
                                        }),
                                    },
                                ]
                            ]
                        };
                        // Compare current content and markup with new content and markup
                        if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                            yield bot.editMessageText(newContent, {
                                chat_id: chatId,
                                message_id: storedMessageId,
                                parse_mode: 'Markdown',
                                reply_markup: newMarkup,
                            });
                            // Update stored content and markup
                            yield user_models_1.default.updateOne({ chat_id: chatId }, {
                                $set: {
                                    'base_dashboard_content': newContent,
                                    'base_dashboard_markup': newMarkup
                                }
                            });
                        }
                    }
                    else {
                        yield bot.sendMessage(chatId, 'No wallet address found.');
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'User not found.');
                }
            }
            catch (error) {
                console.error('Failed to refresh wallet base:', error);
                yield bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
            }
            break;
        // HANDLE BASE CALLBACKS
        case 'generate_new_base_wallet':
            yield (0, basescan_service_1.handleGenerateNewBaseWallet)(bot, chatId);
            break;
        case 'snipetrade_dashboard_base':
            yield (0, basetrade_1.tradeBaseScreen)(bot, chatId, (0, globalState_1.getCurrentContractAddress)() || '');
            break;
        case 'scan_contract_base':
            const userAddress = yield (0, basescan_service_1.askForBaseContractAddress)(bot, chatId, messageId);
            if (userAddress) {
                yield (0, basescan_service_1.handleBASEScanContract)(bot, chatId, userAddress);
            }
            else {
                yield bot.sendMessage(chatId, 'Invalid contract address.');
            }
            break;
        case 'scan_snipe_base':
            try {
                const baseAddressToSnipe = yield (0, basescan_service_1.askForBaseToken)(bot, chatId, messageId);
                yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'base' } });
                if (baseAddressToSnipe) {
                    yield (0, baseboard_1.baseSniperScreen)(bot, chatId, baseAddressToSnipe);
                }
                else {
                    yield bot.sendMessage(chatId, 'Invalid contract address.');
                }
            }
            catch (error) {
                console.error('Error during Base token snipe:', error);
                yield bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
            }
            break;
        case 'sell_base':
            yield (0, basescan_service_1.updatebaseMessageWithSellOptions)(bot, chatId, messageId);
            break;
        // case 'trade_base':
        //   await updateBaseMessageWithTradeOptions(bot, chatId, messageId);
        //   break;
        case 'back_base':
            yield (0, basescan_service_1.mainbaseMenu)(bot, chatId, messageId);
            break;
        case 'active_trades_base':
            yield (0, basescan_service_1.fetchAndDisplayActiveBaseTrades)(bot, chatId, 'base');
            break;
        case 'previous_base':
            if (currentIndex > 0) {
                currentIndex--;
                const tradesStateBase = yield trade_model_1.default.find({ isSold: false, blockchain: 'base' }).exec();
                yield (0, basescan_service_1.displayCurrentBaseTrade)(bot, chatId, tradesStateBase, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
            }
            break;
        case 'next_base':
            const tradesStateBase = yield trade_model_1.default.find({ isSold: false, blockchain: 'base' }).exec();
            if (currentIndex < tradesStateBase.length - 1) {
                currentIndex++;
                yield (0, basescan_service_1.displayCurrentBaseTrade)(bot, chatId, tradesStateBase, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
            }
            break;
        case 'refresh_base':
            yield (0, basescan_service_1.handleBaseRefresh)(bot, chatId);
            break;
        case 'chart_base':
            yield (0, basescan_service_1.handleBaseChart)(bot, chatId);
            break;
        case 'spend_base':
            try {
                const userBuy = yield (0, basescan_service_1.handleSpendBase)(bot, chatId);
                if (userBuy) {
                    const receipt = yield (0, baseuniswap_trader_1.buyBaseTokens)(chatId, userBuy);
                    if (receipt && receipt.transactionHash && receipt.blockNumber) {
                        const { transactionHash, blockNumber } = receipt;
                        yield bot.sendMessage(chatId, `✅ Tokens successfully purchased:\n\n` +
                            `🔗 Transaction Hash: ${transactionHash}\n` +
                            `🧱 Block Number: ${blockNumber}\n\n` +
                            `👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${transactionHash}\nTo see your transaction`);
                    }
                    else {
                        yield bot.sendMessage(chatId, `Error: Unable to get transaction details.`);
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'Invalid contract address.');
                }
            }
            catch (error) {
                console.error('Error purchasing tokens:', error);
                if (error instanceof Error) {
                    if (error.message.includes('Insufficient BNB balance')) {
                        yield bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost.`);
                    }
                    else if (error.message.includes('SERVER_ERROR') || error.message.includes('ECONNRESET')) {
                        yield bot.sendMessage(chatId, `Error purchasing tokens: There was a problem processing your request. Please try again later.`);
                    }
                    else {
                        yield bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network.`);
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'An unknown error occurred while purchasing tokens. Please try again later.');
                }
            }
            break;
        case 'sell_all_base':
            yield (0, basescan_service_1.handleBaseSellAll)(bot, chatId);
            break;
        case 'sell_25_base':
            yield (0, basescan_service_1.sellBaseTokenPercentage)(bot, chatId, 25);
            break;
        case 'sell_50_base':
            yield (0, basescan_service_1.sellBaseTokenPercentage)(bot, chatId, 50);
            break;
        case 'sell_75_base':
            yield (0, basescan_service_1.sellBaseTokenPercentage)(bot, chatId, 75);
            break;
        case 'refresh_wallet_base':
            try {
                const user = yield user_models_1.default.findOne({ chat_id: chatId });
                if (user) {
                    const previousValue = getPreviousValue(chatId, 'base');
                    // console.log(previousValue)
                    if (previousValue) {
                        const { address } = previousValue;
                        // Fetch the stored message ID, content, and markup
                        const storedMessageId = user.base_dashboard_message_id;
                        const storedContent = user.base_dashboard_content;
                        const storedMarkup = user.base_dashboard_markup;
                        const balance = Number(previousValue.balance);
                        const formattedBalance = balance.toFixed(6);
                        // Construct new content and markup
                        const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your BASE Dashboard!
                            \n📍 **Address:** \`${address}\`
                            \n🔗 **Blockchain:** BASE
                            \n💰 **Balance:** \`${formattedBalance} Base ETH\`
                           `;
                        const newMarkup = {
                            inline_keyboard: [
                                [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_base' }) }],
                                [
                                    { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_base' }) },
                                    { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_base' }) }
                                ],
                                [
                                    { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_base' }) },
                                    { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_base_wallet' }) }
                                ],
                                [
                                    {
                                        text: "* Dismiss message",
                                        callback_data: JSON.stringify({
                                            command: "dismiss_message",
                                        }),
                                    },
                                ]
                            ]
                        };
                        // Compare current content and markup with new content and markup
                        if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                            yield bot.editMessageText(newContent, {
                                chat_id: chatId,
                                message_id: storedMessageId,
                                parse_mode: 'Markdown',
                                reply_markup: newMarkup,
                            });
                            // Update stored content and markup
                            yield user_models_1.default.updateOne({ chat_id: chatId }, {
                                $set: {
                                    'base_dashboard_content': newContent,
                                    'base_dashboard_markup': newMarkup
                                }
                            });
                        }
                    }
                    else {
                        yield bot.sendMessage(chatId, 'No wallet address found.');
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'User not found.');
                }
            }
            catch (error) {
                console.error('Failed to refresh wallet base:', error);
                yield bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
            }
            break;
        // HANDLE ETHEREUM CALLBACKS
        case 'generate_new_eth_wallet':
            yield (0, ethscan_service_1.handleGenerateNewethWallet)(bot, chatId);
            break;
        case 'snipetrade_dashboard_eth':
            yield (0, ethtrade_1.tradeEthScreen)(bot, chatId, (0, globalState_1.getCurrentContractAddress)() || '');
            break;
        case 'scan_contract_eth':
            const userEthAddress = yield (0, ethscan_service_1.askForEthContractAddress)(bot, chatId, messageId);
            if (userEthAddress) {
                yield (0, ethscan_service_1.handleETHScanContract)(bot, chatId, userEthAddress);
            }
            else {
                yield bot.sendMessage(chatId, 'Invalid contract address.');
            }
            break;
        case 'scan_snipe_eth':
            try {
                const ethAddressToSnipe = yield (0, ethscan_service_1.askForEthToken)(bot, chatId, messageId);
                yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'eth' } });
                if (ethAddressToSnipe) {
                    yield (0, bscboard_1.bscSniperScreen)(bot, chatId, ethAddressToSnipe);
                }
                else {
                    yield bot.sendMessage(chatId, 'Invalid contract address.');
                }
            }
            catch (error) {
                console.error('Error during Eth token snipe:', error);
                yield bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
            }
            break;
        case 'sell_eth':
            yield (0, ethscan_service_1.updateEthMessageWithSellOptions)(bot, chatId, messageId);
            break;
        case 'trade_eth':
            yield (0, ethscan_service_1.updateEthMessageWithTradeOptions)(bot, chatId, messageId);
            break;
        case 'back_eth':
            yield (0, ethscan_service_1.mainEthMenu)(bot, chatId, messageId);
            break;
        case 'active_trades_eth':
            yield (0, ethscan_service_1.fetchAndDisplayActiveEthTrades)(bot, chatId, 'eth');
            break;
        case 'previous_eth':
            if (currentIndex > 0) {
                currentIndex--;
                const tradesStateEth = yield trade_model_1.default.find({ isSold: false, blockchain: 'eth' }).exec();
                yield (0, ethscan_service_1.displayCurrentEthTrade)(bot, chatId, tradesStateEth, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
            }
            break;
        case 'next_eth':
            const tradesStateEth = yield trade_model_1.default.find({ isSold: false, blockchain: 'eth' }).exec();
            if (currentIndex < tradesStateEth.length - 1) {
                currentIndex++;
                yield (0, ethscan_service_1.displayCurrentEthTrade)(bot, chatId, tradesStateEth, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
            }
            break;
        case 'refresh_eth':
            yield (0, ethscan_service_1.handleEthRefresh)(bot, chatId);
            break;
        case 'chart_eth':
            yield (0, ethscan_service_1.handleEthChart)(bot, chatId);
            break;
        case 'spend_eth':
            try {
                const userBuy = yield (0, ethscan_service_1.handleSpendeth)(bot, chatId);
                if (userBuy) {
                    const receipt = yield (0, ethuniswap_trader_1.buyEthTokens)(chatId, userBuy);
                    if (receipt && receipt.transactionHash && receipt.blockNumber) {
                        const { transactionHash, blockNumber } = receipt;
                        yield bot.sendMessage(chatId, `✅ Tokens successfully purchased:\n\n` +
                            `🔗 Transaction Hash: ${transactionHash}\n` +
                            `🧱 Block Number: ${blockNumber}\n\n` +
                            `👁‍🗨 Navigate to:\nhttps://etherscan.com/tx/${transactionHash}\nTo see your transaction`);
                    }
                    else {
                        yield bot.sendMessage(chatId, `Error: Unable to get transaction details.`);
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'Invalid contract address.');
                }
            }
            catch (error) {
                console.error('Error purchasing tokens:', error);
                if (error instanceof Error) {
                    if (error.message.includes('Insufficient Eth balance')) {
                        yield bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost.`);
                    }
                    else if (error.message.includes('SERVER_ERROR') || error.message.includes('ECONNRESET')) {
                        yield bot.sendMessage(chatId, `Error purchasing tokens: There was a problem processing your request. Please try again later.`);
                    }
                    else {
                        yield bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network.`);
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'An unknown error occurred while purchasing tokens. Please try again later.');
                }
            }
            break;
        case 'sell_all_eth':
            yield (0, ethscan_service_1.handleEthSellAll)(bot, chatId);
            break;
        case 'sell_25_eth':
            yield (0, ethscan_service_1.sellethTokenPercentage)(bot, chatId, 25);
            break;
        case 'sell_50_eth':
            yield (0, ethscan_service_1.sellethTokenPercentage)(bot, chatId, 50);
            break;
        case 'sell_75_eth':
            yield (0, ethscan_service_1.sellethTokenPercentage)(bot, chatId, 75);
            break;
        case 'refresh_wallet_eth':
            try {
                const user = yield user_models_1.default.findOne({ chat_id: chatId });
                if (user) {
                    const previousValue = getPreviousValue(chatId, 'eth');
                    // console.log(previousValue)
                    if (previousValue) {
                        const { address } = previousValue;
                        // Fetch the stored message ID, content, and markup
                        const storedMessageId = user.eth_dashboard_message_id;
                        const storedContent = user.eth_dashboard_content;
                        const storedMarkup = user.eth_dashboard_markup;
                        const balance = Number(previousValue.balance);
                        const formattedBalance = balance.toFixed(6);
                        // Construct new content and markup
                        const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your ETH Dashboard!
                          \n📍 **Address:** \`${address}\`
                          \n🔗 **Blockchain:** ETH
                          \n💰 **Balance:** \`${formattedBalance}  ETH\`
                         `;
                        const newMarkup = {
                            inline_keyboard: [
                                [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_eth' }) }],
                                [
                                    { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_eth' }) },
                                    { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_eth' }) }
                                ],
                                [
                                    { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_eth' }) },
                                    { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_eth_wallet' }) }
                                ],
                                [
                                    {
                                        text: "* Dismiss message",
                                        callback_data: JSON.stringify({
                                            command: "dismiss_message",
                                        }),
                                    },
                                ]
                            ]
                        };
                        // Compare current content and markup with new content and markup
                        if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                            yield bot.editMessageText(newContent, {
                                chat_id: chatId,
                                message_id: storedMessageId,
                                parse_mode: 'Markdown',
                                reply_markup: newMarkup,
                            });
                            // Update stored content and markup
                            yield user_models_1.default.updateOne({ chat_id: chatId }, {
                                $set: {
                                    'eth_dashboard_content': newContent,
                                    'eth_dashboard_markup': newMarkup
                                }
                            });
                        }
                    }
                    else {
                        yield bot.sendMessage(chatId, 'No wallet address found.');
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'User not found.');
                }
            }
            catch (error) {
                console.error('Failed to refresh wallet et:', error);
                yield bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
            }
            break;
        // HANDLE SOL CALLBACKS
        case 'generate_new_sol_wallet':
            yield (0, solscan_service_1.handleGenerateNewSolWallet)(bot, chatId);
            break;
        case 'snipetrade_dashboard_sol':
            yield (0, soltrade_1.tradeSolScreen)(bot, chatId, (0, globalState_1.getCurrentContractAddress)() || '');
            break;
        case 'scan_contract_sol':
            const userSolAddress = yield (0, solscan_service_1.askForSolContractAddress)(bot, chatId, messageId);
            if (userSolAddress) {
                yield (0, solscan_service_1.handleSOLScanContract)(bot, chatId, userSolAddress);
            }
            else {
                yield bot.sendMessage(chatId, 'Invalid contract address.');
            }
            break;
        case 'scan_snipe_sol':
            try {
                const solAddressToSnipe = yield (0, solscan_service_1.askForSolToken)(bot, chatId, messageId);
                yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'sol' } });
                if (solAddressToSnipe) {
                    yield (0, bscboard_1.bscSniperScreen)(bot, chatId, solAddressToSnipe);
                }
                else {
                    yield bot.sendMessage(chatId, 'Invalid contract address.');
                }
            }
            catch (error) {
                console.error('Error during Eth token snipe:', error);
                yield bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
            }
            break;
        case 'sell_sol':
            yield (0, solscan_service_1.updateSolMessageWithSellOptions)(bot, chatId, messageId);
            break;
        case 'trade_sol':
            yield (0, solscan_service_1.updateSolMessageWithTradeOptions)(bot, chatId, messageId);
            break;
        case 'back_sol':
            yield (0, solscan_service_1.mainSolMenu)(bot, chatId, messageId);
            break;
        case 'active_trades_sol':
            yield (0, solscan_service_1.fetchAndDisplayActiveSolTrades)(bot, chatId, 'sol');
            break;
        case 'previous_sol':
            if (currentIndex > 0) {
                currentIndex--;
                const tradesStateSol = yield trade_model_1.default.find({ isSold: false, blockchain: 'sol' }).exec();
                yield (0, solscan_service_1.displayCurrentSolTrade)(bot, chatId, tradesStateSol, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
            }
            break;
        case 'next_sol':
            const tradesStateSol = yield trade_model_1.default.find({ isSold: false, blockchain: 'sol' }).exec();
            if (currentIndex < tradesStateSol.length - 1) {
                currentIndex++;
                yield (0, solscan_service_1.displayCurrentSolTrade)(bot, chatId, tradesStateSol, currentIndex, messageId);
            }
            else {
                yield bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
            }
            break;
        case 'refresh_sol':
            yield (0, solscan_service_1.handleSolRefresh)(bot, chatId);
            break;
        case 'chart_sol':
            yield (0, solscan_service_1.handleSolChart)(bot, chatId);
            break;
        case 'spend_sol':
            try {
                const userBuy = yield (0, solscan_service_1.handleSpendsol)(bot, chatId);
                if (!userBuy || isNaN(Number(userBuy))) {
                    throw new Error('Invalid amount.');
                }
                const amount = Number(userBuy);
                const receipt = yield (0, solbuy_trader_1.buySolToken)(chatId, amount);
                if (receipt) {
                    yield bot.sendMessage(chatId, `✅ Successfully purchased tokens:\n\n` +
                        `🔗 Transaction Hash: ${receipt}\n\n` +
                        `👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${receipt}\nTo see your transaction`);
                }
                else {
                    yield bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
                }
            }
            catch (error) {
                console.error('Error purchasing tokens:', error);
                const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
                yield bot.sendMessage(chatId, `An error occurred while purchasing tokens}`);
            }
            break;
        case 'sell_all_sol':
            yield (0, solscan_service_1.handleSolSellAll)(bot, chatId);
            break;
        case 'sell_25_sol':
            yield (0, solscan_service_1.sellSolTokenPercentage)(bot, chatId, 25);
            break;
        case 'sell_50_sol':
            yield (0, solscan_service_1.sellSolTokenPercentage)(bot, chatId, 50);
            break;
        case 'sell_75_sol':
            yield (0, solscan_service_1.sellSolTokenPercentage)(bot, chatId, 75);
            break;
        case 'refresh_wallet_sol':
            try {
                const user = yield user_models_1.default.findOne({ chat_id: chatId });
                if (user) {
                    const previousValue = getPreviousValue(chatId, 'sol');
                    // console.log(previousValue)
                    if (previousValue) {
                        const { address } = previousValue;
                        // Fetch the stored message ID, content, and markup
                        const storedMessageId = user.sol_dashboard_message_id;
                        const storedContent = user.sol_dashboard_content;
                        const storedMarkup = user.sol_dashboard_markup;
                        const balance = Number(previousValue.balance);
                        const formattedBalance = balance.toFixed(6);
                        // Construct new content and markup
                        const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your SOL Dashboard!
                          \n📍 **Address:** \`${address}\`
                          \n🔗 **Blockchain:** SOL
                          \n💰 **Balance:** \`${formattedBalance}  SOL\`
                         `;
                        const newMarkup = {
                            inline_keyboard: [
                                [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_sol' }) }],
                                [
                                    { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_sol' }) },
                                    { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_sol' }) }
                                ],
                                [
                                    { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_sol' }) },
                                    { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_sol_wallet' }) }
                                ],
                                [
                                    {
                                        text: "* Dismiss message",
                                        callback_data: JSON.stringify({
                                            command: "dismiss_message",
                                        }),
                                    },
                                ]
                            ]
                        };
                        // Compare current content and markup with new content and markup
                        if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                            yield bot.editMessageText(newContent, {
                                chat_id: chatId,
                                message_id: storedMessageId,
                                parse_mode: 'Markdown',
                                reply_markup: newMarkup,
                            });
                            // Update stored content and markup
                            yield user_models_1.default.updateOne({ chat_id: chatId }, {
                                $set: {
                                    'sol_dashboard_content': newContent,
                                    'sol_dashboard_markup': newMarkup
                                }
                            });
                        }
                    }
                    else {
                        yield bot.sendMessage(chatId, 'No wallet address found.');
                    }
                }
                else {
                    yield bot.sendMessage(chatId, 'User not found.');
                }
            }
            catch (error) {
                console.error('Failed to refresh wallet et:', error);
                yield bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
            }
            break;
        default:
        // console.warn('Unknown command:', data.command);
    }
});
exports.handleCallbackQuery = handleCallbackQuery;
// Handle dismissing messages
const handleDismissMessage = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield bot.deleteMessage(chatId, messageId);
    }
    catch (error) {
        console.error('Failed to delete message:', error);
    }
});
// Handle blockchain view
const handleBlockchainView = (bot, chatId, command) => __awaiter(void 0, void 0, void 0, function* () {
    const selectedBlockchain = command.split('_')[1];
    yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: selectedBlockchain } });
    switch (selectedBlockchain) {
        case 'bsc':
            yield (0, bsc_dashboard_1.handleBscDashboard)(bot, chatId);
            break;
        case 'base':
            yield (0, base_dashboard_1.handleBaseDashboard)(bot, chatId);
            break;
        case 'eth':
            yield (0, eth_dashboard_1.handleEthDashboard)(bot, chatId);
            break;
        case 'sol':
            yield (0, sol_dashboard_1.handleSolDashboard)(bot, chatId);
            break;
        // Add cases for other blockchains as needed
    }
});
function handleActiveTradesBsc(bot, chatId) {
    throw new Error('Function not implemented.');
}
