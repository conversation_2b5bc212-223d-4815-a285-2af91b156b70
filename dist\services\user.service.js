"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const index_1 = require("../models/index");
exports.UserService = {
    createUser: (props) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.create(props);
        }
        catch (err) {
            console.log(err);
            throw new Error(err.message);
        }
    }),
    findById: (id) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.findById(id);
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    findOne: (filter) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.findOne(Object.assign(Object.assign({}, filter), { retired: false }));
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    findLastOne: (filter) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.findOne(filter).sort({ updatedAt: -1 });
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    find: (filter) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.find(filter);
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    findAndSort: (filter) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.find(filter).sort({ retired: 1, nonce: 1 }).exec();
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    updateOne: (id, updateData) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.findByIdAndUpdate(id, updateData, { new: true });
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    findAndUpdateOne: (filter, updateData) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.findOneAndUpdate(filter, updateData, { new: true });
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    updateMany: (filter, updateData) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.updateMany(filter, { $set: updateData });
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    deleteOne: (filter) => __awaiter(void 0, void 0, void 0, function* () {
        try {
            return yield index_1.UserSchema.findOneAndDelete(filter);
        }
        catch (err) {
            throw new Error(err.message);
        }
    }),
    extractUniqueCode: (text) => {
        const words = text.split(' ');
        return words.length > 1 ? words[1] : null;
    },
    extractPNLdata: (text) => {
        const words = text.split(' ');
        if (words.length > 1 && words[1].endsWith('png')) {
            return words[1].replace('png', '.png');
        }
        return null;
    },
};
