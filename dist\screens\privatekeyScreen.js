"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.privatekeyHandler = void 0;
const user_models_1 = __importDefault(require("../models/user.models"));
const privatekeyHandler = (bot, msg) => __awaiter(void 0, void 0, void 0, function* () {
    const { username, id: chat_id, first_name, last_name } = msg.chat;
    try {
        // Check if the user already exists in the database
        const user = yield user_models_1.default.findOne({ chat_id });
        if (user) {
            const caption = `👋 View your Keys (Tap to copy)!\n\n` +
                `SOL: <code><tg-spoiler> ${user.sol_wallet.private_key}</tg-spoiler></code>\n` +
                `BSC: <code><tg-spoiler>${user.bsc_wallet.private_key}</tg-spoiler></code>\n` +
                `BASE: <code><tg-spoiler>${user.base_wallet.private_key}</tg-spoiler></code>\n` +
                `ETH: <code><tg-spoiler> ${user.eth_wallet.private_key}</tg-spoiler></code>\n\n`;
            yield bot.sendMessage(chat_id, caption, {
                parse_mode: 'HTML',
                disable_web_page_preview: true,
                reply_markup: {
                    inline_keyboard: [
                        [
                            {
                                text: "* Dismiss message",
                                callback_data: JSON.stringify({
                                    command: "dismiss_message",
                                }),
                            },
                        ],
                    ],
                },
            });
        }
        else {
            yield bot.sendMessage(chat_id, 'User not found. Please register first.');
        }
    }
    catch (error) {
        console.error('Error handling wallet selection:', error);
        yield bot.sendMessage(chat_id, 'An error occurred while processing your request. Please try again later.');
    }
});
exports.privatekeyHandler = privatekeyHandler;
