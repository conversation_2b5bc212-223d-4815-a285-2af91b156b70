/**
 * Snipe Monitor
 *
 * This module monitors tokens for liquidity and executes snipe trades
 * when conditions are met.
 */

import http from 'http';
import { Server } from 'socket.io';
import TelegramBot from 'node-telegram-bot-api';
import Snipe, { ISnipe, ISnipeConfig } from '../models/snipe.model';
import { BlockchainType, TraderFactory } from '../services/trading/TradingInterface';
import { logger } from '../services/logger/LoggerService';
import { getLiquidityInfoFromDexscreener } from '../services/checkliquidity.service';
import { cacheService } from '../services/cache/RedisService';

// Constants
const BATCH_SIZE = 10; // Process 10 tokens at a time (reduced for better reliability)
const UPDATE_INTERVAL = 30000; // 30 seconds
const MAX_RETRIES = 3;
const CACHE_TTL = 60; // 1 minute cache for liquidity checks

// Create server and socket.io instance
const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

/**
 * Send a message to a Telegram user
 * @param bot Telegram bot instance
 * @param chatId Chat ID to send message to
 * @param message Message text
 * @param options Message options
 */
const sendMessageToUser = async (
  bot: TelegramBot,
  chatId: number,
  message: string,
  options?: TelegramBot.SendMessageOptions
): Promise<void> => {
  try {
    await bot.sendMessage(chatId, message, options);
    logger.debug(`Message sent to chat ID ${chatId}`, {
      chatId,
      messageLength: message.length
    });
  } catch (error) {
    logger.error(`Failed to send message to chat ID ${chatId}`, {
      error: (error as Error).message,
      chatId
    });
  }
};

/**
 * Check liquidity for a token
 * @param contractAddress The token contract address
 * @param blockchain The blockchain
 * @returns Liquidity information
 */
async function checkLiquidity(contractAddress: string, blockchain: string): Promise<any> {
  const cacheKey = `liquidity:${blockchain}:${contractAddress}`;

  try {
    // Check cache first
    const cachedResult = await cacheService.get(cacheKey);
    if (cachedResult) {
      logger.debug(`Using cached liquidity info for ${contractAddress}`, {
        contractAddress,
        blockchain
      });
      return cachedResult;
    }

    // Get liquidity info
    const result = await getLiquidityInfoFromDexscreener(contractAddress);

    // Cache the result
    await cacheService.set(cacheKey, result, CACHE_TTL);

    return result;
  } catch (error) {
    logger.error(`Error checking liquidity for ${contractAddress}`, {
      error: (error as Error).message,
      contractAddress,
      blockchain
    });
    throw error;
  }
}

/**
 * Execute a snipe
 * @param snipe The snipe configuration
 * @param bot Telegram bot instance
 * @returns Result of the snipe
 */
async function executeSnipe(snipe: ISnipe, bot: TelegramBot): Promise<any> {
  const { chatId, contractAddress, blockchain } = snipe;

  try {
    logger.info(`Executing snipe for ${contractAddress} on ${blockchain}`, {
      chatId,
      contractAddress,
      blockchain
    });

    // Get the appropriate trader
    let result;

    try {
      // Try to use the trader factory first
      if (TraderFactory.hasTrader(blockchain as BlockchainType)) {
        const trader = TraderFactory.getTrader(blockchain as BlockchainType);

        // Get snipe configuration
        const config = snipe.config;
        const spendKey = `spend_${blockchain}` as keyof typeof config;
        const slippageKey = `slippage_${blockchain}` as keyof typeof config;

        const spendAmount = config[spendKey]?.toString() || '0.1';
        const slippage = Number(config[slippageKey]) || 5;

        if (parseFloat(spendAmount) <= 0) {
          throw new Error(`Invalid spend amount: ${spendAmount}`);
        }

        // Execute the trade
        result = await trader.buyTokens(
          contractAddress,
          spendAmount,
          '', // Wallet address will be determined by the trader
          { slippage }
        );

        if (!result.success) {
          throw new Error(result.error || 'Transaction failed');
        }
      } else {
        // If no trader is registered, throw an error
        throw new Error(`No trader registered for blockchain: ${blockchain}`);
      }

      // Update the snipe status
      const executedKey = `isExecuted_${blockchain}` as keyof typeof snipe.config;
      await Snipe.updateOne(
        { _id: snipe._id },
        {
          $set: {
            [`config.${String(executedKey)}`]: true,
            [`config.isBought`]: true
          }
        }
      );

      // Notify the user
      // Get the spend amount from the config
      const config = snipe.config;
      const spendKey = `spend_${blockchain}` as keyof typeof config;
      const spendAmount = config[spendKey]?.toString() || '0.1';

      await sendMessageToUser(
        bot,
        chatId,
        `✅ Successfully sniped ${contractAddress} on ${blockchain.toUpperCase()}!\n\n` +
        `🔗 Transaction Hash: ${result.transactionHash}\n` +
        `💰 Amount Spent: ${spendAmount} ${blockchain.toUpperCase()}`
      );

      // Emit socket event
      io.emit('tokenUpdate', {
        tokenAddress: contractAddress,
        tokenInfo: {
          hasLiquidity: true,
          message: 'Liquidity detected and token purchased.',
          transactionHash: result.transactionHash
        },
      });

      logger.info(`Successfully sniped token ${contractAddress} on ${blockchain}`, {
        chatId,
        contractAddress,
        blockchain,
        transactionHash: result.transactionHash
      });

      return { success: true, transactionHash: result.transactionHash };
    } catch (error) {
      logger.error(`Error executing snipe for ${contractAddress}`, {
        error: (error as Error).message,
        chatId,
        contractAddress,
        blockchain
      });

      // Increment retry counter
      const retryKey = `retries_${blockchain}` as keyof typeof snipe.config;
      const currentRetries = (snipe.config[retryKey] as number) || 0;

      await Snipe.updateOne(
        { _id: snipe._id },
        { $set: { [`config.${String(retryKey)}`]: currentRetries + 1 } }
      );

      // Notify the user of the error
      await sendMessageToUser(
        bot,
        chatId,
        `❌ Error sniping ${contractAddress} on ${blockchain.toUpperCase()}: ${(error as Error).message}`
      );

      throw error;
    }
  } catch (error) {
    logger.error(`Failed to execute snipe for ${contractAddress}`, {
      error: (error as Error).message,
      chatId,
      contractAddress,
      blockchain
    });
    return { success: false, error: (error as Error).message };
  }
}
