(()=>{"use strict";var e={1888:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.fetchBalancesForAllChains=void 0;const s=n(9321);a(n(818)).default.config();const o=(e,t)=>{const n=new s.ethers.providers.JsonRpcProvider(e,t);return n.on("error",(t=>{console.error(`Provider error for ${e}:`,t)})),n},i={eth:o(process.env.ETH_PROVIDER_URL||"",1),bsc:o(process.env.BSC_PROVIDER_URL||"",56),base:o(process.env.BASE_PROVIDER_URL||"",8453)};r(void 0,void 0,void 0,(function*(){for(const[e,t]of Object.entries(i))try{const n=yield t.getNetwork();console.log(`${e.toUpperCase()} Network:`,n)}catch(t){console.error(`Failed to connect to ${e.toUpperCase()} network:`,t)}})).catch(console.error);const d={eth:process.env.ETH_PRIVATE_KEY||"4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b",bsc:process.env.BSC_PRIVATE_KEY||"4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b",base:process.env.BASE_PRIVATE_KEY||"4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b"},c={eth:new s.ethers.Wallet(d.eth,i.eth),bsc:new s.ethers.Wallet(d.bsc,i.bsc),base:new s.ethers.Wallet(d.base,i.base)},l=[process.env.TOKEN_ADDRESS_1||"",process.env.TOKEN_ADDRESS_2||"",process.env.TOKEN_ADDRESS_3||""],u=["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)","function symbol() view returns (string)"],g=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=new s.ethers.Contract(t,u,e),[r,a,o]=yield Promise.all([n.balanceOf(e.address),n.decimals(),n.symbol()]);return{tokenAddress:t,balance:r,symbol:o,decimals:a}}catch(n){return console.error(`Error fetching balance for token ${t} on ${e}:`,n),null}}));t.fetchBalancesForAllChains=()=>r(void 0,void 0,void 0,(function*(){const e={};for(const[t,n]of Object.entries(c)){e[t]={};const r=yield n.getBalance();e[t].native=s.ethers.utils.formatEther(r);for(const r of l)if(r)try{const a=yield g(n,r);if(a){const{balance:n,decimals:o,symbol:i}=a;e[t][r]=s.ethers.utils.formatUnits(n,o)}}catch(e){console.error(`Error fetching balance for token ${r} on ${t}:`,e)}}return e})),(0,t.fetchBalancesForAllChains)().catch(console.error)},8014:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SET_SOL_SPEND=t.SET_ETH_SPEND=t.SET_BASE_SPEND=t.SET_BSC_SPEND=t.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE=t.INPUT_SOL_CONTRACT_ADDRESS=t.INPUT_ETH_CONTRACT_ADDRESS=t.INPUT_BASE_CONTRACT_ADDRESS=t.INPUT_CONTRACT_ADDRESS=void 0,t.INPUT_CONTRACT_ADDRESS="Please submit the BSC contract address you want to scan.",t.INPUT_BASE_CONTRACT_ADDRESS="Please submit the BASE contract address you want to scan.",t.INPUT_ETH_CONTRACT_ADDRESS="Please submit the ETH contract address you want to scan.",t.INPUT_SOL_CONTRACT_ADDRESS="Please submit the SOL contract address you want to scan.",t.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE="Please enter the BSC token you want to snipe",t.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE="Please enter the BASE token you want to snipe",t.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE="Please enter the ETH token you want to snipe",t.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE="Please enter the SOL token you want to snipe",t.SET_BSC_SPEND="Input how much BSC to spend",t.SET_BASE_SPEND="Input how much BASE to spend",t.SET_ETH_SPEND="Input how much ETH to spend",t.SET_SOL_SPEND="Input how much SOL to spend"},3433:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.initializeSocketServer=t.startPeriodicSnipeUpdates=t.updateTokens=void 0;const s=a(n(8611)),o=n(4437),i=a(n(8087)),d=n(871),c=n(1213),l=n(8904),u=n(8708),g=s.default.createServer(),y=new o.Server(g,{cors:{origin:"*",methods:["GET","POST"]}});g.setMaxListeners(20),y.setMaxListeners(20);const h=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{yield e.sendMessage(t,n,a),c.logger.debug(`Message sent to chat ID ${t}`,{chatId:t,messageLength:n.length})}catch(e){c.logger.error(`Failed to send message to chat ID ${t}`,{error:e.message,chatId:t})}}));function p(e,t){return r(this,void 0,void 0,(function*(){const n=`liquidity:${t}:${e}`;try{const r=yield u.cacheService.get(n);if(r)return c.logger.debug(`Using cached liquidity info for ${e}`,{contractAddress:e,blockchain:t}),r;const a=yield(0,l.getLiquidityInfoFromDexscreener)(e);return yield u.cacheService.set(n,a,60),a}catch(n){throw c.logger.error(`Error checking liquidity for ${e}`,{error:n.message,contractAddress:e,blockchain:t}),n}}))}function f(e,t){return r(this,void 0,void 0,(function*(){var n,r;const{chatId:a,contractAddress:s,blockchain:o}=e;try{let l;c.logger.info(`Executing snipe for ${s} on ${o}`,{chatId:a,contractAddress:s,blockchain:o});try{if(!d.TraderFactory.hasTrader(o))throw new Error(`No trader registered for blockchain: ${o}`);{const t=d.TraderFactory.getTrader(o),r=e.config,a=`spend_${o}`,i=`slippage_${o}`,c=(null===(n=r[a])||void 0===n?void 0:n.toString())||"0.1",u=Number(r[i])||5;if(parseFloat(c)<=0)throw new Error(`Invalid spend amount: ${c}`);if(l=yield t.buyTokens(s,c,"",{slippage:u}),!l.success)throw new Error(l.error||"Transaction failed")}const u=`isExecuted_${o}`;yield i.default.updateOne({_id:e._id},{$set:{[`config.${String(u)}`]:!0,"config.isBought":!0}});const g=(null===(r=e.config[`spend_${o}`])||void 0===r?void 0:r.toString())||"0.1";return yield h(t,a,`✅ Successfully sniped ${s} on ${o.toUpperCase()}!\n\n🔗 Transaction Hash: ${l.transactionHash}\n💰 Amount Spent: ${g} ${o.toUpperCase()}`),y.emit("tokenUpdate",{tokenAddress:s,tokenInfo:{hasLiquidity:!0,message:"Liquidity detected and token purchased.",transactionHash:l.transactionHash}}),c.logger.info(`Successfully sniped token ${s} on ${o}`,{chatId:a,contractAddress:s,blockchain:o,transactionHash:l.transactionHash}),{success:!0,transactionHash:l.transactionHash}}catch(n){c.logger.error(`Error executing snipe for ${s}`,{error:n.message,chatId:a,contractAddress:s,blockchain:o});const r=`retries_${o}`,d=e.config[r]||0;throw yield i.default.updateOne({_id:e._id},{$set:{[`config.${String(r)}`]:d+1}}),yield h(t,a,`❌ Error sniping ${s} on ${o.toUpperCase()}: ${n.message}`),n}}catch(e){return c.logger.error(`Failed to execute snipe for ${s}`,{error:e.message,chatId:a,contractAddress:s,blockchain:o}),{success:!1,error:e.message}}}))}function m(){return r(this,void 0,void 0,(function*(){c.logger.info("Starting graceful shutdown");try{y.removeAllListeners();const e=yield y.fetchSockets();for(const t of e)t.disconnect(!0);yield new Promise((e=>{g.close((()=>{c.logger.info("HTTP server closed"),e()}))})),c.logger.info("Graceful shutdown completed")}catch(e){throw c.logger.error("Error during graceful shutdown",{error:e.message}),e}}))}t.updateTokens=e=>r(void 0,void 0,void 0,(function*(){const t=Date.now();c.logger.info("Started checking tokens for liquidity");try{const n=yield i.default.find({"config.isConfigured":!0,"config.isBought":!1,$or:[{"config.isExecuted_bsc":!1},{"config.isExecuted_sol":!1},{"config.isExecuted_eth":!1},{"config.isExecuted_base":!1}]}).lean();if(0===n.length)return void c.logger.debug("No snipes to process");c.logger.info(`Found ${n.length} snipes to process`);const r=n.reduce(((e,t)=>{const n=t.blockchain;return e[n]||(e[n]=[]),e[n].push(t),e}),{});for(const[t,n]of Object.entries(r)){c.logger.info(`Processing ${n.length} snipes for ${t}`);const r=[...new Set(n.map((e=>e.contractAddress)))];for(let a=0;a<r.length;a+=10){const s=r.slice(a,a+10);c.logger.debug(`Processing batch ${Math.floor(a/10)+1} of ${Math.ceil(r.length/10)} for ${t}`);const o=yield Promise.all(s.map((e=>p(e,t))));for(let r=0;r<s.length;r++){const a=s[r],d=o[r],l=n.filter((e=>e.contractAddress===a));for(const n of l){yield i.default.updateOne({_id:n._id},{$set:{hasLiquidity:d.hasLiquidity,lastPriceUpdate:new Date}});const r=`isExecuted_${t}`;d.hasLiquidity&&!n.config[r]?(c.logger.info(`Liquidity found for token ${a} on ${t}, executing snipe`),f(n,e).catch((e=>{c.logger.error(`Failed to execute snipe for ${a}`,{error:e.message,blockchain:t,address:a})})),yield h(e,n.chatId,`🔍 Liquidity detected for ${a} on ${t.toUpperCase()}! Attempting to snipe...`),y.emit("liquidityFound",{tokenAddress:a,blockchain:t,timestamp:(new Date).toISOString()})):d.hasLiquidity||c.logger.debug(`No liquidity found for token ${a} on ${t}`)}}a+10<r.length&&(yield new Promise((e=>setTimeout(e,2e3))))}}const a=Date.now()-t;c.logger.info(`Completed token update in ${a}ms`,{snipeCount:n.length,durationMs:a})}catch(e){c.logger.error("Error updating tokens",{error:e.message,stack:e.stack})}})),t.startPeriodicSnipeUpdates=e=>(c.logger.info("Starting periodic snipe updates (every 30 seconds)"),setInterval((()=>(0,t.updateTokens)(e)),3e4)),t.initializeSocketServer=(e,n)=>{y.on("connection",(e=>{c.logger.info("New client connected",{socketId:e.id}),_(e),e.on("disconnect",(()=>{c.logger.info("Client disconnected",{socketId:e.id})})),e.on("execute_snipe",(t=>r(void 0,void 0,void 0,(function*(){try{const r=yield i.default.findById(t.snipeId);if(!r)return void e.emit("error",{message:"Snipe not found"});f(r,n).then((n=>{e.emit("snipe_executed",{snipeId:t.snipeId,result:n})})).catch((t=>{e.emit("error",{message:t.message})})),e.emit("snipe_queued",{snipeId:t.snipeId,message:"Snipe has been queued for execution"}),c.logger.info(`Manual snipe queued for ${r.contractAddress}`,{snipeId:t.snipeId,blockchain:r.blockchain})}catch(n){c.logger.error("Error executing manual snipe",{error:n.message,snipeId:t.snipeId}),e.emit("error",{message:n.message})}})))),e.on("health_check",(()=>r(void 0,void 0,void 0,(function*(){try{const t={status:"healthy",uptime:process.uptime(),timestamp:(new Date).toISOString()};e.emit("health_status",t)}catch(t){c.logger.error("Error sending health status",{error:t.message}),e.emit("error",{message:t.message})}}))))})),g.listen(e,(()=>{c.logger.info(`Socket.IO server is running on port ${e}`)})),(0,t.startPeriodicSnipeUpdates)(n),process.on("SIGTERM",(()=>r(void 0,void 0,void 0,(function*(){c.logger.info("SIGTERM received, shutting down gracefully"),yield m()})))),process.on("SIGINT",(()=>r(void 0,void 0,void 0,(function*(){c.logger.info("SIGINT received, shutting down gracefully"),yield m()}))))};const _=e=>r(void 0,void 0,void 0,(function*(){try{const t=yield i.default.find({"config.isConfigured":!0,"config.isBought":!1}).lean();e.emit("snipe_data",{snipes:t}),c.logger.debug("Sent snipe data to client",{socketId:e.id,snipeCount:t.length})}catch(t){c.logger.error("Error sending snipe data",{error:t.message,socketId:e.id})}}))},4736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.clearAllContractAddresses=t.getAllCurrentContractAddresses=t.setCurrentContractAddress=t.getCurrentContractAddress=void 0;const r=n(871),a={currentContractAddresses:{[r.BlockchainType.BSC]:null,[r.BlockchainType.ETH]:null,[r.BlockchainType.SOL]:null,[r.BlockchainType.BASE]:null}};t.getCurrentContractAddress=e=>a.currentContractAddresses[e]||null,t.setCurrentContractAddress=(e,t)=>{a.currentContractAddresses[e]=t},t.getAllCurrentContractAddresses=()=>Object.assign({},a.currentContractAddresses),t.clearAllContractAddresses=()=>{Object.keys(a.currentContractAddresses).forEach((e=>{a.currentContractAddresses[e]=null}))},t.default=a},4103:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.updateAutoTradeAsSold=function(e,t,n,r){return o(this,void 0,void 0,(function*(){yield c.findOneAndUpdate({contractAddress:t},{isSold:!0}).exec()}))};const i=s(n(6037)),d=new i.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String},contractName:{type:String,default:""},isSold:{type:Boolean,default:!1},isInitialized:{type:String,enum:["pending","ongoing"],default:"pending"},config:{takeProfit:{type:Number,default:0},stopLoss:{type:Number,default:0},spendAmount:{type:Number,default:0}}}),c=i.default.model("AutoTrade",d);t.default=c},6131:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.UserSchema=void 0;const a=r(n(8722));t.UserSchema=a.default},8087:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const o=s(n(6037)),i=new o.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String,required:!0},config:{retries_base:{type:Number},retries_eth:{type:Number},retries_sol:{type:Number},retries_bsc:{type:Number},slippage_bsc:{type:Number},spend_bsc:{type:Number},timeDelay_bsc:{type:Number},blockDelay_bsc:{type:Number},retriesOnFail_bsc:{type:Number},slippage_base:{type:Number},spend_base:{type:Number},timeDelay_base:{type:Number},blockDelay_base:{type:Number},retriesOnFail_base:{type:Number},slippage_sol:{type:Number},spend_sol:{type:Number},timeDelay_sol:{type:Number},blockDelay_sol:{type:Number},retriesOnFail_sol:{type:Number},slippage_eth:{type:Number},spend_eth:{type:Number},timeDelay_eth:{type:Number},blockDelay_eth:{type:Number},retriesOnFail_eth:{type:Number},isConfigured:{type:Boolean,default:!1},isBought:{type:Boolean,default:!1},isExecuted_bsc:{type:Boolean,default:!1},isExecuted_base:{type:Boolean,default:!1},isExecuted_sol:{type:Boolean,default:!1},isExecuted_eth:{type:Boolean,default:!1}},hasLiquidity:{type:Boolean,default:!1},lastPriceUpdate:{type:Date}}),d=o.default.model("Snipe",i);t.default=d},6786:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.updateTradeAsSold=function(e,t,n,r){return o(this,void 0,void 0,(function*(){yield c.findOneAndUpdate({contractAddress:t},{isSold:!0}).exec()}))};const i=s(n(6037)),d=new i.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String},contractName:{type:String,default:""},buyAmount:{type:Number,default:0},sellAmount:{type:Number,default:0},isSold:{type:Boolean,default:!1},config:{takeProfit:{type:Number,default:0},stopLoss:{type:Number,default:0},spendAmount:{type:Number,default:0}}}),c=i.default.model("Trade",d);t.default=c},8722:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const o=s(n(6037)),i=new o.Schema({chat_id:{type:Number,required:!0},first_name:{type:String,required:!0},last_name:{type:String},username:{type:String},sol_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},bsc_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},base_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},eth_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},preset_setting:{type:[Number],default:[.01,1,5,10]},nonce:{type:Number,default:0},retired:{type:Boolean,default:!1},referrer_code:{type:String,default:null},referrer_wallet:{type:String,default:null},referral_code:{type:String,default:null},referral_date:{type:String,default:null},schedule:{type:String,default:"60"},burn_fee:{type:Boolean,default:!1},auto_buy:{type:Boolean,default:!1},auto_buy_amount:{type:String,default:"0"},auto_sell_amount:{type:String,default:"0"},trades:{bsc:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}],sol:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}],eth:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}],base:[{type:o.Schema.Types.ObjectId,ref:"Trade",default:[]}]},currentBlockchain:{type:String,enum:["bsc","sol","eth","base"],default:"bsc"},sol_config:{slippage:{type:o.Schema.Types.Decimal128,default:.6},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},bsc_config:{slippage:{type:o.Schema.Types.Decimal128,default:.1},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},base_config:{slippage:{type:o.Schema.Types.Decimal128,default:.1},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},eth_config:{slippage:{type:o.Schema.Types.Decimal128,default:.2},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:o.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:o.Schema.Types.Decimal128,default:0},maxMarketCap:{type:o.Schema.Types.Decimal128,default:0},maxLiquidity:{type:o.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:o.Schema.Types.Decimal128,default:0},sellLow:{type:o.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},bsc_dashboard_message_id:{type:Number},bsc_dashboard_content:{type:String},bsc_dashboard_markup:{type:o.Schema.Types.Mixed},eth_dashboard_message_id:{type:Number},eth_dashboard_content:{type:String},eth_dashboard_markup:{type:o.Schema.Types.Mixed},sol_dashboard_message_id:{type:Number},sol_dashboard_content:{type:String},sol_dashboard_markup:{type:o.Schema.Types.Mixed},base_dashboard_message_id:{type:Number},base_dashboard_content:{type:String},base_dashboard_markup:{type:o.Schema.Types.Mixed},bscCurrentContractAddress:{type:String},solCurrentContractAddress:{type:String},baseCurrentContractAddress:{type:String},ethCurrentContractAddress:{type:String},bscCurrentTokenName:{type:String},solCurrentTokenName:{type:String},baseCurrentTokenName:{type:String},ethCurrentTokenName:{type:String},snipes:[{type:o.Schema.Types.ObjectId,ref:"Snipe",default:[]}]}),d=o.default.model("User",i);t.default=d},872:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.TransactionType=t.BlockchainType=void 0;const s=a(n(6037)),o=n(1213),i=n(4199);var d,c;!function(e){e.BSC="bsc",e.ETH="eth",e.SOL="sol",e.BASE="base"}(d||(t.BlockchainType=d={})),function(e){e.INCOMING="incoming",e.OUTGOING="outgoing",e.UNKNOWN="unknown"}(c||(t.TransactionType=c={}));const l=new s.default.Schema({userId:{type:s.default.Schema.Types.ObjectId,ref:"User",required:!0},chatId:{type:Number,required:!0},walletAddress:{type:String,required:!0},blockchain:{type:String,required:!0,enum:Object.values(d)},balance:{type:String,required:!0,default:"0"},previousBalance:{type:String,default:"0"},lastUpdated:{type:Date,default:Date.now},lastTransaction:{hash:String,type:{type:String,enum:Object.values(c)},amount:String,timestamp:Date}});l.index({walletAddress:1,blockchain:1},{unique:!0}),l.index({userId:1}),l.index({chatId:1}),l.statics.updateBalance=function(e,t,n,a,s){return r(this,void 0,void 0,(function*(){try{const r=yield this.findOne({walletAddress:e,blockchain:t});if(!r)return o.logger.warn(`No wallet balance record found for ${e} on ${t}`),null;const i=r.balance,d=(parseFloat(n)-parseFloat(i)).toString();return r.previousBalance=i,r.balance=n,r.lastUpdated=new Date,a&&(r.lastTransaction={hash:a,type:s||c.UNKNOWN,amount:Math.abs(parseFloat(d)).toString(),timestamp:new Date}),yield r.save(),r}catch(r){return o.logger.error(`Error updating wallet balance for ${e} on ${t}`,{error:r.message,walletAddress:e,blockchain:t,newBalance:n}),null}}))},l.statics.sendBalanceAlert=function(e,t){return r(this,void 0,void 0,(function*(){var n,r;try{const a=new i.BotService(e),s=parseFloat(t.previousBalance).toFixed(6),o=parseFloat(t.balance).toFixed(6),c=(parseFloat(t.balance)-parseFloat(t.previousBalance)).toFixed(6),l=parseFloat(c)>0;if(Math.abs(parseFloat(c))<1e-6)return;const u=(l?"📈 Balance Increased":"📉 Balance Decreased")+"\n\n"+`Wallet: ${t.walletAddress.substring(0,6)}...${t.walletAddress.substring(t.walletAddress.length-4)}\n`+`Network: ${t.blockchain.toUpperCase()}\n`+`Previous: ${s}\n`+`Current: ${o}\n`+`Change: ${l?"+":""}${c}`;let g="";if(null===(n=t.lastTransaction)||void 0===n?void 0:n.hash){const e=t.lastTransaction.hash;switch(t.blockchain){case d.BSC:g=`https://bscscan.com/tx/${e}`;break;case d.ETH:g=`https://etherscan.io/tx/${e}`;break;case d.BASE:g=`https://basescan.org/tx/${e}`;break;case d.SOL:g=`https://solscan.io/tx/${e}`}}const y=(null===(r=t.lastTransaction)||void 0===r?void 0:r.hash)?{inline_keyboard:[[{text:"🔍 View Transaction",url:g}],[{text:"❌ Dismiss",callback_data:JSON.stringify({command:"dismiss_message"})}]]}:void 0,h={parse_mode:"HTML",disable_notification:Math.abs(parseFloat(c))<.01,reply_markup:y};yield a.sendMessage(t.chatId,u,h)}catch(e){o.logger.error(`Error sending balance alert for ${t.walletAddress}`,{error:e.message,walletAddress:t.walletAddress,blockchain:t.blockchain,chatId:t.chatId})}}))};const u=s.default.model("WalletBalance",l);t.default=u},566:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.showConfigKeyboard=void 0;const s=a(n(6261)),o=a(n(8722)),i=new s.default,d=e=>({inline_keyboard:[[{text:`Slippage: ${e.slippage}`,callback_data:JSON.stringify({command:"set_slippage_base"})},{text:`Max Gas Price: ${e.maxGasPrice}`,callback_data:JSON.stringify({command:"set_max_gas_price_base"})}],[{text:`Max Gas Limit: ${e.maxGasLimit}`,callback_data:JSON.stringify({command:"set_max_gas_limit_base"})},{text:`Min Liquidity: ${e.minLiquidity}`,callback_data:JSON.stringify({command:"set_min_liquidity_base"})}],[{text:`Max Market Cap: ${e.maxMarketCap}`,callback_data:JSON.stringify({command:"set_max_market_cap_base"})},{text:`Max Liquidity: ${e.maxLiquidity}`,callback_data:JSON.stringify({command:"set_max_liquidity_base"})}],[{text:"Auto Buy: "+(e.autoBuy?"✅":"❌"),callback_data:JSON.stringify({command:"toggle_auto_buy_base",state:e.autoBuy})},{text:"Auto Sell: "+(e.autoSell?"✅":"❌"),callback_data:JSON.stringify({command:"toggle_auto_sell_base",state:e.autoSell})}],[{text:"Auto Approve: "+(e.autoApprove?"✅":"❌"),callback_data:JSON.stringify({command:"toggle_auto_approve_base",state:e.autoApprove})},{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_base"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const a=yield o.default.findOne({chat_id:t}).exec();if(!a)throw new Error("User not found.");const s=`${a.currentBlockchain}_config`,d=Object.assign(Object.assign({},a[s]),n),c={[s]:d};yield o.default.updateOne({chat_id:t},{$set:c}).exec(),i.emit("configUpdated",t,d,e);const l=yield e.sendMessage(t,"✅ Configuration updated successfully.");setTimeout((()=>r(void 0,void 0,void 0,(function*(){try{yield e.deleteMessage(t,l.message_id)}catch(e){console.error("Failed to delete success message:",e)}}))),5e3)}catch(n){console.error("Error updating configuration:",n),yield e.sendMessage(t,"❌ An error occurred while updating your wallet configurations.")}}));i.on("configUpdated",((e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chat_id:e}).exec();if(!r)throw new Error("User not found.");const a=d(t),s=r[`${r.currentBlockchain}_dashboard_message_id`];yield n.editMessageReplyMarkup(a,{chat_id:e,message_id:s})}catch(e){console.error("Error updating the configuration keyboard:",e)}}))));const l={},u=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(l[t]){const n=l[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return l[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${a}:`,e.text),l[t]=Object.assign(Object.assign({},l[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}}));t.showConfigKeyboard=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield o.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found.");const a=n[`${n.currentBlockchain}_config`],s=d(a),i=yield e.sendMessage(t,"Please select the configuration option to edit:",{reply_markup:s});n[`${n.currentBlockchain}_dashboard_message_id`]=i.message_id,yield n.save(),e.on("callback_query",(t=>r(void 0,void 0,void 0,(function*(){const{message:n,data:r}=t,a=null==n?void 0:n.chat.id;if(null==n||n.message_id,!a||!r)return;const s=JSON.parse(r),o=s.command;try{switch(o){case"set_slippage_base":const t=yield u(e,a,"Enter new slippage percentage:","slippage");yield c(e,a,{slippage:parseFloat(t)});break;case"set_max_gas_price_base":const n=yield u(e,a,"Enter new max gas price:","maxGasPrice");yield c(e,a,{maxGasPrice:parseFloat(n)});break;case"set_max_gas_limit_base":const r=yield u(e,a,"Enter new max gas limit:","maxGasLimit");yield c(e,a,{maxGasLimit:parseFloat(r)});break;case"set_min_liquidity_base":const o=yield u(e,a,"Enter new min liquidity:","minLiquidity");yield c(e,a,{minLiquidity:parseFloat(o)});break;case"set_max_market_cap_base":const i=yield u(e,a,"Enter new max market cap:","maxMarketCap");yield c(e,a,{maxMarketCap:parseFloat(i)});break;case"set_max_liquidity_base":const d=yield u(e,a,"Enter new max liquidity:","maxLiquidity");yield c(e,a,{maxLiquidity:parseFloat(d)});break;case"toggle_auto_buy_base":const l=!s.state;yield c(e,a,{autoBuy:l});break;case"toggle_auto_sell_base":const g=!s.state;yield c(e,a,{autoSell:g});break;case"toggle_auto_approve_base":const y=!s.state;yield c(e,a,{autoApprove:y})}yield e.answerCallbackQuery(t.id)}catch(t){console.error("Error handling callback query:",t),yield e.sendMessage(a,"❌ An error occurred while processing your request.")}}))))}catch(n){console.error("Failed to show configuration keyboard:",n),yield e.sendMessage(t,"❌ An error occurred while displaying the configuration keyboard.")}}))},1347:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBaseDashboard=t.getBaseBalance=void 0;const s=n(9321),o=a(n(8722));t.getBaseBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.BASE_PROVIDER_URL;if(!t)throw new Error("Provider is not defined in the .env file");const n=new s.ethers.providers.JsonRpcProvider(t),r=yield n.getBalance(e);return parseFloat(s.ethers.utils.formatEther(r)).toFixed(6)})),t.handleBaseDashboard=(e,n)=>r(void 0,void 0,void 0,(function*(){var r;const a=yield o.default.findOne({chat_id:n});if(!a)return void e.sendMessage(n,"User not found.");const s=a.first_name||"User",i=(null===(r=a.base_wallet)||void 0===r?void 0:r.address)||"Not set",d=`👋 Welcome, ${s}, to your BASE Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** base\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,t.getBaseBalance)(i):"0.0 BNB"} BASE\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_base"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_base"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_base"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_base_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(n,d,c);yield o.default.updateOne({chat_id:n},{$set:{base_dashboard_message_id:l.message_id,base_dashboard_content:d,base_dashboard_markup:c.reply_markup}})}))},4544:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBscDashboard=t.getBscBalance=void 0;const s=n(9321),o=a(n(8722));t.getBscBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.BSC_PROVIDER_URL;if(!t)throw new Error("BSC_PROVIDER_URL is not defined in the .env file");const n=new s.ethers.providers.JsonRpcProvider(t),r=yield n.getBalance(e);return parseFloat(s.ethers.utils.formatEther(r)).toFixed(6)})),t.handleBscDashboard=(e,n)=>r(void 0,void 0,void 0,(function*(){var r;const a=yield o.default.findOne({chat_id:n});if(!a)return void e.sendMessage(n,"User not found.");const s=a.first_name||"User",i=(null===(r=a.bsc_wallet)||void 0===r?void 0:r.address)||"Not set",d=`👋 Welcome, ${s}, to your BSC Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** BSC\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,t.getBscBalance)(i):"0.0 BNB"} BNB\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_bsc"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_bsc"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_bsc"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_bsc_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(n,d,c);yield o.default.updateOne({chat_id:n},{$set:{bsc_dashboard_message_id:l.message_id,bsc_dashboard_content:d,bsc_dashboard_markup:c.reply_markup}})}))},439:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleEthDashboard=t.getEthBalance=void 0;const s=n(9321),o=a(n(8722));t.getEthBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.ETH_PROVIDER_URL;if(!t)throw new Error("Provider is not defined in the .env file");const n=new s.ethers.providers.JsonRpcProvider(t),r=yield n.getBalance(e);return parseFloat(s.ethers.utils.formatEther(r)).toFixed(6)})),t.handleEthDashboard=(e,n)=>r(void 0,void 0,void 0,(function*(){var r;const a=yield o.default.findOne({chat_id:n});if(!a)return void e.sendMessage(n,"User not found.");const s=a.first_name||"User",i=(null===(r=a.eth_wallet)||void 0===r?void 0:r.address)||"Not set",d=`👋 Welcome, ${s}, to your ETH Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** ethereum\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,t.getEthBalance)(i):"0.0 ETH"} ETH\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_eth"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_eth"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_eth"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_eth_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(n,d,c);yield o.default.updateOne({chat_id:n},{$set:{eth_dashboard_message_id:l.message_id,eth_dashboard_content:d,eth_dashboard_markup:c.reply_markup}})}))},8322:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleSolDashboard=void 0;const s=a(n(8722)),o=n(4035);t.handleSolDashboard=(e,t)=>r(void 0,void 0,void 0,(function*(){var n;try{const r=yield s.default.findOne({chat_id:t});if(!r)return void(yield e.sendMessage(t,"User not found."));const a=r.first_name||"User",i=(null===(n=r.sol_wallet)||void 0===n?void 0:n.address)||"Not set",d=`👋 Welcome, ${a}, to your sol Dashboard!\n\n📍 **Address:** \`${i}\`\n\n🔗 **Blockchain:** Solana\n\n💰 **Balance:** \`${"Not set"!==i?yield(0,o.getSolBalance)(i):"0.0 sol"} SOL\`\n\n⚙️ **Settings:** [Antirug, etc.]`,c={parse_mode:"Markdown",reply_markup:{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_sol"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"show_config"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_sol"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_sol"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_sol_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}},l=yield e.sendMessage(t,d,c);yield s.default.updateOne({chat_id:t},{$set:{sol_dashboard_message_id:l.message_id,sol_dashboard_content:d,sol_dashboard_markup:c.reply_markup}})}catch(n){console.error("Error handling Sol dashboard:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while loading the Sol dashboard: ${r}`)}}))},5542:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.privatekeyHandler=void 0;const s=a(n(8722));t.privatekeyHandler=(e,t)=>r(void 0,void 0,void 0,(function*(){const{username:n,id:r,first_name:a,last_name:o}=t.chat;try{const t=yield s.default.findOne({chat_id:r});if(t){const n=`👋 View your Keys (Tap to copy)!\n\nSOL: <code><tg-spoiler> ${t.sol_wallet.private_key}</tg-spoiler></code>\nBSC: <code><tg-spoiler>${t.bsc_wallet.private_key}</tg-spoiler></code>\nBASE: <code><tg-spoiler>${t.base_wallet.private_key}</tg-spoiler></code>\nETH: <code><tg-spoiler> ${t.eth_wallet.private_key}</tg-spoiler></code>\n\n`;yield e.sendMessage(r,n,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}else yield e.sendMessage(r,"User not found. Please register first.")}catch(t){console.error("Error handling wallet selection:",t),yield e.sendMessage(r,"An error occurred while processing your request. Please try again later.")}}))},7307:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=a(n(8722));t.default=(e,t)=>r(void 0,void 0,void 0,(function*(){const{id:n}=t.chat;try{if(yield s.default.findOne({chat_id:n})){const t="👋 Select a blockchain to snipe!\n\n";yield e.sendMessage(n,t,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"scan_snipe_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"scan_snipe_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"scan_snipe_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"scan_snipe_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}}catch(t){console.error("Error handling wallet selection:",t),yield e.sendMessage(n,"An error occurred while processing your request. Please try again later.")}}))},8786:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.baseSniperScreen=void 0;const s=a(n(8087)),o=n(8904),i=n(3433),d=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_base"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"base_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"base_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c={},l=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(c[t]){const n=c[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return c[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${a}:`,e.text),c[t]=Object.assign(Object.assign({},c[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),u={};t.baseSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const a=yield(0,o.getLiquidityInfoFromDexscreener)(n),{symbol:c,name:p,chainId:f,dexId:m,pairAddress:_}=a,v=`\n🏷 **Token Information:**\n📍 **Symbol:** ${c}\n📛 **Name:** ${p}\n🔗 **Chain ID:** ${f}\n🏦 **Dex ID:** ${m}\n🔗 **Pair Address:** ${_}\n    `,b={parse_mode:"Markdown",reply_markup:d()};if(a.hasLiquidity){const n=yield e.sendMessage(t,v,b);u[t]=Object.assign(Object.assign({},u[t]),{initialMessageId:n.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}`,b),yield y(t,n,!1);u[t]=u[t]||{},u[t][n]={isConfigured:!0},e.on("callback_query",(o=>r(void 0,void 0,void 0,(function*(){var r,c,y,p;const f=null===(r=o.message)||void 0===r?void 0:r.message_id,m=(null===(c=o.message)||void 0===c||c.text,(null===(y=o.message)||void 0===y?void 0:y.reply_markup)||{}),_=JSON.parse(o.data||"{}").command;if("base_snipe_config"===_){u[t]={contractAddress:n};const r={inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_base"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_base"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_base"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_base"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_base"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_base"})}]]};JSON.stringify(r)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:f}))}else if("back_to_main_snipe_base"===_){const n=d();JSON.stringify(n)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(n,{chat_id:t,message_id:f}))}else if("base_snipe_execute"===_)a.hasLiquidity?yield e.answerCallbackQuery(o.id,{text:"🚫 Token has already been launched.",show_alert:!0}):(yield s.default.findOne({chatId:t,contractAddress:n,blockchain:"base"}))?((0,i.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Executing snipe...")):yield e.sendMessage(t,"❌ Snipe configuration not found.");else if("review_config_base"===_){const r=null===(p=u[t])||void 0===p?void 0:p.initialMessageId;r&&(yield h(e,t,r,n))}else if("set_slippage_base"===_){const r=yield l(e,t,"Enter new slippage percentage:","slippage");yield g(t,n,{slippage_base:parseInt(r,10)}),yield e.sendMessage(t,`Slippage set to ${r}%`)}else if("set_spend_base"===_){const r=yield l(e,t,"Enter new spend amount:","spend");yield g(t,n,{spend_base:parseFloat(r)}),yield e.sendMessage(t,`Spend amount set to ${r}`)}else if("set_time_delay_base"===_){const r=yield l(e,t,"Enter new time delay (in milliseconds):","time delay");yield g(t,n,{timeDelay_base:parseInt(r,10)}),yield e.sendMessage(t,`Time delay set to ${r} ms`)}else if("set_block_delay_base"===_){const r=yield l(e,t,"Enter new block delay:","block delay");yield g(t,n,{blockDelay_base:parseInt(r,10)}),yield e.sendMessage(t,`Block delay set to ${r}`)}else if("set_retries_base"===_){const r=yield l(e,t,"Enter new retry count:","retries");yield g(t,n,{retries_base:parseInt(r,10)}),yield e.sendMessage(t,`Retries set to ${r}`)}}))))}catch(n){console.error("Error in baseSniperScreen:",n),yield e.sendMessage(t,"❌ An error occurred during the operation. Please try again.")}}));const g=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"base"});r&&(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),console.log("Updated snipe config:",r))}catch(e){console.error("Error updating snipe config:",e)}})),y=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"base"}))return void console.log("BASE snipe config already exists for this contract address.");const r=new s.default({chatId:e,contractAddress:t,blockchain:"base",isBought:n});yield r.save(),console.log("BASE snipe config saved successfully.")}catch(e){console.error("Error saving BASE snipe config:",e)}})),h=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chatId:t,contractAddress:a,blockchain:"base"});if(r){const s=r.config,o=`\n📊 **Snipe Configuration:**\n*Contract:* ${a}\n*Slippage:* ${s.slippage_base||"N/A"}%\n*Spend:* ${s.spend_base||"N/A"}\n*Time Delay:* ${s.timeDelay_base||"N/A"} ms\n*Block Delay:* ${s.blockDelay_base||"N/A"}\n*Retries:* ${s.retries_base||"N/A"}\n      `;yield e.editMessageText(o,{chat_id:t,message_id:n,parse_mode:"Markdown"})}else yield e.sendMessage(t,"❌ Snipe configuration not found.")}catch(n){console.error("Error in reviewMessage:",n),yield e.sendMessage(t,"❌ An error occurred while reviewing the snipe configuration.")}}))},2893:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.bscSniperScreen=void 0;const s=a(n(8087)),o=n(9558),i=n(871),d=n(1213),c=n(3433),l=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_bsc"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"bsc_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"bsc_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),u=()=>({inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_bsc"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_bsc"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_bsc"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_bsc"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_bsc"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_bsc"})}]]}),g={},y=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(g[t]){const n=g[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return g[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${a}:`,e.text),g[t]=Object.assign(Object.assign({},g[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),h={};t.bscSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){var a;try{const g=yield(0,o.getTokenInfoFromDexscreener)(n,i.BlockchainType.BSC),_=i.TraderFactory.getTrader(i.BlockchainType.BSC),v=yield _.checkHoneypot(n),{symbol:b="Unknown",name:k="Unknown",priceUsd:w="Unknown",liquidity:S="Unknown",pairAddress:T="Unknown"}=g||{};let A=`\n🏷 **Token Information:**\n📍 **Symbol:** ${b}\n📛 **Name:** ${k}\n💵 **Price:** $${w}\n💧 **Liquidity:** $${"object"==typeof S&&null!==S?S.usd:"Unknown"}\n🔗 **Pair Address:** ${T}\n    `;v.isHoneypot&&(A+=`\n⚠️ **HONEYPOT WARNING** ⚠️\nThis token appears to be a honeypot. Sniping is not recommended.\nReason: ${v.honeypotReason||"Unknown"}\n      `);const O={parse_mode:"Markdown",reply_markup:l()},E=(null===(a=null==g?void 0:g.liquidity)||void 0===a?void 0:a.usd)>0;if(E){const r=yield e.sendMessage(t,A,O);h[t]=h[t]||{},h[t].bsc=h[t].bsc||{},h[t].bsc[n]={isConfigured:!0,initialMessageId:r.message_id},d.logger.info("BSC token with liquidity displayed",{chatId:t,contractAddress:n,hasLiquidity:!0,messageId:r.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}. Ready to snipe when liquidity is added.`,O),yield f(t,n,!1),d.logger.info("BSC snipe configuration saved",{chatId:t,contractAddress:n,hasLiquidity:!1});h[t]=h[t]||{},h[t].bsc=h[t].bsc||{},h[t].bsc[n]=h[t].bsc[n]||{isConfigured:!0};const x=a=>r(void 0,void 0,void 0,(function*(){var r,o,i,g,f,_,v,b,k,w,S;try{const T=null===(r=a.message)||void 0===r?void 0:r.message_id;if(!T)return;if((null===(o=a.message)||void 0===o?void 0:o.chat.id)!==t)return;if((null===(f=null===(g=null===(i=h[t])||void 0===i?void 0:i.bsc)||void 0===g?void 0:g[n])||void 0===f?void 0:f.initialMessageId)!==T&&!(null===(v=null===(_=a.message)||void 0===_?void 0:_.text)||void 0===v?void 0:v.includes(n)))return;const A=(null===(b=a.message)||void 0===b?void 0:b.reply_markup)||{},O=JSON.parse(a.data||"{}").command;switch(yield e.answerCallbackQuery(a.id),O){case"bsc_snipe_config":h[t]=h[t]||{},h[t].bsc=h[t].bsc||{},h[t].bsc[n]=h[t].bsc[n]||{},h[t].bsc[n].configMode=!0;const r=u();JSON.stringify(r)!==JSON.stringify(A)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:T}));break;case"back_to_main_snipe_bsc":const o=l();JSON.stringify(o)!==JSON.stringify(A)&&(yield e.editMessageReplyMarkup(o,{chat_id:t,message_id:T}));break;case"bsc_snipe_execute":if(E)yield e.answerCallbackQuery(a.id,{text:"🚫 Token has already been launched.",show_alert:!0});else{const r=yield s.default.findOne({chatId:t,contractAddress:n,blockchain:"bsc"});r?((0,c.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Snipe configured and ready! Monitoring for liquidity...\n\nThe bot will automatically execute the snipe when liquidity is detected."),d.logger.info("BSC snipe execution started",{chatId:t,contractAddress:n,snipeId:r._id})):yield e.sendMessage(t,"❌ Snipe configuration not found. Please configure the snipe first.")}break;case"review_config_bsc":const i=null===(S=null===(w=null===(k=h[t])||void 0===k?void 0:k.bsc)||void 0===w?void 0:w[n])||void 0===S?void 0:S.initialMessageId;i&&(yield m(e,t,i,n));break;case"set_slippage_bsc":const g=yield y(e,t,"Enter new slippage percentage:","slippage");yield p(t,n,{slippage_bsc:parseInt(g,10)}),yield e.sendMessage(t,`✅ Slippage set to ${g}%`);break;case"set_spend_bsc":const f=yield y(e,t,"Enter new spend amount:","spend");yield p(t,n,{spend_bsc:parseFloat(f)}),yield e.sendMessage(t,`✅ Spend amount set to ${f} BSC`);break;case"set_time_delay_bsc":const _=yield y(e,t,"Enter new time delay (in milliseconds):","time delay");yield p(t,n,{timeDelay_bsc:parseInt(_,10)}),yield e.sendMessage(t,`✅ Time delay set to ${_} ms`);break;case"set_block_delay_bsc":const v=yield y(e,t,"Enter new block delay:","block delay");yield p(t,n,{blockDelay_bsc:parseInt(v,10)}),yield e.sendMessage(t,`✅ Block delay set to ${v}`);break;case"set_retries_bsc":const b=yield y(e,t,"Enter new retry count:","retries");yield p(t,n,{retries_bsc:parseInt(b,10)}),yield e.sendMessage(t,`✅ Retries set to ${b}`);break;case"dismiss_message":try{yield e.deleteMessage(t,T),e.removeListener("callback_query",x),d.logger.debug("BSC sniper message dismissed",{chatId:t,messageId:T})}catch(e){d.logger.error("Failed to delete BSC sniper message",{chatId:t,messageId:T,error:e.message})}}}catch(r){d.logger.error("Error handling BSC sniper callback",{chatId:t,contractAddress:n,error:r.message}),yield e.sendMessage(t,`⚠️ An error occurred: ${r.message}`)}}));e.on("callback_query",x)}catch(r){d.logger.error("Error in bscSniperScreen",{chatId:t,contractAddress:n,error:r.message}),yield e.sendMessage(t,`❌ An error occurred: ${r.message}`)}}));const p=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});r?(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),d.logger.info("Updated BSC snipe config",{chatId:e,contractAddress:t,updates:Object.keys(n),snipeId:r._id})):d.logger.warn("Snipe configuration not found for update",{chatId:e,contractAddress:t,blockchain:"bsc"})}catch(n){d.logger.error("Error updating snipe config",{chatId:e,contractAddress:t,error:n.message})}})),f=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});if(r)return void d.logger.info("BSC snipe config already exists",{chatId:e,contractAddress:t,snipeId:r._id});const a=new s.default({chatId:e,contractAddress:t,blockchain:"bsc",hasLiquidity:n,config:{isConfigured:!0,isBought:!1,isExecuted_bsc:!1,slippage_bsc:5,spend_bsc:.1,timeDelay_bsc:0,blockDelay_bsc:0,retries_bsc:3}});yield a.save(),d.logger.info("New BSC snipe config saved",{chatId:e,contractAddress:t,snipeId:a._id})}catch(n){d.logger.error("Error saving BSC snipe config",{chatId:e,contractAddress:t,error:n.message})}})),m=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){var r;try{const c=yield s.default.findOne({chatId:t,contractAddress:a,blockchain:"bsc"});if(c){const s=yield(0,o.getTokenInfoFromDexscreener)(a,i.BlockchainType.BSC),l=c.config,g=`\n📊 **BSC Snipe Configuration:**\n📝 **Contract:** ${a}\n🚀 **Token:** ${(null==s?void 0:s.name)||"Unknown"} (${(null==s?void 0:s.symbol)||"Unknown"})\n💵 **Current Price:** $${(null==s?void 0:s.priceUsd)||"Unknown"}\n💧 **Liquidity:** $${(null===(r=null==s?void 0:s.liquidity)||void 0===r?void 0:r.usd)||"Unknown"}\n\n⚙️ **Snipe Settings:**\n📈 **Slippage:** ${l.slippage_bsc||"5"}%\n💰 **Spend Amount:** ${l.spend_bsc||"0.1"} BSC\n⏱️ **Time Delay:** ${l.timeDelay_bsc||"0"} ms\n🧱 **Block Delay:** ${l.blockDelay_bsc||"0"} blocks\n🔄 **Retries:** ${l.retries_bsc||"3"}\n🚀 **Ready to Snipe:** ${c.hasLiquidity?"No (Liquidity already added)":"Yes"}\n      `;yield e.editMessageText(g,{chat_id:t,message_id:n,parse_mode:"Markdown",reply_markup:u()}),d.logger.debug("BSC snipe configuration reviewed",{chatId:t,contractAddress:a,snipeId:c._id})}else yield e.sendMessage(t,"❌ Snipe configuration not found. Please set up a snipe first.")}catch(r){d.logger.error("Error in reviewMessage",{chatId:t,contractAddress:a,messageId:n,error:r.message}),yield e.sendMessage(t,`❌ An error occurred: ${r.message}`)}}))},4248:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.ethSniperScreen=void 0;const s=a(n(8087)),o=n(8904),i=n(3433),d=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_eth"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"eth_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"eth_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c={},l=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(c[t]){const n=c[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return c[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${a}:`,e.text),c[t]=Object.assign(Object.assign({},c[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),u={};t.ethSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const a=yield(0,o.getLiquidityInfoFromDexscreener)(n),{symbol:c,name:p,chainId:f,dexId:m,pairAddress:_}=a,v=`\n🏷 **Token Information:**\n📍 **Symbol:** ${c}\n📛 **Name:** ${p}\n🔗 **Chain ID:** ${f}\n🏦 **Dex ID:** ${m}\n🔗 **Pair Address:** ${_}\n    `,b={parse_mode:"Markdown",reply_markup:d()};if(a.hasLiquidity){const n=yield e.sendMessage(t,v,b);u[t]=Object.assign(Object.assign({},u[t]),{initialMessageId:n.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}`,b),yield y(t,n,!1);u[t]=u[t]||{},u[t][n]={isConfigured:!0},e.on("callback_query",(o=>r(void 0,void 0,void 0,(function*(){var r,c,y,p;const f=null===(r=o.message)||void 0===r?void 0:r.message_id,m=(null===(c=o.message)||void 0===c||c.text,(null===(y=o.message)||void 0===y?void 0:y.reply_markup)||{}),_=JSON.parse(o.data||"{}").command;if("eth_snipe_config"===_){u[t]={contractAddress:n};const r={inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_eth"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_eth"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_eth"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_eth"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_eth"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_eth"})}]]};JSON.stringify(r)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:f}))}else if("back_to_main_snipe_eth"===_){const n=d();JSON.stringify(n)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(n,{chat_id:t,message_id:f}))}else if("eth_snipe_execute"===_)a.hasLiquidity?yield e.answerCallbackQuery(o.id,{text:"🚫 Token has already been launched.",show_alert:!0}):(yield s.default.findOne({chatId:t,contractAddress:n,blockchain:"eth"}))?((0,i.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Executing snipe...")):yield e.sendMessage(t,"❌ Snipe configuration not found.");else if("review_config_eth"===_){const r=null===(p=u[t])||void 0===p?void 0:p.initialMessageId;r&&(yield h(e,t,r,n))}else if("set_slippage_eth"===_){const r=yield l(e,t,"Enter new slippage percentage:","slippage");yield g(t,n,{slippage_eth:parseInt(r,10)}),yield e.sendMessage(t,`Slippage set to ${r}%`)}else if("set_spend_eth"===_){const r=yield l(e,t,"Enter new spend amount:","spend");yield g(t,n,{spend_eth:parseFloat(r)}),yield e.sendMessage(t,`Spend amount set to ${r}`)}else if("set_time_delay_eth"===_){const r=yield l(e,t,"Enter new time delay (in milliseconds):","time delay");yield g(t,n,{timeDelay_eth:parseInt(r,10)}),yield e.sendMessage(t,`Time delay set to ${r} ms`)}else if("set_block_delay_eth"===_){const r=yield l(e,t,"Enter new block delay:","block delay");yield g(t,n,{blockDelay_eth:parseInt(r,10)}),yield e.sendMessage(t,`Block delay set to ${r}`)}else if("set_retries_eth"===_){const r=yield l(e,t,"Enter new retry count:","retries");yield g(t,n,{retries_eth:parseInt(r,10)}),yield e.sendMessage(t,`Retries set to ${r}`)}}))))}catch(n){console.error("Error in ethSniperScreen:",n),yield e.sendMessage(t,"❌ An error occurred during the operation. Please try again.")}}));const g=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"});r&&(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),console.log("Updated snipe config:",r))}catch(e){console.error("Error updating snipe config:",e)}})),y=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"}))return void console.log("ETH snipe config already exists for this contract address.");const r=new s.default({chatId:e,contractAddress:t,blockchain:"eth",isBought:n});yield r.save(),console.log("ETH snipe config saved successfully.")}catch(e){console.error("Error saving ETH snipe config:",e)}})),h=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chatId:t,contractAddress:a,blockchain:"eth"});if(r){const s=r.config,o=`\n📊 **Snipe Configuration:**\n*Contract:* ${a}\n*Slippage:* ${s.slippage_eth||"N/A"}%\n*Spend:* ${s.spend_eth||"N/A"}\n*Time Delay:* ${s.timeDelay_eth||"N/A"} ms\n*Block Delay:* ${s.blockDelay_eth||"N/A"}\n*Retries:* ${s.retries_eth||"N/A"}\n      `;yield e.editMessageText(o,{chat_id:t,message_id:n,parse_mode:"Markdown"})}else yield e.sendMessage(t,"❌ Snipe configuration not found.")}catch(n){console.error("Error in reviewMessage:",n),yield e.sendMessage(t,"❌ An error occurred while reviewing the snipe configuration.")}}))},4259:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.solSniperScreen=void 0;const s=a(n(8087)),o=n(8904),i=n(3433),d=()=>({inline_keyboard:[[{text:"📊 Scan Another Contract",callback_data:JSON.stringify({command:"scan_snipe_sol"})},{text:"Execute Snipe",callback_data:JSON.stringify({command:"sol_snipe_execute"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"sol_snipe_config"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),c={},l=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(c[t]){const n=c[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return c[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log(`Received ${a}:`,e.text),c[t]=Object.assign(Object.assign({},c[t]),{replyId:e.message_id}),n(e.text)):o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),u={};t.solSniperScreen=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const a=yield(0,o.getLiquidityInfoFromDexscreener)(n),{symbol:c,name:p,chainId:f,dexId:m,pairAddress:_}=a,v=`\n🏷 **Token Information:**\n📍 **Symbol:** ${c}\n📛 **Name:** ${p}\n🔗 **Chain ID:** ${f}\n🏦 **Dex ID:** ${m}\n🔗 **Pair Address:** ${_}\n    `,b={parse_mode:"Markdown",reply_markup:d()};if(a.hasLiquidity){const n=yield e.sendMessage(t,v,b);u[t]=Object.assign(Object.assign({},u[t]),{initialMessageId:n.message_id})}else yield e.sendMessage(t,`⚠️ No liquidity found for the token ${n}`,b),yield y(t,n,!1);u[t]=u[t]||{},u[t][n]={isConfigured:!0},e.on("callback_query",(o=>r(void 0,void 0,void 0,(function*(){var r,c,y,p;const f=null===(r=o.message)||void 0===r?void 0:r.message_id,m=(null===(c=o.message)||void 0===c||c.text,(null===(y=o.message)||void 0===y?void 0:y.reply_markup)||{}),_=JSON.parse(o.data||"{}").command;if("sol_snipe_config"===_){u[t]={contractAddress:n};const r={inline_keyboard:[[{text:"Slippage",callback_data:JSON.stringify({command:"set_slippage_sol"})},{text:"Spend",callback_data:JSON.stringify({command:"set_spend_sol"})}],[{text:"Time Delay",callback_data:JSON.stringify({command:"set_time_delay_sol"})},{text:"Block Delay",callback_data:JSON.stringify({command:"set_block_delay_sol"})}],[{text:"Retries on Fail",callback_data:JSON.stringify({command:"set_retries_sol"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_snipe_sol"})}]]};JSON.stringify(r)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(r,{chat_id:t,message_id:f}))}else if("back_to_main_snipe_sol"===_){const n=d();JSON.stringify(n)!==JSON.stringify(m)&&(yield e.editMessageReplyMarkup(n,{chat_id:t,message_id:f}))}else if("sol_snipe_execute"===_)a.hasLiquidity?yield e.answerCallbackQuery(o.id,{text:"🚫 Token has already been launched.",show_alert:!0}):(yield s.default.findOne({chatId:t,contractAddress:n,blockchain:"sol"}))?((0,i.startPeriodicSnipeUpdates)(e),yield e.sendMessage(t,"🔫 Executing snipe...")):yield e.sendMessage(t,"❌ Snipe configuration not found.");else if("review_config_sol"===_){const r=null===(p=u[t])||void 0===p?void 0:p.initialMessageId;r&&(yield h(e,t,r,n))}else if("set_slippage_sol"===_){const r=yield l(e,t,"Enter new slippage percentage:","slippage");yield g(t,n,{slippage_sol:parseInt(r,10)}),yield e.sendMessage(t,`Slippage set to ${r}%`)}else if("set_spend_sol"===_){const r=yield l(e,t,"Enter new spend amount:","spend");yield g(t,n,{spend_sol:parseFloat(r)}),yield e.sendMessage(t,`Spend amount set to ${r}`)}else if("set_time_delay_sol"===_){const r=yield l(e,t,"Enter new time delay (in milliseconds):","time delay");yield g(t,n,{timeDelay_sol:parseInt(r,10)}),yield e.sendMessage(t,`Time delay set to ${r} ms`)}else if("set_block_delay_sol"===_){const r=yield l(e,t,"Enter new block delay:","block delay");yield g(t,n,{blockDelay_sol:parseInt(r,10)}),yield e.sendMessage(t,`Block delay set to ${r}`)}else if("set_retries_sol"===_){const r=yield l(e,t,"Enter new retry count:","retries");yield g(t,n,{retries_sol:parseInt(r,10)}),yield e.sendMessage(t,`Retries set to ${r}`)}}))))}catch(n){console.error("Error in solSniperScreen:",n),yield e.sendMessage(t,"❌ An error occurred during the operation. Please try again.")}}));const g=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{let r=yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"});r&&(n.isConfigured=!0,r.config=Object.assign(Object.assign({},r.config),n),yield r.save(),console.log("Updated snipe config:",r))}catch(e){console.error("Error updating snipe config:",e)}})),y=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(yield s.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"}))return void console.log("SOL snipe config already exists for this contract address.");const r=new s.default({chatId:e,contractAddress:t,blockchain:"sol",isBought:n});yield r.save(),console.log("SOL snipe config saved successfully.")}catch(e){console.error("Error saving SOL snipe config:",e)}})),h=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chatId:t,contractAddress:a,blockchain:"sol"});if(r){const s=r.config,o=`\n📊 **Snipe Configuration:**\n*Contract:* ${a}\n*Slippage:* ${s.slippage_sol||"N/A"}%\n*Spend:* ${s.spend_sol||"N/A"}\n*Time Delay:* ${s.timeDelay_sol||"N/A"} ms\n*Block Delay:* ${s.blockDelay_sol||"N/A"}\n*Retries:* ${s.retries_sol||"N/A"}\n      `;yield e.editMessageText(o,{chat_id:t,message_id:n,parse_mode:"Markdown"})}else yield e.sendMessage(t,"❌ Snipe configuration not found.")}catch(n){console.error("Error in reviewMessage:",n),yield e.sendMessage(t,"❌ An error occurred while reviewing the snipe configuration.")}}))},9266:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeBaseScreen=t.reviewBaseTrade=t.monitorBasePriceAndSell=t.executeBaseTrade=t.updateBaseTradeConfig=t.createBaseTradeKeyboard=t.askForBaseTradeInput=void 0,t.getbaseTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){const n=new s.ethers.providers.JsonRpcProvider(y),r=new s.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[a,o]=yield Promise.all([r.balanceOf(t),r.decimals()]);return s.ethers.utils.formatUnits(a,o)}))};const s=n(9321),o=a(n(4103)),i=n(9558),d=n(871),c=n(1213),l=n(4103),u=n(6786),g=a(n(8722)),y=process.env.BASE_PROVIDER_URL;if(!y)throw new Error("BASE_PROVIDER_URL environment variable is not set");const h={};t.askForBaseTradeInput=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(h[t]){const n=h[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return h[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(s=>r(void 0,void 0,void 0,(function*(){if(s.text){const r=parseFloat(s.text);h[t]=Object.assign(Object.assign({},h[t]),{replyId:s.message_id});const o=`✅ ${a.charAt(0).toUpperCase()+a.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);h[t]=Object.assign(Object.assign({},h[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),t.createBaseTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_base"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_base"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_base"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_base"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_base"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_base"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateBaseTradeConfig=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:e,contractAddress:t,blockchain:"base"});r&&(r.config[n]=a,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeBaseTrade=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:n,contractAddress:a,blockchain:"base"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const s=d.TraderFactory.getTrader(d.BlockchainType.BASE),i=r.config.spendAmount.toString(),l=yield s.buyTokens(a,i,"",{slippage:5,gasLimit:1e6});if(!l.success)return c.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:a,error:l.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${l.error||"Unknown error"}`));yield o.default.findOneAndUpdate({chatId:n,contractAddress:a,blockchain:"base"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${l.transactionHash}`),yield(0,t.monitorBasePriceAndSell)(e,n,r),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),c.logger.info("BASE trade executed successfully",{chatId:n,contractAddress:a,transactionHash:l.transactionHash})}catch(t){c.logger.error("Error executing BASE trade",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const p={};t.monitorBasePriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const a=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;c.logger.info("Starting BASE price monitoring",{chatId:t,contractAddress:n.contractAddress,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const s=setInterval((()=>r(void 0,void 0,void 0,(function*(){var r;try{const o=yield(0,i.getTokenInfoFromDexscreener)(n.contractAddress,d.BlockchainType.BASE);if(!o||void 0===o.priceChange)return void c.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress});const y=parseFloat((null===(r=o.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(y))return void c.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:o.priceChange});if(c.logger.debug(`Price Change: ${y}%`,{contractAddress:n.contractAddress,priceChange:y}),y>=n.config.takeProfit||y<=n.config.stopLoss){c.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:y,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),clearInterval(s),delete p[a];const r=yield g.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:o}=r.base_wallet,i=d.TraderFactory.getTrader(d.BlockchainType.BASE),h=yield i.getTokenBalance(n.contractAddress,o),f=parseFloat(h);if(isNaN(f)||f<=0)return void c.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:h});c.logger.info(`Token Balance: ${f}`,{contractAddress:n.contractAddress,userAddress:o});let m=!1,_=0,v="";for(;_<2&&!m;)try{_++,c.logger.info(`Selling tokens, attempt ${_}`,{contractAddress:n.contractAddress,tokenBalance:f});const t=yield i.sellTokens(n.contractAddress,parseFloat(h),o,{slippage:5,gasLimit:1e6});if(t.success)return m=!0,v=t.transactionHash||"",yield(0,l.updateAutoTradeAsSold)(r._id,n.contractAddress,f,"base"),yield(0,u.updateTradeAsSold)(r._id,n.contractAddress,f,"base"),c.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:v}),void(e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${v}\n💰 Amount Sold: ${f} tokens\n📊 Price Change: ${y}%`)));c.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){c.logger.error(`Attempt ${_} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return void(m||(c.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${_} attempts.\nPlease try to sell manually.`))))}c.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:y,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss})}catch(e){c.logger.error("Error in monitorBasePriceAndSell",{contractAddress:n.contractAddress,error:e.message})}}))),15e3);p[a]=s})),t.reviewBaseTrade=(e,n,a,s)=>r(void 0,void 0,void 0,(function*(){var r;try{const l=yield o.default.findOne({chatId:n,contractAddress:s,blockchain:"base"});if(!l)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,i.getTokenInfoFromDexscreener)(s,d.BlockchainType.BASE),g=(null==u?void 0:u.priceUsd)||"Unknown",y=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",h=l.isSold?"Yes":"No",p="ongoing"===l.isInitialized?"Active":"pending"===l.isInitialized?"Pending":"Completed",f=`💼 **BASE Trade Details:**\n📝 **Contract:** ${s}\n🚀 **Token:** ${l.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${y}%\n📈 **Take Profit:** ${l.config.takeProfit}%\n📉 **Stop Loss:** ${l.config.stopLoss}%\n💰 **Spend Amount:** ${l.config.spendAmount} BASE\n🔄 **Is Sold:** ${h}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:(0,t.createBaseTradeKeyboard)()}),c.logger.debug("BASE trade review displayed",{chatId:n,contractAddress:s,messageId:a})}catch(t){c.logger.error("Error in reviewBaseTrade",{chatId:n,contractAddress:s,messageId:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeBaseScreen=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const s=yield(0,i.getTokenInfoFromDexscreener)(a,d.BlockchainType.BASE);if(!s)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:l,name:u}=s,g=d.TraderFactory.getTrader(d.BlockchainType.BASE),y=yield g.checkHoneypot(a),h=new o.default({chatId:n,blockchain:"base",contractAddress:a,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield h.save();let p="💼 **BASE Trading**\n";p+=`📝 **Contract:** ${a}\n`,p+=`🚀 **Token:** ${u} (${l})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/base/${a})\n\n`,y.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${y.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createBaseTradeKeyboard)()});c.logger.info("BASE trade screen displayed",{chatId:n,contractAddress:a,messageId:f.message_id,isHoneypot:y.isHoneypot});const m=s=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=s.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(s.data).command;switch(yield e.answerCallbackQuery(s.id),r){case"snipe_set_take_profit_base":const r=yield(0,t.askForBaseTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateBaseTradeConfig)(n,a,"takeProfit",r);break;case"snipe_set_stop_loss_base":const o=yield(0,t.askForBaseTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateBaseTradeConfig)(n,a,"stopLoss",o);break;case"snipe_set_spend_amount_base":const i=yield(0,t.askForBaseTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateBaseTradeConfig)(n,a,"spendAmount",i);break;case"snipetrade_buy_base":yield(0,t.executeBaseTrade)(e,n,a);break;case"snipe_review_trade_base":yield(0,t.reviewBaseTrade)(e,n,s.message.message_id,a);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){c.logger.error("Error handling BASE trade callback",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){c.logger.error("Error in tradeBaseScreen",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the BASE trading screen: ${t.message}`)}}))},8485:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeBscScreen=t.reviewBscTrade=t.monitorBscPriceAndSell=t.executeBscTrade=t.updateBscTradeConfig=t.createBscTradeKeyboard=t.askForBscTradeInput=void 0,t.getBscTokenBalance=_;const s=n(9321),o=a(n(818)),i=a(n(4103)),d=n(9558),c=n(871),l=n(1213),u=n(4103),g=n(6786),y=a(n(8722));o.default.config();const h=process.env.EVM_ADMIN_ADDRESS||"0x25AA04655aefbC523412eD5a67d36968127499f5",p=process.env.BSC_PROVIDER_URL||"https://bsc-dataseed.binance.org/";if(!h)throw new Error("ADMIN_ADDRESS environment variable is not set");if(!p)throw new Error("Provider environment variable is not set");const f={};t.askForBscTradeInput=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(f[t]){const n=f[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return f[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(s=>r(void 0,void 0,void 0,(function*(){if(s.text){const r=parseFloat(s.text);f[t]=Object.assign(Object.assign({},f[t]),{replyId:s.message_id});const o=`✅ ${a.charAt(0).toUpperCase()+a.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);f[t]=Object.assign(Object.assign({},f[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),t.createBscTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_bsc"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_bsc"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_bsc"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_bsc"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_bsc"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_bsc"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateBscTradeConfig=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield i.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});r&&(r.config[n]=a,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeBscTrade=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield i.default.findOne({chatId:n,contractAddress:a,blockchain:"bsc"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const s=c.TraderFactory.getTrader(c.BlockchainType.BSC),o=r.config.spendAmount.toString(),d=yield s.buyTokens(a,o,"",{slippage:5,gasLimit:1e6});if(!d.success)return l.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:a,error:d.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${d.error||"Unknown error"}`));yield i.default.findOneAndUpdate({chatId:n,contractAddress:a,blockchain:"bsc"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${d.transactionHash}`),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),(0,t.monitorBscPriceAndSell)(e,n,r),l.logger.info("BSC trade executed successfully",{chatId:n,contractAddress:a,transactionHash:d.transactionHash})}catch(t){l.logger.error("Error executing BSC trade",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const m={};function _(e,t){return r(this,void 0,void 0,(function*(){const n=new s.ethers.providers.JsonRpcProvider(p),r=new s.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[a,o]=yield Promise.all([r.balanceOf(t),r.decimals()]);return s.ethers.utils.formatUnits(a,o)}))}t.monitorBscPriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const a=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;l.logger.info("Starting BSC price monitoring",{chatId:t,contractAddress:n.contractAddress,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const s=setInterval((()=>r(void 0,void 0,void 0,(function*(){var r;try{const o=yield(0,d.getTokenInfoFromDexscreener)(n.contractAddress,c.BlockchainType.BSC);if(!o||void 0===o.priceChange)return void l.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress});const i=parseFloat((null===(r=o.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(i))return void l.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:o.priceChange});if(l.logger.debug(`Price Change: ${i}%`,{contractAddress:n.contractAddress,priceChange:i}),i>=n.config.takeProfit||i<=n.config.stopLoss){l.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:i,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),clearInterval(s),delete m[a];const r=yield y.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:o}=r.bsc_wallet,d=yield _(n.contractAddress,o),h=parseFloat(d);if(isNaN(h)||h<=0)return void l.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:d});l.logger.info(`Token Balance: ${h}`,{contractAddress:n.contractAddress,userAddress:o});const p=c.TraderFactory.getTrader(c.BlockchainType.BSC);let f=!1,v=0,b="";for(;v<2&&!f;)try{v++,l.logger.info(`Selling tokens, attempt ${v}`,{contractAddress:n.contractAddress,tokenBalance:h});const t=yield p.sellTokens(n.contractAddress,parseFloat(d),"",{slippage:5,gasLimit:1e6});if(t.success)return f=!0,b=t.transactionHash||"",yield(0,u.updateAutoTradeAsSold)(r._id,n.contractAddress,h,"bsc"),yield(0,g.updateTradeAsSold)(r._id,n.contractAddress,h,"bsc"),l.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:b}),void(e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${b}\n💰 Amount Sold: ${h} tokens\n📊 Price Change: ${i}%`)));l.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){l.logger.error(`Attempt ${v} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return void(f||(l.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${v} attempts.\nPlease try to sell manually.`))))}l.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:i,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss})}catch(e){l.logger.error("Error in monitorBscPriceAndSell",{contractAddress:n.contractAddress,error:e.message})}}))),15e3);m[a]=s})),t.reviewBscTrade=(e,n,a,s)=>r(void 0,void 0,void 0,(function*(){var r;try{const o=yield i.default.findOne({chatId:n,contractAddress:s,blockchain:"bsc"});if(!o)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,d.getTokenInfoFromDexscreener)(s,c.BlockchainType.BSC),g=(null==u?void 0:u.priceUsd)||"Unknown",y=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",h=o.isSold?"Yes":"No",p="ongoing"===o.isInitialized?"Active":"pending"===o.isInitialized?"Pending":"Completed",f=`💼 **BSC Trade Details:**\n📝 **Contract:** ${s}\n🚀 **Token:** ${o.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${y}%\n📈 **Take Profit:** ${o.config.takeProfit}%\n📉 **Stop Loss:** ${o.config.stopLoss}%\n💰 **Spend Amount:** ${o.config.spendAmount} BSC\n🔄 **Is Sold:** ${h}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:(0,t.createBscTradeKeyboard)()}),l.logger.debug("BSC trade review displayed",{chatId:n,contractAddress:s,messageId:a})}catch(t){l.logger.error("Error in reviewBscTrade",{chatId:n,contractAddress:s,messageId:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeBscScreen=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const s=yield(0,d.getTokenInfoFromDexscreener)(a,c.BlockchainType.BSC);if(!s)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:o,name:u}=s,g=c.TraderFactory.getTrader(c.BlockchainType.BSC),y=yield g.checkHoneypot(a),h=new i.default({chatId:n,blockchain:"bsc",contractAddress:a,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield h.save();let p="💼 **BSC Trading**\n";p+=`📝 **Contract:** ${a}\n`,p+=`🚀 **Token:** ${u} (${o})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/bsc/${a})\n\n`,y.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${y.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createBscTradeKeyboard)()});l.logger.info("BSC trade screen displayed",{chatId:n,contractAddress:a,messageId:f.message_id,isHoneypot:y.isHoneypot});const m=s=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=s.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(s.data).command;switch(yield e.answerCallbackQuery(s.id),r){case"snipe_set_take_profit_bsc":const r=yield(0,t.askForBscTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateBscTradeConfig)(n,a,"takeProfit",r);break;case"snipe_set_stop_loss_bsc":const o=yield(0,t.askForBscTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateBscTradeConfig)(n,a,"stopLoss",o);break;case"snipe_set_spend_amount_bsc":const i=yield(0,t.askForBscTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateBscTradeConfig)(n,a,"spendAmount",i);break;case"snipetrade_buy_bsc":yield(0,t.executeBscTrade)(e,n,a);break;case"snipe_review_trade_bsc":yield(0,t.reviewBscTrade)(e,n,s.message.message_id,a);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){l.logger.error("Error handling BSC trade callback",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){l.logger.error("Error in tradeBscScreen",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the BSC trading screen: ${t.message}`)}}))},2836:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeEthScreen=t.reviewEthTrade=t.monitorEthPriceAndSell=t.executeEthTrade=t.updateEthTradeConfig=t.createEthTradeKeyboard=t.askForEthTradeInput=void 0,t.getEthTokenBalance=m;const s=n(9321),o=a(n(4103)),i=n(9558),d=n(871),c=n(1213),l=n(4103),u=n(6786),g=a(n(8722)),y=process.env.EVM_ADMIN_ADDRESS||"",h=process.env.ETH_PROVIDER_URL;if(!y)throw new Error("ADMIN_ADDRESS environment variable is not set");if(!h)throw new Error("Provider environment variable is not set");const p={};t.askForEthTradeInput=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(p[t]){const n=p[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return p[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(s=>r(void 0,void 0,void 0,(function*(){if(s.text){const r=parseFloat(s.text);p[t]=Object.assign(Object.assign({},p[t]),{replyId:s.message_id});const o=`✅ ${a.charAt(0).toUpperCase()+a.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);p[t]=Object.assign(Object.assign({},p[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),t.createEthTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_eth"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_eth"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_eth"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_eth"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_eth"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_eth"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateEthTradeConfig=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"});r&&(r.config[n]=a,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeEthTrade=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:n,contractAddress:a,blockchain:"eth"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const s=d.TraderFactory.getTrader(d.BlockchainType.ETH),i=r.config.spendAmount.toString(),l=yield s.buyTokens(a,i,"",{slippage:5,gasLimit:1e6});if(!l.success)return c.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:a,error:l.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${l.error||"Unknown error"}`));yield o.default.findOneAndUpdate({chatId:n,contractAddress:a,blockchain:"eth"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${l.transactionHash}`),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),(0,t.monitorEthPriceAndSell)(e,n,r),c.logger.info("ETH trade executed successfully",{chatId:n,contractAddress:a,transactionHash:l.transactionHash})}catch(t){c.logger.error("Error executing ETH trade",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const f={};function m(e,t){return r(this,void 0,void 0,(function*(){const n=new s.ethers.providers.JsonRpcProvider(h),r=new s.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[a,o]=yield Promise.all([r.balanceOf(t),r.decimals()]);return s.ethers.utils.formatUnits(a,o)}))}t.monitorEthPriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){var r;try{const a=yield(0,i.getTokenInfoFromDexscreener)(n.contractAddress,d.BlockchainType.ETH);if(!a||void 0===a.priceChange)return c.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress}),!1;const s=parseFloat((null===(r=a.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(s))return c.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:a.priceChange}),!1;if(c.logger.debug(`Price Change: ${s}%`,{contractAddress:n.contractAddress,priceChange:s}),s>=n.config.takeProfit||s<=n.config.stopLoss){c.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:s,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const r=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;f[r]&&(f[r].stop(),delete f[r]);const a=yield g.default.findOne({chat_id:t}).exec();if(!a)throw new Error("User not found");const{address:o}=a.eth_wallet,i=yield m(n.contractAddress,o),y=parseFloat(i);if(isNaN(y)||y<=0)return c.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:i}),!1;c.logger.info(`Token Balance: ${y}`,{contractAddress:n.contractAddress,userAddress:o});const h=d.TraderFactory.getTrader(d.BlockchainType.ETH);let p=!1,_=0,v="";for(;_<2&&!p;)try{_++,c.logger.info(`Selling tokens, attempt ${_}`,{contractAddress:n.contractAddress,tokenBalance:y});const t=yield h.sellTokens(n.contractAddress,parseFloat(i),"",{slippage:5,gasLimit:1e6});if(t.success)return p=!0,v=t.transactionHash||"",yield(0,l.updateAutoTradeAsSold)(a._id,n.contractAddress,y,"eth"),yield(0,u.updateTradeAsSold)(a._id,n.contractAddress,y,"eth"),c.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:v}),e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${v}\n💰 Amount Sold: ${y} tokens\n📊 Price Change: ${s}%`)),!0;c.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){c.logger.error(`Attempt ${_} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return p||(c.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${_} attempts.\nPlease try to sell manually.`))),p}return c.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:s,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),!1}catch(e){return c.logger.error("Error in monitorEthPriceAndSell",{contractAddress:n.contractAddress,error:e.message}),!1}})),t.reviewEthTrade=(e,n,a,s)=>r(void 0,void 0,void 0,(function*(){var r;try{const l=yield o.default.findOne({chatId:n,contractAddress:s,blockchain:"eth"});if(!l)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,i.getTokenInfoFromDexscreener)(s,d.BlockchainType.ETH),g=(null==u?void 0:u.priceUsd)||"Unknown",y=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",h=l.isSold?"Yes":"No",p="ongoing"===l.isInitialized?"Active":"pending"===l.isInitialized?"Pending":"Completed",f=`💼 **ETH Trade Details:**\n📝 **Contract:** ${s}\n🚀 **Token:** ${l.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${y}%\n📈 **Take Profit:** ${l.config.takeProfit}%\n📉 **Stop Loss:** ${l.config.stopLoss}%\n💰 **Spend Amount:** ${l.config.spendAmount} ETH\n🔄 **Is Sold:** ${h}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:(0,t.createEthTradeKeyboard)()}),c.logger.debug("ETH trade review displayed",{chatId:n,contractAddress:s,messageId:a})}catch(t){c.logger.error("Error in reviewEthTrade",{chatId:n,contractAddress:s,messageId:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeEthScreen=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const s=yield(0,i.getTokenInfoFromDexscreener)(a,d.BlockchainType.ETH);if(!s)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:l,name:u}=s,g=d.TraderFactory.getTrader(d.BlockchainType.ETH),y=yield g.checkHoneypot(a),h=new o.default({chatId:n,blockchain:"eth",contractAddress:a,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield h.save();let p="💼 **ETH Trading**\n";p+=`📝 **Contract:** ${a}\n`,p+=`🚀 **Token:** ${u} (${l})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/ethereum/${a})\n\n`,y.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${y.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createEthTradeKeyboard)()});c.logger.info("ETH trade screen displayed",{chatId:n,contractAddress:a,messageId:f.message_id,isHoneypot:y.isHoneypot});const m=s=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=s.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(s.data).command;switch(yield e.answerCallbackQuery(s.id),r){case"snipe_set_take_profit_eth":const r=yield(0,t.askForEthTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateEthTradeConfig)(n,a,"takeProfit",r);break;case"snipe_set_stop_loss_eth":const o=yield(0,t.askForEthTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateEthTradeConfig)(n,a,"stopLoss",o);break;case"snipe_set_spend_amount_eth":const i=yield(0,t.askForEthTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateEthTradeConfig)(n,a,"spendAmount",i);break;case"snipetrade_buy_eth":yield(0,t.executeEthTrade)(e,n,a);break;case"snipe_review_trade_eth":yield(0,t.reviewEthTrade)(e,n,s.message.message_id,a);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){c.logger.error("Error handling ETH trade callback",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){c.logger.error("Error in tradeEthScreen",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the ETH trading screen: ${t.message}`)}}))},9527:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.tradeSolScreen=t.reviewSolTrade=t.monitorSolPriceAndSell=t.executeSolTrade=t.updateSolTradeConfig=t.createSolTradeKeyboard=t.askForSolTradeInput=void 0,t.getSolTokenBalance=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield e.getParsedTokenAccountsByOwner(new s.PublicKey(t),{mint:new s.PublicKey(n)});if(0===r.value.length)throw new Error("Token account not found");const a=r.value[0].account.data.parsed.info.tokenAmount.uiAmount;return console.log("Balance (using Solana-Web3.js): ",a),a}catch(e){throw console.error("Error fetching token balance:",e),e}}))};const s=n(8491),o=a(n(4103)),i=n(9558),d=n(871),c=n(1213),l=n(4103),u=n(6786),g=a(n(8722)),y=process.env.SOL_PROVIDER_URL;if(new s.Connection(y||"https://api.mainnet-beta.solana.com"),!y)throw new Error("Provider environment variable is not set");const h={};t.askForSolTradeInput=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(h[t]){const n=h[t];n.promptId&&(yield e.deleteMessage(t,n.promptId).catch(console.error)),n.replyId&&(yield e.deleteMessage(t,n.replyId).catch(console.error)),n.successId&&(yield e.deleteMessage(t,n.successId).catch(console.error))}const s=yield e.sendMessage(t,n,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return h[t]={promptId:s.message_id},new Promise(((n,o)=>{e.onReplyToMessage(t,s.message_id,(s=>r(void 0,void 0,void 0,(function*(){if(s.text){const r=parseFloat(s.text);h[t]=Object.assign(Object.assign({},h[t]),{replyId:s.message_id});const o=`✅ ${a.charAt(0).toUpperCase()+a.slice(1)} successfully updated.`,i=yield e.sendMessage(t,o);h[t]=Object.assign(Object.assign({},h[t]),{successId:i.message_id}),n(r)}else o(new Error(`Invalid ${a}.`))}))))}))}catch(e){throw console.error(`Failed to ask for ${a}:`,e),e}})),t.createSolTradeKeyboard=()=>({inline_keyboard:[[{text:"🛒 Execute Trade",callback_data:JSON.stringify({command:"snipetrade_buy_sol"})}],[{text:"📈 Take Profit",callback_data:JSON.stringify({command:"snipe_set_take_profit_sol"})},{text:"📉 Stop Loss",callback_data:JSON.stringify({command:"snipe_set_stop_loss_sol"})},{text:"📉 Amount",callback_data:JSON.stringify({command:"snipe_set_spend_amount_sol"})}],[{text:"🔄 Refresh Token",callback_data:JSON.stringify({command:"snipe_refresh_token_sol"})},{text:"🔍 Review Trade",callback_data:JSON.stringify({command:"snipe_review_trade_sol"})}],[{text:"⬅️ Back",callback_data:JSON.stringify({command:"back_to_main_trade"})}]]}),t.updateSolTradeConfig=(e,t,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"});r&&(r.config[n]=a,yield r.save(),console.log(`Updated ${String(n)} for trade:`,r))}catch(e){console.error(`Error updating trade ${String(n)}:`,e)}})),t.executeSolTrade=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield o.default.findOne({chatId:n,contractAddress:a,blockchain:"sol"});if(!r)return void(yield e.sendMessage(n,"❌ Trade configuration not found."));if(r.config.spendAmount<=0)return void(yield e.sendMessage(n,"⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set."));const s=d.TraderFactory.getTrader(d.BlockchainType.SOL),i=r.config.spendAmount.toString(),l=yield s.buyTokens(a,i,"",{slippage:5,gasLimit:1e6});if(!l.success)return c.logger.error("Failed to purchase tokens",{chatId:n,contractAddress:a,error:l.error}),void(yield e.sendMessage(n,`❌ Failed to purchase tokens: ${l.error||"Unknown error"}`));yield o.default.findOneAndUpdate({chatId:n,contractAddress:a,blockchain:"sol"},{isSold:!1,isInitialized:"ongoing"}).exec(),yield e.sendMessage(n,`✅ Successfully purchased tokens. Transaction Hash: ${l.transactionHash}`),yield(0,t.monitorSolPriceAndSell)(e,n,r),yield e.sendMessage(n,"✅ Trade executed successfully and monitoring started."),c.logger.info("SOL trade executed successfully",{chatId:n,contractAddress:a,transactionHash:l.transactionHash})}catch(t){c.logger.error("Error executing SOL trade",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while executing the trade: ${t.message}`)}}));const p={};t.monitorSolPriceAndSell=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const a=`${n.chatId}_${n.contractAddress}_${n.blockchain}`;c.logger.info("Starting SOL price monitoring",{chatId:t,contractAddress:n.contractAddress,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss});const s=setInterval((()=>r(void 0,void 0,void 0,(function*(){var r;try{const o=yield(0,i.getTokenInfoFromDexscreener)(n.contractAddress,d.BlockchainType.SOL);if(!o||void 0===o.priceChange)return void c.logger.warn("Token info or price change not found",{contractAddress:n.contractAddress});const y=parseFloat((null===(r=o.priceChange.h24)||void 0===r?void 0:r.toString())||"0");if(isNaN(y))return void c.logger.warn("Invalid priceChange value",{contractAddress:n.contractAddress,priceChange:o.priceChange});if(c.logger.debug(`Price Change: ${y}%`,{contractAddress:n.contractAddress,priceChange:y}),y>=n.config.takeProfit||y<=n.config.stopLoss){c.logger.info("Conditions met for selling tokens",{contractAddress:n.contractAddress,priceChange:y,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss}),clearInterval(s),delete p[a];const r=yield g.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:o}=r.sol_wallet,i=d.TraderFactory.getTrader(d.BlockchainType.SOL),h=yield i.getTokenBalance(n.contractAddress,o),f=parseFloat(h);if(isNaN(f)||f<=0)return void c.logger.error("Invalid or zero token balance",{contractAddress:n.contractAddress,userAddress:o,tokenBalance:h});c.logger.info(`Token Balance: ${f}`,{contractAddress:n.contractAddress,userAddress:o});let m=!1,_=0,v="";for(;_<2&&!m;)try{_++,c.logger.info(`Selling tokens, attempt ${_}`,{contractAddress:n.contractAddress,tokenBalance:f});const t=yield i.sellTokens(n.contractAddress,parseFloat(h),o,{slippage:5,gasLimit:1e6});if(t.success)return m=!0,v=t.transactionHash||"",yield(0,l.updateAutoTradeAsSold)(r._id,n.contractAddress,f,"sol"),yield(0,u.updateTradeAsSold)(r._id,n.contractAddress,f,"sol"),c.logger.info("Trade sold successfully",{contractAddress:n.contractAddress,transactionHash:v}),void(e&&(yield e.sendMessage(n.chatId,`✅ Trade Auto-sold successfully!\n\n🔗 Transaction Hash: ${v}\n💰 Amount Sold: ${f} tokens\n📊 Price Change: ${y}%`)));c.logger.error("Failed to sell tokens",{contractAddress:n.contractAddress,error:t.error})}catch(e){c.logger.error(`Attempt ${_} to sell tokens failed`,{contractAddress:n.contractAddress,error:e.message})}return void(m||(c.logger.error("Failed to sell tokens after 2 attempts",{contractAddress:n.contractAddress}),e&&(yield e.sendMessage(n.chatId,`⚠️ Failed to auto-sell tokens after ${_} attempts.\nPlease try to sell manually.`))))}c.logger.debug("Conditions not met, continuing monitoring",{contractAddress:n.contractAddress,priceChange:y,takeProfit:n.config.takeProfit,stopLoss:n.config.stopLoss})}catch(e){c.logger.error("Error in monitorSolPriceAndSell",{contractAddress:n.contractAddress,error:e.message})}}))),15e3);p[a]=s})),t.reviewSolTrade=(e,n,a,s)=>r(void 0,void 0,void 0,(function*(){var r;try{const l=yield o.default.findOne({chatId:n,contractAddress:s,blockchain:"sol"});if(!l)return void(yield e.sendMessage(n,"❌ No trade found."));const u=yield(0,i.getTokenInfoFromDexscreener)(s,d.BlockchainType.SOL),g=(null==u?void 0:u.priceUsd)||"Unknown",y=(null===(r=null==u?void 0:u.priceChange)||void 0===r?void 0:r.h24)||"Unknown",h=l.isSold?"Yes":"No",p="ongoing"===l.isInitialized?"Active":"pending"===l.isInitialized?"Pending":"Completed",f=`💼 **SOL Trade Details:**\n📝 **Contract:** ${s}\n🚀 **Token:** ${l.contractName}\n💵 **Current Price:** $${g}\n📊 **24h Change:** ${y}%\n📈 **Take Profit:** ${l.config.takeProfit}%\n📉 **Stop Loss:** ${l.config.stopLoss}%\n💰 **Spend Amount:** ${l.config.spendAmount} SOL\n🔄 **Is Sold:** ${h}\n⚙️ **Status:** ${p}`;yield e.editMessageText(f,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:(0,t.createSolTradeKeyboard)()}),c.logger.debug("SOL trade review displayed",{chatId:n,contractAddress:s,messageId:a})}catch(t){c.logger.error("Error in reviewSolTrade",{chatId:n,contractAddress:s,messageId:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while reviewing the trade: ${t.message}`)}})),t.tradeSolScreen=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const s=yield(0,i.getTokenInfoFromDexscreener)(a,d.BlockchainType.SOL);if(!s)return void(yield e.sendMessage(n,"❌ Token information not found."));const{symbol:l,name:u}=s,g=d.TraderFactory.getTrader(d.BlockchainType.SOL),y=yield g.checkHoneypot(a),h=new o.default({chatId:n,blockchain:"sol",contractAddress:a,contractName:u,isSold:!1,isInitialized:"pending",config:{takeProfit:0,stopLoss:0,spendAmount:0}});yield h.save();let p="💼 **SOL Trading**\n";p+=`📝 **Contract:** ${a}\n`,p+=`🚀 **Token:** ${u} (${l})\n`,p+=`🔗 [Dexscreener Link](https://dexscreener.com/solana/${a})\n\n`,y.isHoneypot&&(p+="⚠️ **HONEYPOT WARNING** ⚠️\n",p+="This token appears to be a honeypot. Trading is not recommended.\n",p+=`Reason: ${y.honeypotReason||"Unknown"}\n\n`),p+="What would you like to do next?";const f=yield e.sendMessage(n,p,{parse_mode:"Markdown",reply_markup:(0,t.createSolTradeKeyboard)()});c.logger.info("SOL trade screen displayed",{chatId:n,contractAddress:a,messageId:f.message_id,isHoneypot:y.isHoneypot});const m=s=>r(void 0,void 0,void 0,(function*(){var r;if((null===(r=s.message)||void 0===r?void 0:r.message_id)===f.message_id)try{const r=JSON.parse(s.data).command;switch(yield e.answerCallbackQuery(s.id),r){case"snipe_set_take_profit_sol":const r=yield(0,t.askForSolTradeInput)(e,n,"📈 Set your take profit %:","take profit");yield(0,t.updateSolTradeConfig)(n,a,"takeProfit",r);break;case"snipe_set_stop_loss_sol":const o=yield(0,t.askForSolTradeInput)(e,n,"📉 Set your stop loss %:","stop loss");yield(0,t.updateSolTradeConfig)(n,a,"stopLoss",o);break;case"snipe_set_spend_amount_sol":const i=yield(0,t.askForSolTradeInput)(e,n,"💰 Set the amount to spend:","spend amount");yield(0,t.updateSolTradeConfig)(n,a,"spendAmount",i);break;case"snipetrade_buy_sol":yield(0,t.executeSolTrade)(e,n,a);break;case"snipe_review_trade_sol":yield(0,t.reviewSolTrade)(e,n,s.message.message_id,a);break;case"back_to_main_trade":e.removeListener("callback_query",m)}}catch(t){c.logger.error("Error handling SOL trade callback",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred: ${t.message}`)}}));e.on("callback_query",m)}catch(t){c.logger.error("Error in tradeSolScreen",{chatId:n,contractAddress:a,error:t.message}),yield e.sendMessage(n,`⚠️ An error occurred while accessing the SOL trading screen: ${t.message}`)}}))},6913:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=a(n(8722));t.default=(e,t)=>r(void 0,void 0,void 0,(function*(){const{id:n}=t.chat;try{if(yield s.default.findOne({chat_id:n})){const t="👋 Select a wallet!\n\n";yield e.sendMessage(n,t,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"view_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"view_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}}catch(t){console.error("Error handling wallet selection:",t),yield e.sendMessage(n,"An error occurred while processing your request. Please try again later.")}}))},4046:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const s=a(n(8722)),o=a(n(6786)),i=n(4653);t.default=(e,t)=>r(void 0,void 0,void 0,(function*(){const{username:n,id:r,first_name:a,last_name:d}=t.chat;let c,l=0,u=null;if(u=yield s.default.findOne({chat_id:r}),u){const t=`👋 Welcome back to EasyBot!\n\nHere are your wallet addresses:\n\n<b>SOL Wallet Address:</b> <code>${u.sol_wallet.address}</code>\n<b>BSC Wallet Address:</b> <code>${u.bsc_wallet.address}</code>\n<b>BASE Wallet Address:</b> <code>${u.base_wallet.address}</code>\n<b>ETH Wallet Address:</b> <code>${u.eth_wallet.address}</code>\n\n<b>Fire /privatekeys to view</b>\n\n`;yield e.sendMessage(r,t,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"view_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"view_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}else{do{const t=(0,i.generateSOLWallet)(),l=(0,i.generateBSCWallet)(),g=(0,i.generateBASEWallet)(),y=(0,i.generateETHWallet)();c=new s.default({chat_id:r,first_name:a,last_name:d,username:n,sol_wallet:{address:t.address,private_key:t.privateKey},bsc_wallet:{address:l.address,private_key:l.privateKey},base_wallet:{address:g.address,private_key:g.privateKey},eth_wallet:{address:y.address,private_key:y.privateKey},trades:{bsc:[],sol:[],eth:[],base:[]}}),u=yield c.save();for(const e of["bsc","sol","eth","base"]){const t=u[`${e}_wallet`].address,n=new o.default({userId:u._id,walletId:t,contractName:"Empty",buyAmount:0,sellAmount:0,takeProfit:0,stopLoss:0});yield n.save(),yield s.default.updateOne({_id:u._id},{$push:{[`trades.${e}`]:n._id}})}const h=`👋 Welcome to EasyBot!\n\nHere are your wallet addresses:\n\n<b>SOL Wallet Address:</b> <code>${u.sol_wallet.address}</code>\n<b>BSC Wallet Address:</b> <code>${u.bsc_wallet.address}</code>\n<b>BASE Wallet Address:</b> <code>${u.base_wallet.address}</code>\n<b>ETH Wallet Address:</b> <code>${u.eth_wallet.address}</code>\n\n<b>Save these private keys below:</b>\n\n<tg-spoiler>SOL: <code>${u.sol_wallet.private_key}</code></tg-spoiler>\n<tg-spoiler>BSC: <code>${u.bsc_wallet.private_key}</code></tg-spoiler>\n<tg-spoiler>BASE: <code>${u.base_wallet.private_key}</code></tg-spoiler>\n<tg-spoiler>ETH: <code>${u.eth_wallet.private_key}</code></tg-spoiler>\n\n<b>To get started, please read our <a href="https://docs.io">docs</a></b>`;yield e.sendMessage(r,h,{parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"BASE",callback_data:JSON.stringify({command:"view_base"})}],[{text:"SOL",callback_data:JSON.stringify({command:"view_sol"})},{text:"ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}})}while(null===u&&l++<5);null===u&&(yield e.sendMessage(r,"Failed to create your wallet. Please try again later."))}}))},6997:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});const d=i(n(7252)),c=i(n(818)),l=i(n(6037)),u=i(n(6209)),g=n(3433),y=n(5055),h=n(108),p=n(5542),f=i(n(4046)),m=i(n(6913)),_=i(n(7307)),v=n(1888),b=n(1213),k=n(8708),w=n(4199),S=n(4545);c.default.config();const T=process.env.TELEGRAM_BOT_TOKEN||"",A=process.env.ADMIN_BOT_TOKEN||"",O=process.env.MONGODB_URI||"mongodb://localhost:27017/mydatabase",E=process.env.SOCKET_PORT||3e3;if(!T||!A)throw new Error("TELEGRAM_BOT_TOKEN or ADMIN_BOT_TOKEN is not defined in the .env file");const x=(0,d.default)(),B=process.env.PORT||8e3;l.default.connect(O,{autoCreate:!0,retryReads:!0}).then((()=>o(void 0,void 0,void 0,(function*(){b.logger.info("Connected to MongoDB successfully");const{bot:e}=(()=>{var e,t;process.setMaxListeners(20);const n=new u.default(T,{polling:!0}),r=(0,w.createBotService)(n);return null===(t=(e=n).setMaxListeners)||void 0===t||t.call(e,20),n.on("error",(e=>{var t;"ETELEGRAM"!==e.code||403!==(null===(t=e.response)||void 0===t?void 0:t.statusCode)?b.logger.error("Telegram bot error",{error:e}):b.logger.warn("Bot was blocked by a user",{error:e.message})})),n.on("callback_query",(e=>o(void 0,void 0,void 0,(function*(){yield(0,h.handleCallbackQuery)(n,e)})))),n.onText(/\/start/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,f.default)(n,e)})))),n.onText(/\/sniper/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,_.default)(n,e)})))),n.onText(/\/wallets/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,m.default)(n,e)})))),n.onText(/\/privatekeys/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,p.privatekeyHandler)(n,e)})))),b.logger.info("Bot initialized with all handlers"),{bot:n,botService:r}})();(()=>{var e,t;const n=new u.default(A,{polling:!0});(0,w.createBotService)(n);null===(t=(e=n).setMaxListeners)||void 0===t||t.call(e,20),n.on("callback_query",(e=>o(void 0,void 0,void 0,(function*(){yield(0,h.handleCallbackQuery)(n,e)})))),n.onText(/\/start/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,f.default)(n,e)})))),n.onText(/\/sniper/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,_.default)(n,e)})))),n.onText(/\/wallets/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,m.default)(n,e)})))),n.onText(/\/privatekeys/,(e=>o(void 0,void 0,void 0,(function*(){yield(0,p.privatekeyHandler)(n,e)})))),b.logger.info("Admin bot initialized with all handlers")})(),(0,S.initializeServices)(e),(0,v.fetchBalancesForAllChains)(),yield k.cacheService.set("server_start_time",(new Date).toISOString()),b.logger.info("Cache service initialized"),(0,g.initializeSocketServer)(Number(E),e),(0,y.initializeWalletMonitor)(e),x.get("/",((e,t)=>{t.send("Easy Sniper Bot is running")})),x.get("/health",((e,t)=>o(void 0,void 0,void 0,(function*(){try{const e=yield k.cacheService.get("server_start_time"),n=(yield k.cacheService.get("wallet_monitor:active"))||!1,r=yield l.default.model("WalletBalance").countDocuments();t.json({status:"ok",uptime:process.uptime(),startTime:e,version:process.env.npm_package_version||"1.0.0",services:{walletMonitor:{active:n,walletCount:r}}})}catch(e){t.status(500).json({status:"error",message:e.message||"Unknown error"})}})))),x.listen(B,(()=>{b.logger.info(`Server is running on port ${B}`)}));const t=()=>o(void 0,void 0,void 0,(function*(){b.logger.info("Graceful shutdown initiated");const{walletMonitorService:e}=yield Promise.resolve().then((()=>s(n(5055))));yield e.stop(),b.logger.info("Wallet monitor service stopped"),yield new Promise((e=>{const t=x.listen();t?t.close((()=>{b.logger.info("HTTP server closed"),e()})):(b.logger.info("No HTTP server to close"),e())})),yield l.default.connection.close(),b.logger.info("MongoDB connection closed"),process.exit(0)}));process.on("SIGTERM",t),process.on("SIGINT",t)})))).catch((e=>{b.logger.error("Error connecting to MongoDB:",{error:e.message})}))},2724:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBaseDashboard=t.getTokenDetails=t.sellBaseTokenPercentage=t.handleBaseSellAll=t.handleSpendBase=t.handleBaseChart=t.handleBaseRefresh=t.askForBaseToken=t.askForBaseContractAddress=t.handleGenerateNewBaseWallet=t.mainbaseMenu=t.updatebaseMessageWithSellOptions=t.createbaseKeyboard=t.getBaseSellOptionsKeyboard=t.handleBASEScanContract=void 0,t.fetchAndDisplayActiveBaseTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let a=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield w(e,t,r,a)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayCurrentBaseTrade=w;const s=a(n(8722)),o=a(n(6786)),i=n(9558),d=a(n(3883)),c=n(9321),l=n(1347);Object.defineProperty(t,"handleBaseDashboard",{enumerable:!0,get:function(){return l.handleBaseDashboard}});const u=n(4653),g=n(8014),y=n(8014),h=n(4736),p=n(871),f=n(2969);let m={},_={};const v=process.env.BASE_PROVIDER_URL,b=new c.ethers.providers.JsonRpcProvider(v);if(!v)throw new Error("PROVIDER is not defined in the environment variables.");t.handleBASEScanContract=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(!a)throw new Error("Invalid contract address.");const l=(0,h.getCurrentContractAddress)(p.BlockchainType.BASE);l&&m[l]&&delete m[l],m[a]=m[a]||{},(0,h.setCurrentContractAddress)(p.BlockchainType.BASE,a);const u=yield(0,f.checkIfHoneypot)(a,p.BlockchainType.BASE),g=yield(0,i.getTokenInfoFromDexscreener)(a),y=yield(o=a,r(void 0,void 0,void 0,(function*(){try{const e=new c.ethers.Contract(o,d.default,b),t=yield e.decimals(),n=yield e.totalSupply();return c.ethers.utils.formatUnits(n,t)}catch(e){return console.error("Error fetching total supply:",e),null}})));if(null===y)throw new Error("Failed to fetch total supply.");const v=Number(y),k=Number(g.priceUsd)*v,w=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),S=w(v),T=w(k),A=w(Number(g.liquidity.usd)),O=g.priceChange.h1||0,E=O>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",x=Math.ceil(parseFloat(u.buyTax)),B=Math.ceil(parseFloat(u.sellTax)),M=`\n🪙 ${g.symbol} (${g.name}) || 📈 ${O>0?"+":""}${O}%  || 🏦 ${g.dexId} || ${g.chainId}\n\n${E}  Price Change (1h): ${O>0?"+":""}${O}%\n\nCA: ${g.address}\nLP: ${g.pairAddress}\n\n💼 TOTAL SUPPLY: ${S} ${g.symbol}\n🏷️ MC: $${T}\n💧 LIQUIDITY: $${A}\n💵 PRICE: $${g.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${u.risk}\n📉 Buy Tax: ${x}%\n📈 Sell Tax: ${B}%\n\n⚠️⚠️⚠️⚠️ ${u.details||"No specific risks identified"}\n    `,N=yield s.default.findOne({chat_id:n}).exec();N&&(N.baseCurrentTokenName=g.name,N.baseCurrentContractAddress=a,yield N.save()),m[a]={totalSupply:v,marketCap:k,checkHoneypot:u,tokenInfo:g,resultMessage:M};const I=_[n];I&&(yield e.deleteMessage(n,I));const C=yield e.sendMessage(n,M,{parse_mode:"Markdown",reply_markup:(0,t.createbaseKeyboard)()});return _[n]=C.message_id,C.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}var o})),t.getBaseSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_base"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_base"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_base"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_base"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_base"})}]]})),t.createbaseKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_base"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_base"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_base"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_base"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_base"})},{text:"💸 Spend X Base",callback_data:JSON.stringify({command:"spend_base"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updatebaseMessageWithSellOptions=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getBaseSellOptionsKeyboard)(),s=(0,h.getCurrentContractAddress)(p.BlockchainType.BASE);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.mainbaseMenu=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createbaseKeyboard)(),s=(0,h.getCurrentContractAddress)(p.BlockchainType.BASE);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update base menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewBaseWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield s.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,u.generateBASEWallet)();yield s.default.updateOne({chat_id:t},{$set:{"base_wallet.address":n.address,"base_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the base blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,l.handleBaseDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let k={};function w(e,t,n,a,o){return r(this,void 0,void 0,(function*(){const r=n[a],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield s.default.findOneAndUpdate({chat_id:t},{baseCurrentContractAddress:r.contractAddress,baseCurrentTokenName:r.contractName}),(0,h.setCurrentContractAddress)(p.BlockchainType.BASE,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_base"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_base"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_base"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_base"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_base"})},{text:"Next",callback_data:JSON.stringify({command:"next_base"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}function S(e,t){return r(this,void 0,void 0,(function*(){const n=new c.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],b),[r,a]=yield Promise.all([n.balanceOf(t),n.decimals()]);return c.ethers.utils.formatUnits(r,a)}))}t.askForBaseContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,g.INPUT_BASE_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForBaseToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,g.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received Base token:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleBaseRefresh=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,h.getCurrentContractAddress)(p.BlockchainType.BASE);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleBASEScanContract)(e,n,r),a){const t=a.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleBaseChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,h.getCurrentContractAddress)(p.BlockchainType.BASE);if(n){const r=`https://dexscreener.com/base/${n}`,a=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});k[t]={promptId:a.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");k[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendBase=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,y.SET_BASE_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend base:",e),new Error("Failed to handle spend base action.")}})),t.handleBaseSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield s.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.base_wallet,a=n.baseCurrentContractAddress||"";if(!a)throw new Error("No contract address found for user");const o=yield S(a,r),i=parseFloat(o);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=p.TraderFactory.getTrader(p.BlockchainType.BASE),c=yield d.sellTokens(a,100,r);if(c&&c.transactionHash&&c.blockNumber){const{transactionHash:n,blockNumber:r}=c;yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${n}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${n}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling all tokens: ${r}`)}})),t.sellBaseTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:a}=r.base_wallet,o=r.baseCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=yield S(o,a),d=parseFloat(i);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");const c=d*(n/100);if(c<=0)throw new Error("Invalid percentage value");console.log("Amount to sell:",c);const l=p.TraderFactory.getTrader(p.BlockchainType.BASE),u=yield l.sellTokens(o,n,a);if(u&&u.transactionHash&&u.blockNumber){const{transactionHash:r,blockNumber:a}=u;yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${r}\n🧱 Block Number: ${a}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${r}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling tokens: ${r}`)}})),t.getTokenDetails=e=>m[e]},5488:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.providerManager=t.ProviderManager=void 0;const a=n(9321),s=n(1213),o=n(8708),i={bsc:["https://bsc-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3","https://bsc-dataseed1.defibit.io/","https://bsc-dataseed1.ninicoin.io/","https://bsc-dataseed2.defibit.io/","https://bsc-dataseed3.defibit.io/","https://bsc-dataseed4.defibit.io/","https://rpc.ankr.com/bsc"],eth:["https://eth.llamarpc.com","https://mainnet.infura.io/v3/********************************","https://rpc.ankr.com/eth","https://ethereum.publicnode.com","https://eth-mainnet.public.blastapi.io"],base:["https://mainnet.base.org","https://base-mainnet.g.alchemy.com/v2/6O-PggabiAzJuhB3Qxw7vrO5eTpEH5rx/","https://base.blockpi.network/v1/rpc/public","https://base-rpc.publicnode.com","https://1rpc.io/base"],sol:["https://api.mainnet-beta.solana.com","https://solana-mainnet.g.alchemy.com/v2/demo","https://rpc.ankr.com/solana","https://solana-api.projectserum.com"]},d={eth:1,bsc:56,base:8453};class c{constructor(){this.providers=new Map,this.currentProviders=new Map,this.healthCheckInterval=null,this.initializeProviders(),this.startHealthCheck()}initializeProviders(){this.initializeChainProviders("bsc",process.env.BSC_PROVIDER_URL),this.initializeChainProviders("eth",process.env.ETH_PROVIDER_URL),this.initializeChainProviders("base",process.env.BASE_PROVIDER_URL),s.logger.info("Provider Manager initialized",{chains:Array.from(this.providers.keys())})}initializeChainProviders(e,t){const n=[],r=i[e]||[],a=d[e];if(t){const e=this.createProvider(t,a);n.push({provider:e,url:t,isHealthy:!0,lastChecked:new Date,failCount:0})}for(const e of r){if(e===t)continue;const r=this.createProvider(e,a);n.push({provider:r,url:e,isHealthy:!0,lastChecked:new Date,failCount:0})}this.providers.set(e,n),n.length>0&&this.currentProviders.set(e,n[0])}createProvider(e,t){const n=new a.ethers.providers.JsonRpcProvider(e,t);return n.on("error",(t=>{s.logger.warn(`Provider error for ${e}:`,{error:t.message,url:e})})),n}startHealthCheck(){this.healthCheckInterval=setInterval((()=>{this.checkAllProviders()}),12e4)}checkAllProviders(){return r(this,void 0,void 0,(function*(){for(const[e,t]of this.providers.entries())yield this.checkProvidersForChain(e,t)}))}checkProvidersForChain(e,t){return r(this,void 0,void 0,(function*(){var n;let r=!1;for(const n of t){const t=yield this.checkProviderHealth(n.provider,n.url);if(n.isHealthy=t,n.lastChecked=new Date,t){n.failCount=0,r=!0;const t=this.currentProviders.get(e);t&&!t.isHealthy&&(this.currentProviders.set(e,n),s.logger.info(`Switched to healthy provider for ${e}`,{url:n.url}))}else n.failCount++}r||s.logger.warn(`No healthy provider found for ${e}`),yield o.cacheService.set(`provider:status:${e}`,{chain:e,providers:t.map((e=>({url:e.url,isHealthy:e.isHealthy,failCount:e.failCount,lastChecked:e.lastChecked}))),currentProvider:null===(n=this.currentProviders.get(e))||void 0===n?void 0:n.url},300)}))}checkProviderHealth(e,t){return r(this,void 0,void 0,(function*(){try{return(yield e.getNetwork()).chainId>0}catch(e){return s.logger.warn(`Provider health check failed for ${t}`,{error:e.message,url:t}),!1}}))}getProvider(e){const t=this.currentProviders.get(e);return t?t.provider:null}getAllProviders(e){return(this.providers.get(e)||[]).map((e=>e.provider))}shutdown(){this.healthCheckInterval&&(clearInterval(this.healthCheckInterval),this.healthCheckInterval=null)}}t.ProviderManager=c,t.providerManager=new c},5419:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting token info for ${e} from Basescan`,{contractAddress:e});const t=new a.ethers.Contract(e,["function name() view returns (string)","function symbol() view returns (string)","function decimals() view returns (uint8)","function totalSupply() view returns (uint256)"],d),[n,r,o,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),c=a.ethers.utils.formatUnits(i,o);return s.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:n,symbol:r,decimals:o,totalSupply:c}),{name:n,symbol:r,decimals:o,totalSupply:c}}catch(t){throw s.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new a.ethers.Contract(e,["function balanceOf(address) view returns (uint256)","function decimals() view returns (uint8)"],d),[r,o]=yield Promise.all([n.balanceOf(t),n.decimals()]),i=a.ethers.utils.formatUnits(r,o);return s.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:i}),i}catch(n){throw s.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),n}}))},t.getBaseBalance=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting BASE balance for ${e}`,{walletAddress:e});const t=yield d.getBalance(e),n=a.ethers.utils.formatEther(t);return s.logger.info(`BASE balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw s.logger.error(`Error getting BASE balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.getTransactionReceipt=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting transaction receipt for ${e}`,{txHash:e});const t=yield d.getTransactionReceipt(e);return s.logger.info(`Transaction receipt retrieved for ${e}`,{txHash:e,status:t.status}),t}catch(t){throw s.logger.error(`Error getting transaction receipt for ${e}`,{txHash:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=o.TraderFactory.getTrader(o.BlockchainType.BASE),n=yield t.checkHoneypot(e);return s.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return s.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const a=n(9321),s=n(1213),o=n(871),i=process.env.BASE_PROVIDER_URL||"https://mainnet.base.org",d=(process.env.BASE_SCAN_API_KEY,new a.ethers.providers.JsonRpcProvider(i))},3372:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting token info for ${e} from BSCScan`,{contractAddress:e});const t=new a.ethers.Contract(e,["function name() view returns (string)","function symbol() view returns (string)","function decimals() view returns (uint8)","function totalSupply() view returns (uint256)"],d),[n,r,o,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),c=a.ethers.utils.formatUnits(i,o);return s.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:n,symbol:r,decimals:o,totalSupply:c}),{name:n,symbol:r,decimals:o,totalSupply:c}}catch(t){throw s.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new a.ethers.Contract(e,["function balanceOf(address) view returns (uint256)","function decimals() view returns (uint8)"],d),[r,o]=yield Promise.all([n.balanceOf(t),n.decimals()]),i=a.ethers.utils.formatUnits(r,o);return s.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:i}),i}catch(n){throw s.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),n}}))},t.getBnbBalance=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting BNB balance for ${e}`,{walletAddress:e});const t=yield d.getBalance(e),n=a.ethers.utils.formatEther(t);return s.logger.info(`BNB balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw s.logger.error(`Error getting BNB balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.getTransactionReceipt=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting transaction receipt for ${e}`,{txHash:e});const t=yield d.getTransactionReceipt(e);return s.logger.info(`Transaction receipt retrieved for ${e}`,{txHash:e,status:t.status}),t}catch(t){throw s.logger.error(`Error getting transaction receipt for ${e}`,{txHash:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=o.TraderFactory.getTrader(o.BlockchainType.BSC),n=yield t.checkHoneypot(e);return s.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return s.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const a=n(9321),s=n(1213),o=n(871),i=process.env.BSC_PROVIDER_URL||"https://bsc-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3",d=(process.env.BSC_SCAN_API_KEY,new a.ethers.providers.JsonRpcProvider(i))},6395:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting token info for ${e} from Etherscan`,{contractAddress:e});const t=new a.ethers.Contract(e,["function name() view returns (string)","function symbol() view returns (string)","function decimals() view returns (uint8)","function totalSupply() view returns (uint256)"],d),[n,r,o,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),c=a.ethers.utils.formatUnits(i,o);return s.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:n,symbol:r,decimals:o,totalSupply:c}),{name:n,symbol:r,decimals:o,totalSupply:c}}catch(t){throw s.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new a.ethers.Contract(e,["function balanceOf(address) view returns (uint256)","function decimals() view returns (uint8)"],d),[r,o]=yield Promise.all([n.balanceOf(t),n.decimals()]),i=a.ethers.utils.formatUnits(r,o);return s.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:i}),i}catch(n){throw s.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),n}}))},t.getEthBalance=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting ETH balance for ${e}`,{walletAddress:e});const t=yield d.getBalance(e),n=a.ethers.utils.formatEther(t);return s.logger.info(`ETH balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw s.logger.error(`Error getting ETH balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.getTransactionReceipt=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Getting transaction receipt for ${e}`,{txHash:e});const t=yield d.getTransactionReceipt(e);return s.logger.info(`Transaction receipt retrieved for ${e}`,{txHash:e,status:t.status}),t}catch(t){throw s.logger.error(`Error getting transaction receipt for ${e}`,{txHash:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{s.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=o.TraderFactory.getTrader(o.BlockchainType.ETH),n=yield t.checkHoneypot(e);return s.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return s.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const a=n(9321),s=n(1213),o=n(871),i=process.env.ETH_PROVIDER_URL||"https://mainnet.infura.io/v3/your-infura-key",d=(process.env.ETH_SCAN_API_KEY,new a.ethers.providers.JsonRpcProvider(i))},1536:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)},o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.solscan=t.ethscan=t.basescan=t.bscscan=void 0,t.initializeBlockchainServices=function(){const{logger:e}=n(1213);e.info("Initializing blockchain services...");try{const{providerManager:t}=n(5488);e.info("Provider Manager initialized successfully")}catch(t){e.error("Failed to initialize Provider Manager",{error:t.message})}e.info("All blockchain services initialized successfully")},s(n(3003),t),s(n(6580),t),s(n(2969),t);const i=o(n(3372)),d=o(n(5419)),c=o(n(6395)),l=o(n(5150));t.bscscan=i,t.basescan=d,t.ethscan=c,t.solscan=l},5150:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfo=function(e){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Getting token info for ${e} from Solscan`,{contractAddress:e});const t=new s.PublicKey(e),n=yield(0,o.getMint)(g,t);let r="Unknown",a="Unknown";try{const t=yield i.default.get(`${u}/token/${e}`);t.data&&t.data.name&&(r=t.data.name,a=t.data.symbol||"Unknown")}catch(t){d.logger.warn(`Could not fetch token metadata from Solscan API for ${e}`,{contractAddress:e,error:t.message})}const c=(Number(n.supply)/Math.pow(10,n.decimals)).toString();return d.logger.info(`Token info retrieved for ${e}`,{contractAddress:e,name:r,symbol:a,decimals:n.decimals,supply:c}),{name:r,symbol:a,decimals:n.decimals,supply:c}}catch(t){throw d.logger.error(`Error getting token info for ${e}`,{contractAddress:e,error:t.message}),t}}))},t.getTokenBalance=function(e,t){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Getting token balance for ${t}`,{contractAddress:e,walletAddress:t});const n=new s.PublicKey(e),r=new s.PublicKey(t),a=yield g.getTokenAccountsByOwner(r,{mint:n});if(0===a.value.length)return d.logger.info(`No token account found for ${e} in wallet ${t}`,{contractAddress:e,walletAddress:t}),"0";const i=yield(0,o.getAccount)(g,a.value[0].pubkey),c=yield(0,o.getMint)(g,n),l=(Number(i.amount)/Math.pow(10,c.decimals)).toString();return d.logger.info(`Token balance retrieved for ${t}`,{contractAddress:e,walletAddress:t,balance:l}),l}catch(n){return d.logger.error(`Error getting token balance for ${t}`,{contractAddress:e,walletAddress:t,error:n.message}),"0"}}))},t.getSolBalance=function(e){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Getting SOL balance for ${e}`,{walletAddress:e});const t=new s.PublicKey(e),n=((yield g.getBalance(t))/s.LAMPORTS_PER_SOL).toString();return d.logger.info(`SOL balance retrieved for ${e}`,{walletAddress:e,balance:n}),n}catch(t){throw d.logger.error(`Error getting SOL balance for ${e}`,{walletAddress:e,error:t.message}),t}}))},t.checkHoneypot=function(e){return r(this,void 0,void 0,(function*(){try{d.logger.info(`Checking if ${e} is a honeypot`,{contractAddress:e});const t=c.TraderFactory.getTrader(c.BlockchainType.SOL),n=yield t.checkHoneypot(e);return d.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,isHoneypot:n.isHoneypot,reason:n.honeypotReason}),n}catch(t){return d.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,error:t.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${t.message}`}}}))};const s=n(8491),o=n(7018),i=a(n(8938)),d=n(1213),c=n(871),l=process.env.SOL_PROVIDER_URL||"https://api.mainnet-beta.solana.com",u="https://api.solscan.io",g=new s.Connection(l)},4199:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BotService=void 0,t.createBotService=function(e){return new s(e)};const a=n(1213);class s{constructor(e){this.messageStore=new Map,this.bot=e,a.logger.info("Bot service initialized")}sendMessage(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield this.bot.sendMessage(e,t,n);return a.logger.debug(`Message sent to ${e}`,{messageId:r.message_id}),r}catch(t){return t.message.includes("403 Forbidden: bot was blocked by the user")?(a.logger.warn(`User ${e} has blocked the bot`,{chatId:e}),null):(a.logger.error(`Failed to send message to ${e}`,{error:t.message,chatId:e}),null)}}))}editMessage(e,t,n,s){return r(this,void 0,void 0,(function*(){try{const r=yield this.bot.editMessageText(n,Object.assign({chat_id:e,message_id:t},s));return a.logger.debug(`Message ${t} edited in chat ${e}`),r}catch(n){return n.message.includes("403 Forbidden: bot was blocked by the user")?(a.logger.warn(`User ${e} has blocked the bot`,{chatId:e,messageId:t}),null):n.message.includes("message is not modified")?(a.logger.info(`Message ${t} in chat ${e} was not modified (content unchanged)`),!0):(a.logger.error(`Failed to edit message ${t} in chat ${e}`,{error:n.message,chatId:e,messageId:t}),null)}}))}deleteMessage(e,t){return r(this,void 0,void 0,(function*(){try{const n=yield this.bot.deleteMessage(e,t);return a.logger.debug(`Message ${t} deleted from chat ${e}`),n}catch(n){return a.logger.error(`Failed to delete message ${t} from chat ${e}`,{error:n.message,chatId:e,messageId:t}),!1}}))}storeMessageId(e,t,n){this.messageStore.has(e)||this.messageStore.set(e,new Map),this.messageStore.get(e).set(t,n)}getStoredMessageId(e,t){const n=this.messageStore.get(e);if(n)return n.get(t)}createBlockchainKeyboard(){return{inline_keyboard:[[{text:"🔷 BSC",callback_data:JSON.stringify({command:"view_bsc"})},{text:"🔶 ETH",callback_data:JSON.stringify({command:"view_eth"})}],[{text:"🔵 BASE",callback_data:JSON.stringify({command:"view_base"})},{text:"⚪ SOL",callback_data:JSON.stringify({command:"view_sol"})}],[{text:"⚙️ Settings",callback_data:JSON.stringify({command:"show_config"})}]]}}createBlockchainSpecificKeyboard(e){return{inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:`scan_contract_${e}`})},{text:"👛 Generate Wallet",callback_data:JSON.stringify({command:`generate_new_${e}_wallet`})}],[{text:"🔫 Snipe Token",callback_data:JSON.stringify({command:`scan_snipe_${e}`})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:`active_trades_${e}`})}],[{text:"🏠 Main Menu",callback_data:JSON.stringify({command:"main_menu"})}]]}}getBotInstance(){return this.bot}}t.BotService=s},108:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleCallbackQuery=function(e,t){return r(this,void 0,void 0,(function*(){var n,a,v;try{const v=null===(n=t.message)||void 0===n?void 0:n.chat.id,b=null===(a=t.message)||void 0===a?void 0:a.message_id;if(!v||!b)return void s.logger.warn("Invalid callback query: missing chat ID or message ID");const k=new o.BotService(e),w=JSON.parse(t.data||"{}").command;switch(s.logger.debug(`Processing callback query: ${w}`,{chatId:v,messageId:b}),w){case"dismiss_message":yield function(e,t,n){return r(this,void 0,void 0,(function*(){try{yield e.deleteMessage(t,n)}catch(e){s.logger.error("Error dismissing message:",{error:e.message,chatId:t,messageId:n})}}))}(k,v,b);break;case"show_config":yield(0,y.showConfigKeyboard)(e,v);break;case"view_bsc":case"view_base":case"view_sol":case"view_eth":yield function(e,t,n){return r(this,void 0,void 0,(function*(){const r=n.split("_")[1];try{switch(yield d.default.updateOne({chat_id:t},{$set:{currentBlockchain:r}}),r){case"bsc":yield(0,c.handleBscDashboard)(e,t);break;case"base":yield(0,u.handleBaseDashboard)(e,t);break;case"eth":yield(0,l.handleEthDashboard)(e,t);break;case"sol":yield(0,g.handleSolDashboard)(e,t);break;default:s.logger.warn(`Unknown blockchain: ${r}`,{chatId:t}),yield e.sendMessage(t,"Unknown blockchain. Please try again.")}}catch(n){s.logger.error(`Error handling ${r} view:`,{error:n.message,chatId:t}),yield e.sendMessage(t,`⚠️ Error: ${n instanceof Error?n.message:"Unknown error occurred."}`)}}))}(e,v,w);break;case"generate_new_bsc_wallet":yield(0,c.handleGenerateNewBscWallet)(e,v);break;case"generate_new_eth_wallet":yield(0,l.handleGenerateNewethWallet)(e,v);break;case"generate_new_base_wallet":yield(0,u.handleGenerateNewBaseWallet)(e,v);break;case"generate_new_sol_wallet":yield(0,g.handleGenerateNewSolWallet)(e,v);break;case"scan_contract_bsc":const t=yield(0,c.askForContractAddress)(e,v,b);t?yield(0,c.handleBSCScanContract)(e,v,t):yield k.sendMessage(v,"Invalid contract address.");break;case"scan_contract_eth":const n=yield(0,l.askForEthContractAddress)(e,v,b);n?yield(0,l.handleETHScanContract)(e,v,n):yield k.sendMessage(v,"Invalid contract address.");break;case"scan_contract_base":const a=yield(0,u.askForBaseContractAddress)(e,v,b);a?yield(0,u.handleBASEScanContract)(e,v,a):yield k.sendMessage(v,"Invalid contract address.");break;case"scan_contract_sol":const o=yield(0,g.askForSolContractAddress)(e,v,b);o?yield(0,g.handleSOLScanContract)(e,v,o):yield k.sendMessage(v,"Invalid contract address.");break;case"scan_snipe_bsc":try{const t=yield(0,c.askForContractAddress)(e,v,b);yield d.default.updateOne({chat_id:v},{$set:{currentBlockchain:"bsc"}}),t?yield(0,h.bscSniperScreen)(e,v,t):yield k.sendMessage(v,"Invalid contract address.")}catch(e){s.logger.error("Error during BSC token snipe:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"scan_snipe_eth":try{const t=yield(0,l.askForEthContractAddress)(e,v,b);yield d.default.updateOne({chat_id:v},{$set:{currentBlockchain:"eth"}}),t?yield(0,p.ethSniperScreen)(e,v,t):yield k.sendMessage(v,"Invalid contract address.")}catch(e){s.logger.error("Error during ETH token snipe:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"scan_snipe_base":try{const t=yield(0,u.askForBaseContractAddress)(e,v,b);yield d.default.updateOne({chat_id:v},{$set:{currentBlockchain:"base"}}),t?yield(0,f.baseSniperScreen)(e,v,t):yield k.sendMessage(v,"Invalid contract address.")}catch(e){s.logger.error("Error during BASE token snipe:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"scan_snipe_sol":try{const t=yield(0,g.askForSolContractAddress)(e,v,b);yield d.default.updateOne({chat_id:v},{$set:{currentBlockchain:"sol"}}),t?yield(0,m.solSniperScreen)(e,v,t):yield k.sendMessage(v,"Invalid contract address.")}catch(e){s.logger.error("Error during SOL token snipe:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_bsc":try{const t=yield(0,c.handleSpendBSC)(e,v);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.BSC);if(!e){yield k.sendMessage(v,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:v});if(!n||!n.bsc_wallet||!n.bsc_wallet.address){yield k.sendMessage(v,"No wallet found. Please generate a wallet first.");break}const r=n.bsc_wallet.address,a=i.TraderFactory.getTrader(i.BlockchainType.BSC),s=yield a.buyTokens(e,t,r);if(s&&s.transactionHash&&s.blockNumber){const{transactionHash:e,blockNumber:t}=s;yield k.sendMessage(v,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n🧱 Block Number: ${t}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(v,"Error: Unable to get transaction details.")}else yield k.sendMessage(v,"Invalid spend amount.")}catch(e){s.logger.error("Error purchasing BSC tokens:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_eth":try{const t=yield(0,l.handleSpendeth)(e,v);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.ETH);if(!e){yield k.sendMessage(v,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:v});if(!n||!n.eth_wallet||!n.eth_wallet.address){yield k.sendMessage(v,"No wallet found. Please generate a wallet first.");break}const r=n.eth_wallet.address,a=i.TraderFactory.getTrader(i.BlockchainType.ETH),s=yield a.buyTokens(e,t,r);if(s&&s.transactionHash&&s.blockNumber){const{transactionHash:e,blockNumber:t}=s;yield k.sendMessage(v,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n🧱 Block Number: ${t}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.io/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(v,"Error: Unable to get transaction details.")}else yield k.sendMessage(v,"Invalid spend amount.")}catch(e){s.logger.error("Error purchasing ETH tokens:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_base":try{const t=yield(0,u.handleSpendBase)(e,v);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.BASE);if(!e){yield k.sendMessage(v,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:v});if(!n||!n.base_wallet||!n.base_wallet.address){yield k.sendMessage(v,"No wallet found. Please generate a wallet first.");break}const r=n.base_wallet.address,a=i.TraderFactory.getTrader(i.BlockchainType.BASE),s=yield a.buyTokens(e,t,r);if(s&&s.transactionHash&&s.blockNumber){const{transactionHash:e,blockNumber:t}=s;yield k.sendMessage(v,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n🧱 Block Number: ${t}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(v,"Error: Unable to get transaction details.")}else yield k.sendMessage(v,"Invalid spend amount.")}catch(e){s.logger.error("Error purchasing BASE tokens:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"spend_sol":try{const t=yield(0,g.handleSpendsol)(e,v);if(t){const e=(0,_.getCurrentContractAddress)(i.BlockchainType.SOL);if(!e){yield k.sendMessage(v,"No contract address selected. Please select a token first.");break}const n=yield d.default.findOne({chat_id:v});if(!n||!n.sol_wallet||!n.sol_wallet.address){yield k.sendMessage(v,"No wallet found. Please generate a wallet first.");break}const r=n.sol_wallet.address,a=i.TraderFactory.getTrader(i.BlockchainType.SOL),s=yield a.buyTokens(e,t,r);if(s&&s.transactionHash){const{transactionHash:e}=s;yield k.sendMessage(v,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${e}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${e}\nTo see your transaction`)}else yield k.sendMessage(v,"Error: Unable to get transaction details.")}else yield k.sendMessage(v,"Invalid spend amount.")}catch(e){s.logger.error("Error purchasing SOL tokens:",{error:e.message,chatId:v}),yield k.sendMessage(v,`⚠️ Error: ${e instanceof Error?e.message:"Unknown error occurred."}`)}break;case"main_menu":yield function(e,t){return r(this,void 0,void 0,(function*(){try{const n=e.createBlockchainKeyboard();yield e.sendMessage(t,"🏠 *Main Menu*\n\nSelect a blockchain to continue:",{parse_mode:"Markdown",reply_markup:n})}catch(e){s.logger.error("Error showing main menu:",{error:e.message,chatId:t})}}))}(k,v);break;default:s.logger.warn(`Unknown callback command: ${w}`,{chatId:v}),yield k.sendMessage(v,"Unknown command. Please try again.")}yield e.answerCallbackQuery(t.id)}catch(n){if(n.message.includes("query is too old")||n.message.includes("query ID is invalid")){if(s.logger.warn("Callback query expired or invalid:",{query:t.data,queryId:t.id}),null===(v=t.message)||void 0===v?void 0:v.chat.id)try{const n=new o.BotService(e);yield n.sendMessage(t.message.chat.id,"This action has expired. Please try again with a new command.",{reply_to_message_id:t.message.message_id})}catch(e){s.logger.debug("Could not send expired query message",{error:e.message})}}else{s.logger.error("Error handling callback query:",{error:n.message,query:t.data});try{yield e.answerCallbackQuery(t.id,{text:"An error occurred. Please try again.",show_alert:!0})}catch(e){e.message.includes("query is too old")||e.message.includes("query ID is invalid")?s.logger.debug("Could not answer expired callback query",{queryId:t.id}):s.logger.error("Error answering callback query:",{error:e.message,queryId:t.id})}}}}))};const s=n(1213),o=n(4199),i=n(871),d=a(n(8722)),c=n(3173),l=n(6314),u=n(2724),g=n(4035),y=n(566),h=n(2893),p=n(4248),f=n(8786),m=n(4259),_=n(4736)},3173:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleBscDashboard=t.getTokenDetails=t.sellBscTokenPercentage=t.handleBscSellAll=t.handleSpendBSC=t.handleChart=t.handleRefresh=t.askForBSCToken=t.askForContractAddress=t.handleGenerateNewBscWallet=t.mainBscMenu=t.updateBSCMessageWithTradeOptions=t.updateBSCMessageWithSellOptions=t.createBSCKeyboard=t.getSellOptionsKeyboard=t.handleBSCScanContract=void 0,t.fetchAndDisplayActiveBscTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let a=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield S(e,t,r,a)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayBscCurrentTrade=S;const s=a(n(8722)),o=a(n(6786)),i=n(9088),d=n(9558),c=a(n(3883)),l=n(9321),u=n(4544);Object.defineProperty(t,"handleBscDashboard",{enumerable:!0,get:function(){return u.handleBscDashboard}});const g=n(4653),y=n(8014),h=n(8014),p=n(4736),f=n(8485),m=n(871);let _={},v={};const b=process.env.BSC_PROVIDER_URL;if(!b)throw new Error("BSC_PROVIDER_URL is not defined in the environment variables.");const k=new l.ethers.providers.JsonRpcProvider(b);t.handleBSCScanContract=(e,n,a)=>r(void 0,void 0,void 0,(function*(){var o,u;try{if(!a)throw new Error("Invalid contract address.");const g=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);g&&_[g]&&delete _[g],_[a]=_[a]||{},(0,p.setCurrentContractAddress)(m.BlockchainType.BSC,a);const y=yield(0,i.checkEVMHoneypot)(a,m.BlockchainType.BSC),h=yield(0,d.getTokenInfoFromDexscreener)(a),f=yield(u=a,r(void 0,void 0,void 0,(function*(){try{const e=new l.ethers.Contract(u,c.default,k),t=yield e.decimals(),n=yield e.totalSupply();return l.ethers.utils.formatUnits(n,t)}catch(e){return console.error("Error fetching total supply:",e),null}})));if(null===f)throw new Error("Failed to fetch total supply.");const b=Number(f),w=Number(h.priceUsd)*b,S=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),T=S(b),A=S(w),O=S(Number(h.liquidity.usd)),E=(null===(o=h.priceChange)||void 0===o?void 0:o.h1)||0,x=E>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",B=Math.ceil(parseFloat(String(y.buyTax||0))),M=Math.ceil(parseFloat(String(y.sellTax||0))),N=`\n🪙 ${h.symbol} (${h.name}) || 📈 ${E>0?"+":""}${E}%  || 🏦 ${h.dexId} || ${h.chainId}\n\n${x}  Price Change: ${E>0?"+":""}${E}%\n\nCA: ${h.address}\nLP: ${h.pairAddress}\n\n💼 TOTAL SUPPLY: ${T} ${h.symbol}\n🏷️ MC: $${A}\n💧 LIQUIDITY: $${O}\n💵 PRICE: $${h.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${y.isHoneypot?"HIGH":"LOW"}\n📉 Buy Tax: ${B}%\n📈 Sell Tax: ${M}%\n\n⚠️⚠️⚠️⚠️ ${y.honeypotReason||"No specific risks identified"}\n    `,I=yield s.default.findOne({chat_id:n}).exec();I&&(I.bscCurrentTokenName=h.name,I.bscCurrentContractAddress=a,yield I.save()),_[a]={totalSupply:b,marketCap:w,checkHoneypot:y,tokenInfo:h,resultMessage:N};const C=v[n];C&&(yield e.deleteMessage(n,C));const $=yield e.sendMessage(n,N,{parse_mode:"Markdown",reply_markup:(0,t.createBSCKeyboard)()});return v[n]=$.message_id,$.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}})),t.getSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_bsc"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_bsc"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_bsc"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_bsc"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_bsc"})}]]})),t.createBSCKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_bsc"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_bsc"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_bsc"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_bsc"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_bsc"})},{text:"💸 Spend X BSC",callback_data:JSON.stringify({command:"spend_bsc"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updateBSCMessageWithSellOptions=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getSellOptionsKeyboard)(),s=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.updateBSCMessageWithTradeOptions=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,f.createBscTradeKeyboard)();if(!(0,p.getCurrentContractAddress)(m.BlockchainType.BSC))return void console.error("No contract address found in global state.");yield e.editMessageReplyMarkup({inline_keyboard:r.inline_keyboard},{chat_id:t,message_id:n})}catch(e){console.error("Failed to update trade options:",e)}})),t.mainBscMenu=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createBSCKeyboard)(),s=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update BSC menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewBscWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield s.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,g.generateBSCWallet)();yield s.default.updateOne({chat_id:t},{$set:{"bsc_wallet.address":n.address,"bsc_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the BSC blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,u.handleBscDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let w={};function S(e,t,n,a,o){return r(this,void 0,void 0,(function*(){const r=n[a],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield s.default.findOneAndUpdate({chat_id:t},{bscCurrentContractAddress:r.contractAddress,bscCurrentTokenName:r.contractName}),(0,p.setCurrentContractAddress)(m.BlockchainType.BSC,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_bsc"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_bsc"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_bsc"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_bsc"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_bsc"})},{text:"Next",callback_data:JSON.stringify({command:"next_bsc"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}function T(e,t){return r(this,void 0,void 0,(function*(){const n=new l.ethers.providers.JsonRpcProvider("https://bsc-dataseed.binance.org/"),r=new l.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],n),[a,s]=yield Promise.all([r.balanceOf(t),r.decimals()]);return l.ethers.utils.formatUnits(a,s)}))}t.askForContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(w[t]){const n=w[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,y.INPUT_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return w[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),w[t]=Object.assign(Object.assign({},w[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForBSCToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(w[t]){const n=w[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,h.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return w[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received BSC token:",e.text),w[t]=Object.assign(Object.assign({},w[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleRefresh=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleBSCScanContract)(e,n,r),a){const t=a.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,p.getCurrentContractAddress)(m.BlockchainType.BSC);if(n){const r=`https://dexscreener.com/bsc/${n}`,a=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});w[t]={promptId:a.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");w[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendBSC=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,h.SET_BSC_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return w[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),w[t]=Object.assign(Object.assign({},w[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend BSC:",e),new Error("Failed to handle spend BSC action.")}})),t.handleBscSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield s.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.bsc_wallet,a=n.bscCurrentContractAddress||"";if(!a)throw new Error("No contract address found for user");const o=yield T(a,r),i=parseFloat(o);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=m.TraderFactory.getTrader(m.BlockchainType.BSC),c=yield d.sellTokens(a,100,r);if(c&&c.transactionHash&&c.blockNumber){const{transactionHash:n,blockNumber:r}=c;yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${n}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${n}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n),n instanceof Error&&n.message,yield e.sendMessage(t,"An error occurred while purchasing tokens: Possible causes are selling less than required amount and unstable network.")}})),t.sellBscTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:a}=r.bsc_wallet,o=r.bscCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=yield T(o,a),d=parseFloat(i);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");const c=d*(n/100);if(c<=0)throw new Error("Invalid percentage value");console.log("Amount to sell:",c);const l=m.TraderFactory.getTrader(m.BlockchainType.BSC),u=yield l.sellTokens(o,n,a);if(u&&u.transactionHash&&u.blockNumber){const{transactionHash:r,blockNumber:a}=u;yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${r}\n🧱 Block Number: ${a}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${r}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n),n instanceof Error&&n.message,yield e.sendMessage(t,"An error occurred while purchasing tokens: Possible causes are selling less than required amount and unstable network.")}})),t.getTokenDetails=e=>_[e]},8708:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.cacheService=t.CacheFactory=void 0;const a=n(1213);class s{constructor(){this.cache=new Map,this.cleanupInterval=setInterval((()=>this.cleanupExpiredItems()),3e5)}cleanupExpiredItems(){const e=Date.now();let t=0;for(const[n,r]of this.cache.entries())r.expiry&&r.expiry<e&&(this.cache.delete(n),t++);t>0&&a.logger.debug(`Cache cleanup: removed ${t} expired items`)}get(e){return r(this,void 0,void 0,(function*(){const t=this.cache.get(e);return t?t.expiry&&t.expiry<Date.now()?(this.cache.delete(e),null):t.value:null}))}set(e,t,n){return r(this,void 0,void 0,(function*(){const r=n?Date.now()+1e3*n:null;this.cache.set(e,{value:t,expiry:r})}))}del(e){return r(this,void 0,void 0,(function*(){this.cache.delete(e)}))}exists(e){return r(this,void 0,void 0,(function*(){const t=this.cache.get(e);return!(!t||t.expiry&&t.expiry<Date.now()&&(this.cache.delete(e),1))}))}flushAll(){return r(this,void 0,void 0,(function*(){this.cache.clear()}))}getMulti(e){return r(this,void 0,void 0,(function*(){return Promise.all(e.map((e=>this.get(e))))}))}setMulti(e){return r(this,void 0,void 0,(function*(){for(const t of e)yield this.set(t.key,t.value,t.ttlSeconds)}))}delMulti(e){return r(this,void 0,void 0,(function*(){for(const t of e)this.cache.delete(t)}))}shutdown(){clearInterval(this.cleanupInterval)}}class o{static getCache(){return this.instance||("true"===process.env.USE_REDIS?(a.logger.info("Redis cache is enabled in config but not fully implemented yet. Using in-memory cache instead."),this.instance=new s):(a.logger.info("Using in-memory cache"),this.instance=new s)),this.instance}static shutdown(){this.instance instanceof s&&this.instance.shutdown()}}t.CacheFactory=o,t.cacheService=o.getCache()},3868:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleCallbackQuery=void 0;const s=a(n(9896)),o=a(n(6928)),i=n(8491),d=a(n(8722)),c=a(n(6786)),l=a(n(8087)),u=n(4544),g=n(3173),y=n(3173),h=n(2893),p=n(4248),f=n(4259),m=n(8786),_=n(8485),v=n(871),b=n(1347),k=n(2724),w=n(9266),S=n(4035),T=n(8322),A=n(6314),O=n(439),E=n(4736),x=n(2836),B=n(9527),M=n(566),N=o.default.resolve(__dirname,"../previousValues.json");function I(e,t){var n;const r=JSON.parse(s.default.readFileSync(N,"utf-8"));for(const[a,s]of Object.entries(r)){const r=s;if((null===(n=r[t])||void 0===n?void 0:n.chat_id)===e)return{address:a,balance:r[t].balance}}return null}new i.Connection(process.env.SOL_PROVIDER_URL);let C=0;t.handleCallbackQuery=(e,t)=>r(void 0,void 0,void 0,(function*(){var n,a;const s=null===(n=t.message)||void 0===n?void 0:n.chat.id,o=null===(a=t.message)||void 0===a?void 0:a.message_id;if(!s||!o)return;const i=JSON.parse(t.data||"{}");switch(i.command){case"dismiss_message":yield $(e,s,o);break;case"show_config":yield(0,M.showConfigKeyboard)(e,s);break;case"view_bsc":case"view_base":case"view_sol":case"view_eth":yield P(e,s,i.command);break;case"generate_new_bsc_wallet":yield(0,y.handleGenerateNewBscWallet)(e,s);break;case"scan_contract_bsc":const n=yield(0,y.askForContractAddress)(e,s,o);n?yield(0,g.handleBSCScanContract)(e,s,n):yield e.sendMessage(s,"Invalid contract address.");break;case"scan_snipe_bsc":try{const t=yield(0,y.askForBSCToken)(e,s,o);yield d.default.updateOne({chat_id:s},{$set:{currentBlockchain:"bsc"}}),t?yield(0,h.bscSniperScreen)(e,s,t):yield e.sendMessage(s,"Invalid contract address.")}catch(t){console.error("Error during BSC token snipe:",t),yield e.sendMessage(s,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"snipetrade_dashboard_bsc":const a=(0,E.getCurrentContractAddress)(v.BlockchainType.BSC)||"";yield(0,_.tradeBscScreen)(e,s,a);break;case"sell_bsc":yield(0,y.updateBSCMessageWithSellOptions)(e,s,o);break;case"active_trades_bsc":yield(0,y.fetchAndDisplayActiveBscTrades)(e,s,"bsc");break;case"previous_bsc":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"bsc"}).exec();yield(0,y.displayBscCurrentTrade)(e,s,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_bsc":const u=yield c.default.find({isSold:!1,blockchain:"bsc"}).exec();C<u.length-1?(C++,yield(0,y.displayBscCurrentTrade)(e,s,u,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_bsc":yield(0,y.handleRefresh)(e,s);break;case"chart_bsc":yield(0,y.handleChart)(e,s);break;case"spend_bsc":try{const t=yield(0,y.handleSpendBSC)(e,s);if(t){const n=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const a=v.TraderFactory.getTrader(v.BlockchainType.BSC),s=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"});if(!s)throw new Error("Snipe configuration not found");const o=(null===(n=s.config.spend_bsc)||void 0===n?void 0:n.toString())||"0.1",i={slippage:s.config.slippage_bsc||5,gasLimit:1e6},c=yield a.buyTokens(t,o,r.bsc_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return{transactionHash:c.transactionHash,blockNumber:c.blockNumber,status:!0}}catch(e){throw e}}))}(s,t);if(n&&n.transactionHash&&n.blockNumber){const{transactionHash:t,blockNumber:r}=n;yield e.sendMessage(s,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${t}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${t}\nTo see your transaction`)}else yield e.sendMessage(s,"Error: Unable to get transaction details.")}else yield e.sendMessage(s,"Invalid contract address.")}catch(t){if(console.error("Error purchasing tokens:",t),t instanceof Error)if(t.message.includes("insufficient funds"))yield e.sendMessage(s,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again.");else if(t.message.includes("SERVER_ERROR")||t.message.includes("ECONNRESET"))yield e.sendMessage(s,"Error purchasing tokens: There was a problem processing your request. Please try again later.");else if(t.message.includes("body"))try{const n=JSON.parse(t.message);n.error&&n.error.message.includes("insufficient funds")?yield e.sendMessage(s,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again."):yield e.sendMessage(s,"An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.")}catch(t){yield e.sendMessage(s,"An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.")}else yield e.sendMessage(s,"An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.");else yield e.sendMessage(s,"An unknown error occurred while purchasing tokens. Please try again later.")}break;case"sell_all_bsc":yield(0,g.handleBscSellAll)(e,s);break;case"sell_25_bsc":yield(0,g.sellBscTokenPercentage)(e,s,25);break;case"sell_50_bsc":yield(0,g.sellBscTokenPercentage)(e,s,50);break;case"sell_75_bsc":yield(0,g.sellBscTokenPercentage)(e,s,75);break;case"refresh_wallet_bsc":try{const t=yield d.default.findOne({chat_id:s});if(t){const n=I(s,"bsc");if(n){const{address:r}=n,a=t.bsc_dashboard_message_id,o=t.bsc_dashboard_content,i=t.bsc_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your BSC Dashboard!\n                          \n📍 **Address:** \`${r}\`\n                          \n🔗 **Blockchain:** BSC\n                          \n💰 **Balance:** \`${c} BSC\`\n                         `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_bsc"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_bsc"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_bsc"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_bsc"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_bsc_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:s,message_id:a,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:s},{$set:{base_dashboard_content:l,base_dashboard_markup:u}}))}else yield e.sendMessage(s,"No wallet address found.")}else yield e.sendMessage(s,"User not found.")}catch(t){console.error("Failed to refresh wallet base:",t),yield e.sendMessage(s,"An error occurred while refreshing your wallet.")}break;case"generate_new_base_wallet":yield(0,k.handleGenerateNewBaseWallet)(e,s);break;case"snipetrade_dashboard_base":yield(0,w.tradeBaseScreen)(e,s,(0,E.getCurrentContractAddress)(v.BlockchainType.BASE)||"");break;case"scan_contract_base":const b=yield(0,k.askForBaseContractAddress)(e,s,o);b?yield(0,k.handleBASEScanContract)(e,s,b):yield e.sendMessage(s,"Invalid contract address.");break;case"scan_snipe_base":try{const t=yield(0,k.askForBaseToken)(e,s,o);yield d.default.updateOne({chat_id:s},{$set:{currentBlockchain:"base"}}),t?yield(0,m.baseSniperScreen)(e,s,t):yield e.sendMessage(s,"Invalid contract address.")}catch(t){console.error("Error during Base token snipe:",t),yield e.sendMessage(s,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"sell_base":yield(0,k.updatebaseMessageWithSellOptions)(e,s,o);break;case"back_base":yield(0,k.mainbaseMenu)(e,s,o);break;case"active_trades_base":yield(0,k.fetchAndDisplayActiveBaseTrades)(e,s,"base");break;case"previous_base":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"base"}).exec();yield(0,k.displayCurrentBaseTrade)(e,s,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_base":const T=yield c.default.find({isSold:!1,blockchain:"base"}).exec();C<T.length-1?(C++,yield(0,k.displayCurrentBaseTrade)(e,s,T,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_base":yield(0,k.handleBaseRefresh)(e,s);break;case"chart_base":yield(0,k.handleBaseChart)(e,s);break;case"spend_base":try{const t=yield(0,k.handleSpendBase)(e,s);if(t){const n=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const a=v.TraderFactory.getTrader(v.BlockchainType.BASE),s=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"base"});if(!s)throw new Error("Snipe configuration not found");const o=(null===(n=s.config.spend_base)||void 0===n?void 0:n.toString())||"0.1",i={slippage:s.config.slippage_base||5,gasLimit:1e6},c=yield a.buyTokens(t,o,r.base_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return{transactionHash:c.transactionHash,blockNumber:c.blockNumber,status:!0}}catch(e){throw e}}))}(s,t);if(n&&n.transactionHash&&n.blockNumber){const{transactionHash:t,blockNumber:r}=n;yield e.sendMessage(s,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${t}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${t}\nTo see your transaction`)}else yield e.sendMessage(s,"Error: Unable to get transaction details.")}else yield e.sendMessage(s,"Invalid contract address.")}catch(t){console.error("Error purchasing tokens:",t),t instanceof Error?t.message.includes("Insufficient BNB balance")?yield e.sendMessage(s,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost."):t.message.includes("SERVER_ERROR")||t.message.includes("ECONNRESET")?yield e.sendMessage(s,"Error purchasing tokens: There was a problem processing your request. Please try again later."):yield e.sendMessage(s,"An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network."):yield e.sendMessage(s,"An unknown error occurred while purchasing tokens. Please try again later.")}break;case"sell_all_base":yield(0,k.handleBaseSellAll)(e,s);break;case"sell_25_base":yield(0,k.sellBaseTokenPercentage)(e,s,25);break;case"sell_50_base":yield(0,k.sellBaseTokenPercentage)(e,s,50);break;case"sell_75_base":yield(0,k.sellBaseTokenPercentage)(e,s,75);break;case"refresh_wallet_base":try{const t=yield d.default.findOne({chat_id:s});if(t){const n=I(s,"base");if(n){const{address:r}=n,a=t.base_dashboard_message_id,o=t.base_dashboard_content,i=t.base_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your BASE Dashboard!\n                            \n📍 **Address:** \`${r}\`\n                            \n🔗 **Blockchain:** BASE\n                            \n💰 **Balance:** \`${c} Base ETH\`\n                           `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_base"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_base"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_base"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_base"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_base_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:s,message_id:a,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:s},{$set:{base_dashboard_content:l,base_dashboard_markup:u}}))}else yield e.sendMessage(s,"No wallet address found.")}else yield e.sendMessage(s,"User not found.")}catch(t){console.error("Failed to refresh wallet base:",t),yield e.sendMessage(s,"An error occurred while refreshing your wallet.")}break;case"generate_new_eth_wallet":yield(0,A.handleGenerateNewethWallet)(e,s);break;case"snipetrade_dashboard_eth":yield(0,x.tradeEthScreen)(e,s,(0,E.getCurrentContractAddress)(v.BlockchainType.ETH)||"");break;case"scan_contract_eth":const O=yield(0,A.askForEthContractAddress)(e,s,o);O?yield(0,A.handleETHScanContract)(e,s,O):yield e.sendMessage(s,"Invalid contract address.");break;case"scan_snipe_eth":try{const t=yield(0,A.askForEthToken)(e,s,o);yield d.default.updateOne({chat_id:s},{$set:{currentBlockchain:"eth"}}),t?yield(0,p.ethSniperScreen)(e,s,t):yield e.sendMessage(s,"Invalid contract address.")}catch(t){console.error("Error during Eth token snipe:",t),yield e.sendMessage(s,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"sell_eth":yield(0,A.updateEthMessageWithSellOptions)(e,s,o);break;case"trade_eth":yield(0,A.updateEthMessageWithTradeOptions)(e,s,o);break;case"back_eth":yield(0,A.mainEthMenu)(e,s,o);break;case"active_trades_eth":yield(0,A.fetchAndDisplayActiveEthTrades)(e,s,"eth");break;case"previous_eth":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"eth"}).exec();yield(0,A.displayCurrentEthTrade)(e,s,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_eth":const N=yield c.default.find({isSold:!1,blockchain:"eth"}).exec();C<N.length-1?(C++,yield(0,A.displayCurrentEthTrade)(e,s,N,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_eth":yield(0,A.handleEthRefresh)(e,s);break;case"chart_eth":yield(0,A.handleEthChart)(e,s);break;case"spend_eth":try{const t=yield(0,A.handleSpendeth)(e,s);if(t){const n=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const a=v.TraderFactory.getTrader(v.BlockchainType.ETH),s=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"});if(!s)throw new Error("Snipe configuration not found");const o=(null===(n=s.config.spend_eth)||void 0===n?void 0:n.toString())||"0.1",i={slippage:s.config.slippage_eth||5,gasLimit:1e6},c=yield a.buyTokens(t,o,r.eth_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return{transactionHash:c.transactionHash,blockNumber:c.blockNumber,status:!0}}catch(e){throw e}}))}(s,t);if(n&&n.transactionHash&&n.blockNumber){const{transactionHash:t,blockNumber:r}=n;yield e.sendMessage(s,`✅ Tokens successfully purchased:\n\n🔗 Transaction Hash: ${t}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.com/tx/${t}\nTo see your transaction`)}else yield e.sendMessage(s,"Error: Unable to get transaction details.")}else yield e.sendMessage(s,"Invalid contract address.")}catch(t){console.error("Error purchasing tokens:",t),t instanceof Error?t.message.includes("Insufficient Eth balance")?yield e.sendMessage(s,"Error purchasing tokens: Insufficient funds for the intrinsic transaction cost."):t.message.includes("SERVER_ERROR")||t.message.includes("ECONNRESET")?yield e.sendMessage(s,"Error purchasing tokens: There was a problem processing your request. Please try again later."):yield e.sendMessage(s,"An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network."):yield e.sendMessage(s,"An unknown error occurred while purchasing tokens. Please try again later.")}break;case"sell_all_eth":yield(0,A.handleEthSellAll)(e,s);break;case"sell_25_eth":yield(0,A.sellethTokenPercentage)(e,s,25);break;case"sell_50_eth":yield(0,A.sellethTokenPercentage)(e,s,50);break;case"sell_75_eth":yield(0,A.sellethTokenPercentage)(e,s,75);break;case"refresh_wallet_eth":try{const t=yield d.default.findOne({chat_id:s});if(t){const n=I(s,"eth");if(n){const{address:r}=n,a=t.eth_dashboard_message_id,o=t.eth_dashboard_content,i=t.eth_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your ETH Dashboard!\n                          \n📍 **Address:** \`${r}\`\n                          \n🔗 **Blockchain:** ETH\n                          \n💰 **Balance:** \`${c}  ETH\`\n                         `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_eth"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_eth"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_eth"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_eth"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_eth_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:s,message_id:a,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:s},{$set:{eth_dashboard_content:l,eth_dashboard_markup:u}}))}else yield e.sendMessage(s,"No wallet address found.")}else yield e.sendMessage(s,"User not found.")}catch(t){console.error("Failed to refresh wallet et:",t),yield e.sendMessage(s,"An error occurred while refreshing your wallet.")}break;case"generate_new_sol_wallet":yield(0,S.handleGenerateNewSolWallet)(e,s);break;case"snipetrade_dashboard_sol":yield(0,B.tradeSolScreen)(e,s,(0,E.getCurrentContractAddress)(v.BlockchainType.SOL)||"");break;case"scan_contract_sol":const F=yield(0,S.askForSolContractAddress)(e,s,o);F?yield(0,S.handleSOLScanContract)(e,s,F):yield e.sendMessage(s,"Invalid contract address.");break;case"scan_snipe_sol":try{const t=yield(0,S.askForSolToken)(e,s,o);yield d.default.updateOne({chat_id:s},{$set:{currentBlockchain:"sol"}}),t?yield(0,f.solSniperScreen)(e,s,t):yield e.sendMessage(s,"Invalid contract address.")}catch(t){console.error("Error during Sol token snipe:",t),yield e.sendMessage(s,`⚠️ Error: ${t instanceof Error?t.message:"Unknown error occurred."}`)}break;case"sell_sol":yield(0,S.updateSolMessageWithSellOptions)(e,s,o);break;case"trade_sol":yield(0,S.updateSolMessageWithTradeOptions)(e,s,o);break;case"back_sol":yield(0,S.mainSolMenu)(e,s,o);break;case"active_trades_sol":yield(0,S.fetchAndDisplayActiveSolTrades)(e,s,"sol");break;case"previous_sol":if(C>0){C--;const t=yield c.default.find({isSold:!1,blockchain:"sol"}).exec();yield(0,S.displayCurrentSolTrade)(e,s,t,C,o)}else yield e.answerCallbackQuery(t.id,{text:"This is the first trade."});break;case"next_sol":const D=yield c.default.find({isSold:!1,blockchain:"sol"}).exec();C<D.length-1?(C++,yield(0,S.displayCurrentSolTrade)(e,s,D,C,o)):yield e.answerCallbackQuery(t.id,{text:"This is the last trade."});break;case"refresh_sol":yield(0,S.handleSolRefresh)(e,s);break;case"chart_sol":yield(0,S.handleSolChart)(e,s);break;case"spend_sol":try{const t=yield(0,S.handleSpendsol)(e,s);if(!t||isNaN(Number(t)))throw new Error("Invalid amount.");const n=t,a=yield function(e,t){return r(this,void 0,void 0,(function*(){var n;try{const r=yield d.default.findOne({chat_id:e});if(!r)throw new Error("User not found");const a=v.TraderFactory.getTrader(v.BlockchainType.SOL),s=yield l.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"});if(!s)throw new Error("Snipe configuration not found");const o=(null===(n=s.config.spend_sol)||void 0===n?void 0:n.toString())||"0.1",i={slippage:s.config.slippage_sol||5},c=yield a.buyTokens(t,o,r.sol_wallet.address,i);if(!c.success)throw new Error(c.error||"Unknown error");return c.transactionHash||""}catch(e){throw e}}))}(s,n);a?yield e.sendMessage(s,`✅ Successfully purchased tokens:\n\n🔗 Transaction Hash: ${a}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${a}\nTo see your transaction`):yield e.sendMessage(s,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(t){console.error("Error purchasing tokens:",t),t instanceof Error&&t.message,yield e.sendMessage(s,"An error occurred while purchasing tokens}")}break;case"sell_all_sol":yield(0,S.handleSolSellAll)(e,s);break;case"sell_25_sol":yield(0,S.sellSolTokenPercentage)(e,s,25);break;case"sell_50_sol":yield(0,S.sellSolTokenPercentage)(e,s,50);break;case"sell_75_sol":yield(0,S.sellSolTokenPercentage)(e,s,75);break;case"refresh_wallet_sol":try{const t=yield d.default.findOne({chat_id:s});if(t){const n=I(s,"sol");if(n){const{address:r}=n,a=t.sol_dashboard_message_id,o=t.sol_dashboard_content,i=t.sol_dashboard_markup,c=Number(n.balance).toFixed(6),l=`👋 Welcome, ${t.first_name||"User"}, to your SOL Dashboard!\n                          \n📍 **Address:** \`${r}\`\n                          \n🔗 **Blockchain:** SOL\n                          \n💰 **Balance:** \`${c}  SOL\`\n                         `,u={inline_keyboard:[[{text:"🔍 Scan Contract",callback_data:JSON.stringify({command:"scan_contract_sol"})}],[{text:"⚙️ Config",callback_data:JSON.stringify({command:"config_sol"})},{text:"📊 Active Trades",callback_data:JSON.stringify({command:"active_trades_sol"})}],[{text:"🔄 Refresh Wallet",callback_data:JSON.stringify({command:"refresh_wallet_sol"})},{text:"➕ Generate New Wallet",callback_data:JSON.stringify({command:"generate_new_sol_wallet"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]};o===l&&JSON.stringify(i)===JSON.stringify(u)||(yield e.editMessageText(l,{chat_id:s,message_id:a,parse_mode:"Markdown",reply_markup:u}),yield d.default.updateOne({chat_id:s},{$set:{sol_dashboard_content:l,sol_dashboard_markup:u}}))}else yield e.sendMessage(s,"No wallet address found.")}else yield e.sendMessage(s,"User not found.")}catch(t){console.error("Failed to refresh wallet et:",t),yield e.sendMessage(s,"An error occurred while refreshing your wallet.")}}}));const $=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{yield e.deleteMessage(t,n)}catch(e){console.error("Failed to delete message:",e)}})),P=(e,t,n)=>r(void 0,void 0,void 0,(function*(){const r=n.split("_")[1];switch(yield d.default.updateOne({chat_id:t},{$set:{currentBlockchain:r}}),r){case"bsc":yield(0,u.handleBscDashboard)(e,t);break;case"base":yield(0,b.handleBaseDashboard)(e,t);break;case"eth":yield(0,O.handleEthDashboard)(e,t);break;case"sol":yield(0,T.handleSolDashboard)(e,t)}}))},9088:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.checkEVMHoneypot=function(e,t){return r(this,void 0,void 0,(function*(){try{o.logger.info(`Checking if ${e} is a honeypot on ${t}`,{contractAddress:e,blockchain:t});const n={isHoneypot:!1,buyTax:0,sellTax:0,isLiquidityLocked:!1};let r="";switch(t){case s.BlockchainType.BSC:r=process.env.BSC_PROVIDER_URL||"https://bsc-dataseed.binance.org/";break;case s.BlockchainType.ETH:r=process.env.ETH_PROVIDER_URL||"https://mainnet.infura.io/v3/your-infura-key";break;case s.BlockchainType.BASE:r=process.env.BASE_PROVIDER_URL||"https://mainnet.base.org";break;default:throw new Error(`Unsupported blockchain: ${t}`)}const i=new a.ethers.providers.JsonRpcProvider(r),d=yield i.getCode(e);return"0x"===d?(n.isHoneypot=!0,n.honeypotReason="Contract does not exist",n):d.includes("function transferFrom(address,address,uint256)")&&!d.includes("function transfer(address,uint256)")?(n.isHoneypot=!0,n.honeypotReason="Missing transfer function",n):d.includes("blacklist")||d.includes("_blacklist")?(n.isHoneypot=!0,n.honeypotReason="Contract contains blacklist functions",n):(d.includes("_buyTax")||d.includes("buyFee")||d.includes("buyTaxes"))&&(n.buyTax=10,n.buyTax>20)?(n.isHoneypot=!0,n.honeypotReason=`High buy tax: ${n.buyTax}%`,n):(d.includes("_sellTax")||d.includes("sellFee")||d.includes("sellTaxes"))&&(n.sellTax=10,n.sellTax>20)?(n.isHoneypot=!0,n.honeypotReason=`High sell tax: ${n.sellTax}%`,n):d.includes("onlyOwner")&&(d.includes("setMaxTxAmount")||d.includes("setMaxWalletSize"))?(n.isHoneypot=!0,n.honeypotReason="Owner can restrict trading",n):(o.logger.info(`Honeypot check completed for ${e}`,{contractAddress:e,blockchain:t,isHoneypot:n.isHoneypot,buyTax:n.buyTax,sellTax:n.sellTax}),n)}catch(n){return o.logger.error(`Error checking honeypot for ${e}`,{contractAddress:e,blockchain:t,error:n.message}),{isHoneypot:!1,honeypotReason:`Error checking honeypot: ${n.message}`}}}))};const a=n(9321),s=n(871),o=n(1213)},8904:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getLiquidityInfoFromDexscreener=void 0;const s=a(n(8938));t.getLiquidityInfoFromDexscreener=e=>r(void 0,void 0,void 0,(function*(){const t=`https://api.dexscreener.com/latest/dex/tokens/${e}`;try{const n=yield s.default.get(t);if(!n.data.pairs||0===n.data.pairs.length)return console.log(`No liquidity found for the token ${e}.`),{hasLiquidity:!1,message:`No liquidity found for the token ${e}.`};const r=n.data.pairs[0],{baseToken:a,priceNative:o,priceUsd:i,chainId:d,priceChange:c,dexId:l,totalSupply:u,liquidity:g}=r,y=a.symbol,h=a.name||"Unknown Token";return{hasLiquidity:!0,symbol:y,name:h,priceNative:o,priceUsd:i,chainId:d,priceChange:c,dexId:l,totalSupply:u,pairAddress:r.pairAddress||"Not available",marketCap:r.marketCap||"Not available",address:a.address}}catch(e){throw console.error("Error fetching token info from Dexscreener:",e),new Error("Error fetching token info from Dexscreener. Please try again later.")}}))},9558:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfoFromDexscreener=void 0;const a=n(6580),s=n(871),o=n(1213);t.getTokenInfoFromDexscreener=(e,...t)=>r(void 0,[e,...t],void 0,(function*(e,t=s.BlockchainType.BSC){var n;try{const r=yield(0,a.getTokenInfoFromDexscreener)(e,t);return{symbol:(null==r?void 0:r.symbol)||"UNKNOWN",name:(null==r?void 0:r.name)||"Unknown Token",priceNative:(null==r?void 0:r.priceUsd)||0,priceUsd:(null==r?void 0:r.priceUsd)||0,chainId:t,priceChange:(null==r?void 0:r.priceChange)||{h1:0,h24:0},dexId:(null==r?void 0:r.dexId)||"unknown",totalSupply:0,pairAddress:(null==r?void 0:r.pairAddress)||"Not available",marketCap:(null==r?void 0:r.fdv)||"Not available",liquidity:{usd:(null===(n=null==r?void 0:r.liquidity)||void 0===n?void 0:n.usd)||0},address:e}}catch(n){return o.logger.error("Error fetching token info from Dexscreener:",{error:n.message,contractAddress:e,blockchain:t}),{symbol:"UNKNOWN",name:"Unknown Token",priceNative:0,priceUsd:0,chainId:t,priceChange:{h1:0,h24:0},dexId:"unknown",totalSupply:0,pairAddress:"Not available",marketCap:"Not available",liquidity:{usd:0},address:e}}}))},6314:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleEthDashboard=t.getTokenDetails=t.sellethTokenPercentage=t.handleEthSellAll=t.handleSpendeth=t.handleEthChart=t.handleEthRefresh=t.askForEthToken=t.askForEthContractAddress=t.handleGenerateNewethWallet=t.mainEthMenu=t.updateEthMessageWithTradeOptions=t.updateEthMessageWithSellOptions=t.createethKeyboard=t.getEthTradeOptionsKeyboard=t.getEthSellOptionsKeyboard=t.handleETHScanContract=void 0,t.fetchAndDisplayActiveEthTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let a=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield w(e,t,r,a)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayCurrentEthTrade=w;const s=a(n(8722)),o=a(n(6786)),i=n(9088),d=n(9558),c=a(n(3883)),l=n(9321),u=n(439);Object.defineProperty(t,"handleEthDashboard",{enumerable:!0,get:function(){return u.handleEthDashboard}});const g=n(4653),y=n(8014),h=n(8014),p=n(4736),f=n(871);let m={},_={};const v=process.env.ETH_PROVIDER_URL,b=new l.ethers.providers.JsonRpcProvider(v);if(!v)throw new Error("PROVIDER is not defined in the environment variables.");t.handleETHScanContract=(e,n,a)=>r(void 0,void 0,void 0,(function*(){var o,u;try{if(!a)throw new Error("Invalid contract address.");const g=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);g&&m[g]&&delete m[g],m[a]=m[a]||{},(0,p.setCurrentContractAddress)(f.BlockchainType.ETH,a);const y=yield(0,i.checkEVMHoneypot)(a,f.BlockchainType.ETH),h=yield(0,d.getTokenInfoFromDexscreener)(a),v=yield(u=a,r(void 0,void 0,void 0,(function*(){try{const e=new l.ethers.Contract(u,c.default,b),t=yield e.decimals(),n=yield e.totalSupply();return l.ethers.utils.formatUnits(n,t)}catch(e){return console.error("Error fetching total supply:",e),null}})));if(null===v)throw new Error("Failed to fetch total supply.");const k=Number(v),w=Number(h.priceUsd)*k,S=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),T=S(k),A=S(w),O=S(Number(h.liquidity.usd)),E=(null===(o=h.priceChange)||void 0===o?void 0:o.h1)||0,x=E>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",B=Math.ceil(parseFloat(String(y.buyTax||0))),M=Math.ceil(parseFloat(String(y.sellTax||0))),N=`\n🪙 ${h.symbol} (${h.name}) || 📈 ${E>0?"+":""}${E}%  || 🏦 ${h.dexId} || ${h.chainId}\n\n${x}  Price Change: ${E>0?"+":""}${E}%\n\nCA: ${h.address}\nLP: ${h.pairAddress}\n\n💼 TOTAL SUPPLY: ${T} ${h.symbol}\n🏷️ MC: $${A}\n💧 LIQUIDITY: $${O}\n💵 PRICE: $${h.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${y.isHoneypot?"HIGH":"LOW"}\n📉 Buy Tax: ${B}%\n📈 Sell Tax: ${M}%\n\n⚠️⚠️⚠️⚠️ ${y.honeypotReason||"No specific risks identified"}\n    `,I=yield s.default.findOne({chat_id:n}).exec();I&&(I.ethCurrentTokenName=h.name,I.ethCurrentContractAddress=a,yield I.save()),m[a]={totalSupply:k,marketCap:w,checkHoneypot:y,tokenInfo:h,resultMessage:N};const C=_[n];C&&(yield e.deleteMessage(n,C));const $=yield e.sendMessage(n,N,{parse_mode:"Markdown",reply_markup:(0,t.createethKeyboard)()});return _[n]=$.message_id,$.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}})),t.getEthSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_eth"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_eth"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_eth"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_eth"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_eth"})}]]})),t.getEthTradeOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Trade Option 1",callback_data:JSON.stringify({command:"trade_option_1"})},{text:"Trade Option 2",callback_data:JSON.stringify({command:"trade_option_2"})}]]})),t.createethKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_eth"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_eth"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_eth"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_eth"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_eth"})},{text:"💸 Spend X eth",callback_data:JSON.stringify({command:"spend_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updateEthMessageWithSellOptions=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getEthSellOptionsKeyboard)(),s=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.updateEthMessageWithTradeOptions=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getEthTradeOptionsKeyboard)(),s=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update trade options:",e)}})),t.mainEthMenu=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createethKeyboard)(),s=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update eth menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewethWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield s.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,g.generateETHWallet)();yield s.default.updateOne({chat_id:t},{$set:{"eth_wallet.address":n.address,"eth_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the eth blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,u.handleEthDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let k={};function w(e,t,n,a,o){return r(this,void 0,void 0,(function*(){const r=n[a],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield s.default.findOneAndUpdate({chat_id:t},{ethCurrentContractAddress:r.contractAddress,ethCurrentTokenName:r.contractName}),(0,p.setCurrentContractAddress)(f.BlockchainType.ETH,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_eth"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_eth"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_eth"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_eth"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_eth"})},{text:"Next",callback_data:JSON.stringify({command:"next_eth"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}function S(e,t){return r(this,void 0,void 0,(function*(){const n=new l.ethers.Contract(e,["function balanceOf(address owner) view returns (uint256)","function decimals() view returns (uint8)"],b),[r,a]=yield Promise.all([n.balanceOf(t),n.decimals()]);return l.ethers.utils.formatUnits(r,a)}))}t.askForEthContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,y.INPUT_ETH_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForEthToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(k[t]){const n=k[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,y.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received Sol token:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleEthRefresh=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleETHScanContract)(e,n,r),a){const t=a.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleEthChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,p.getCurrentContractAddress)(f.BlockchainType.ETH);if(n){const r=`https://dexscreener.com/eth/${n}`,a=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});k[t]={promptId:a.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");k[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendeth=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,h.SET_ETH_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return k[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),k[t]=Object.assign(Object.assign({},k[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend eth:",e),new Error("Failed to handle spend eth action.")}})),t.handleEthSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield s.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.eth_wallet,a=n.ethCurrentContractAddress||"";if(!a)throw new Error("No contract address found for user");const o=yield S(a,r),i=parseFloat(o);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=f.TraderFactory.getTrader(f.BlockchainType.ETH),c=yield d.sellTokens(a,100,r);if(c&&c.transactionHash&&c.blockNumber){const{transactionHash:n,blockNumber:r}=c;yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${n}\n🧱 Block Number: ${r}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.com/tx/${n}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling all tokens: ${r}`)}})),t.sellethTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:a}=r.eth_wallet,o=r.ethCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=yield S(o,a),d=parseFloat(i);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");const c=d*(n/100);if(c<=0)throw new Error("Invalid percentage value");console.log("Amount to sell:",c);const l=f.TraderFactory.getTrader(f.BlockchainType.ETH),u=yield l.sellTokens(o,n,a);if(u&&u.transactionHash&&u.blockNumber){const{transactionHash:r,blockNumber:a}=u;yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${r}\n🧱 Block Number: ${a}\n\n👁‍🗨 Navigate to:\nhttps://etherscan.org/tx/${r}\nTo see your transaction`)}else yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling tokens: ${r}`)}})),t.getTokenDetails=e=>m[e]},2969:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.checkIfHoneypot=function(e,t){return r(this,void 0,void 0,(function*(){try{return t===o.BlockchainType.SOL?yield function(e){return r(this,void 0,void 0,(function*(){var t,n,r,a,o,l;const u=Date.now(),g=`honeypot:sol:${e}`;try{const y=yield d.cacheService.get(g);if(y)return i.logger.debug(`Using cached honeypot check for ${e} on SOL`,{tokenAddress:e,isHoneypot:y.isHoneypot}),y;const h=`https://api.rugcheck.xyz/v1/tokens/${e}/report/summary`,p=(yield s.default.get(h)).data;let f=[];const m=[];let _=!1;p.risks&&p.risks.length>0&&(f=p.risks.map((e=>("danger"===e.level?(_=!0,m.push(`DANGER: ${e.name} - ${e.description}`)):"high"===e.level&&m.push(`HIGH RISK: ${e.name} - ${e.description}`),`${e.name}: ${e.description} (${e.level})`)))),_&&m.unshift("CRITICAL: Honeypot detected - High risk token");const v=null!==(n=null===(t=p.simulationResult)||void 0===t?void 0:t.buyTax)&&void 0!==n?n:"0",b=null!==(a=null===(r=p.simulationResult)||void 0===r?void 0:r.sellTax)&&void 0!==a?a:"0",k=null!==(l=null===(o=p.simulationResult)||void 0===o?void 0:o.transferTax)&&void 0!==l?l:"0",w={isHoneypot:_,risk:p.score,buyTax:v,sellTax:b,transferTax:k,warnings:m,details:f.join("\n")||"No specific risks identified",timestamp:Date.now()};return yield d.cacheService.set(g,w,c),i.logger.logPerformance("SOL honeypot check",u,{tokenAddress:e,isHoneypot:w.isHoneypot,risk:w.risk}),w}catch(t){i.logger.error("Failed to check if SOL token is honeypot",{error:t.message,tokenAddress:e});const n={isHoneypot:!1,risk:"unknown",buyTax:"0",sellTax:"0",warnings:["Failed to check honeypot status"],details:`Error: ${t.message}`,timestamp:Date.now()};return yield d.cacheService.set(g,n,300),n}}))}(e):yield function(e,t){return r(this,void 0,void 0,(function*(){var n,r,a;const l=Date.now(),u=`honeypot:${t}:${e}`;try{const g=yield d.cacheService.get(u);if(g)return i.logger.debug(`Using cached honeypot check for ${e} on ${t}`,{tokenAddress:e,blockchain:t,isHoneypot:g.isHoneypot}),g;const y=function(e){switch(e){case o.BlockchainType.ETH:return"https://api.honeypot.is/v2/IsHoneypot";case o.BlockchainType.BSC:return"https://bsc.honeypot.is/v2/IsHoneypot";case o.BlockchainType.BASE:return"https://base.honeypot.is/v2/IsHoneypot";default:throw new Error(`Unsupported blockchain for honeypot check: ${e}`)}}(t),h=yield s.default.get(y,{params:{address:e}}),{summary:p,simulationResult:f}=h.data;let m=p.flags.map((e=>e.description));const _=[];"honeypot"===p.risk?_.push("CRITICAL: Honeypot detected - High risk token"):"unknown"===p.risk&&_.push("WARNING: Could not determine if this is a honeypot. Proceed with caution."),m.includes("The source code is not available, allowing for hidden functionality.")&&(_.push("WARNING: Contract's dependencies are closed source, allowing for hidden functionalities."),m=m.filter((e=>"The source code is not available, allowing for hidden functionality."!==e))),m=m.filter((e=>"All snipers are marked as honeypots (blacklisted). Sniper detection still needs some improvements."!==e));const v=null!==(n=null==f?void 0:f.buyTax)&&void 0!==n?n:"0",b=null!==(r=null==f?void 0:f.sellTax)&&void 0!==r?r:"0",k=null!==(a=null==f?void 0:f.transferTax)&&void 0!==a?a:"0",w={isHoneypot:"honeypot"===p.risk,risk:p.risk,buyTax:v,sellTax:b,transferTax:k,buyGas:null==f?void 0:f.buyGas,sellGas:null==f?void 0:f.sellGas,warnings:_,details:m.join("\n")||"No specific risks identified",timestamp:Date.now()};return yield d.cacheService.set(u,w,c),i.logger.logPerformance(`${t} honeypot check`,l,{tokenAddress:e,isHoneypot:w.isHoneypot,risk:w.risk}),w}catch(n){i.logger.error(`Failed to check if ${t} token is honeypot`,{error:n.message,tokenAddress:e,blockchain:t});const r={isHoneypot:!1,risk:"unknown",buyTax:"0",sellTax:"0",warnings:["Failed to check honeypot status"],details:`Error: ${n.message}`,timestamp:Date.now()};return yield d.cacheService.set(u,r,300),r}}))}(e,t)}catch(n){return i.logger.error("Failed to check if token is honeypot",{error:n.message,tokenAddress:e,blockchain:t}),{isHoneypot:!1,risk:"unknown",buyTax:"0",sellTax:"0",warnings:["Failed to check honeypot status"],details:`Error: ${n.message}`,timestamp:Date.now()}}}))},t.formatHoneypotResult=function(e){let t="";return e.isHoneypot?t+="🚨 **HONEYPOT DETECTED** 🚨\n\n":"unknown"===e.risk?t+="⚠️ **UNKNOWN RISK** ⚠️\n\n":t+="✅ **Not detected as a honeypot**\n\n",t+=`**Risk Level:** ${e.risk}\n`,t+=`**Buy Tax:** ${e.buyTax}%\n`,t+=`**Sell Tax:** ${e.sellTax}%\n`,e.transferTax&&(t+=`**Transfer Tax:** ${e.transferTax}%\n`),e.warnings.length>0&&(t+="\n**Warnings:**\n",e.warnings.forEach((e=>{t+=`- ${e}\n`}))),e.details&&"No specific risks identified"!==e.details&&(t+="\n**Details:**\n",t+=e.details),t};const s=a(n(8938)),o=n(871),i=n(1213),d=n(8708),c=3600},4545:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)},o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CacheFactory=t.cacheService=t.logger=void 0,t.initializeServices=function(e){Promise.resolve().then((()=>o(n(1213)))).then((({logger:t})=>{t.info("Initializing services..."),Promise.resolve().then((()=>o(n(8708)))).then((({cacheService:r})=>{t.info("Cache service initialized"),Promise.resolve().then((()=>o(n(1536)))).then((({initializeBlockchainServices:r})=>{r(),e&&Promise.resolve().then((()=>o(n(3003)))).then((({initializeTraders:n})=>{n(e),t.info("Trading services initialized")})),Promise.resolve().then((()=>o(n(7095)))).then((({initializeUtilityServices:e})=>{e(),t.info("All services initialized successfully")}))}))}))}))},t.shutdownServices=function(){Promise.resolve().then((()=>o(n(1213)))).then((({logger:e})=>{e.info("Shutting down services..."),Promise.resolve().then((()=>o(n(8708)))).then((({CacheFactory:t})=>{t.shutdown(),e.info("All services shut down successfully"),e.shutdown()}))}))};var i=n(1213);Object.defineProperty(t,"logger",{enumerable:!0,get:function(){return i.logger}});var d=n(8708);Object.defineProperty(t,"cacheService",{enumerable:!0,get:function(){return d.cacheService}}),Object.defineProperty(t,"CacheFactory",{enumerable:!0,get:function(){return d.CacheFactory}}),s(n(1536),t),s(n(871),t),s(n(3003),t),s(n(6580),t),s(n(2969),t),s(n(7095),t),s(n(3868),t)},1213:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.logger=t.Logger=t.LogLevel=void 0;const a=r(n(9896)),s=r(n(6928)),o=r(n(857)),i=r(n(9907));var d;!function(e){e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e[e.FATAL=4]="FATAL"}(d||(t.LogLevel=d={}));const c="[0m",l="[2m",u="[31m",g="[37m";class y{constructor(){var e,t;if(this.logBuffer=[],this.flushInterval=null,this.rotationInterval=null,this.logLevel=this.getLogLevelFromEnv(),this.logToFile="true"===process.env.LOG_TO_FILE,this.logFilePath=process.env.LOG_FILE_PATH||s.default.join(process.cwd(),"logs","app.log"),this.serviceName=process.env.SERVICE_NAME||"easybot",this.hostname=o.default.hostname(),this.workerId=(null===(t=null===(e=i.default.worker)||void 0===e?void 0:e.id)||void 0===t?void 0:t.toString())||"main",this.maxBufferSize=parseInt(process.env.LOG_BUFFER_SIZE||"100",10),this.flushIntervalMs=parseInt(process.env.LOG_FLUSH_INTERVAL_MS||"5000",10),this.rotateLogsIntervalHours=parseInt(process.env.LOG_ROTATION_HOURS||"24",10),this.maxLogFileSizeMb=parseInt(process.env.MAX_LOG_FILE_SIZE_MB||"10",10),this.maxLogFiles=parseInt(process.env.MAX_LOG_FILES||"5",10),this.metrics={startTime:Date.now(),logCount:{[d.DEBUG]:0,[d.INFO]:0,[d.WARN]:0,[d.ERROR]:0,[d.FATAL]:0},lastFlushTime:0,totalFlushes:0,totalWriteTime:0,maxBufferSize:0},this.logToFile){const e=s.default.dirname(this.logFilePath);a.default.existsSync(e)||a.default.mkdirSync(e,{recursive:!0})}this.flushInterval=setInterval((()=>this.flushLogs()),this.flushIntervalMs),this.logToFile&&(this.rotationInterval=setInterval((()=>this.rotateLogFiles()),60*this.rotateLogsIntervalHours*60*1e3)),this.info("Logger initialized",{level:d[this.logLevel],logToFile:this.logToFile,logFilePath:this.logFilePath,flushInterval:this.flushIntervalMs,maxBufferSize:this.maxBufferSize})}static getInstance(){return y.instance||(y.instance=new y),y.instance}getLogLevelFromEnv(){var e;switch(null===(e=process.env.LOG_LEVEL)||void 0===e?void 0:e.toUpperCase()){case"DEBUG":return d.DEBUG;case"INFO":default:return d.INFO;case"WARN":return d.WARN;case"ERROR":return d.ERROR;case"FATAL":return d.FATAL}}getLevelColor(e){switch(e){case d.DEBUG:return"[36m";case d.INFO:return"[32m";case d.WARN:return"[33m";case d.ERROR:return u;case d.FATAL:return"[41m"+g;default:return g}}formatLogEntryForConsole(e){const t=d[e.level],n=this.getLevelColor(e.level),r=e.timestamp.split("T")[1].replace("Z","");let a=`${l}${r}${c} ${n}[${t}]${c} ${e.message}`;if((e.service||e.workerId)&&(a+=` ${l}(${e.service||this.serviceName}:${e.workerId||this.workerId})${c}`),e.context)try{const t=JSON.stringify(e.context);"{}"!==t&&(a+=` ${l}${t}${c}`)}catch(e){a+=` ${u}[Context serialization failed]${c}`}return a}formatLogEntryForFile(e){const t=d[e.level];let n=`[${e.timestamp}] [${t}] [${e.service||this.serviceName}:${e.workerId||this.workerId}] ${e.message}`;if(e.context)try{const t=JSON.stringify(e.context);"{}"!==t&&(n+=` ${t}`)}catch(e){n+=" [Context serialization failed]"}return n}addToBuffer(e){this.logBuffer.push(e),this.metrics.logCount[e.level]++,this.metrics.maxBufferSize=Math.max(this.metrics.maxBufferSize,this.logBuffer.length),this.logBuffer.length>=this.maxBufferSize&&this.flushLogs()}flushLogs(){if(0===this.logBuffer.length)return;const e=Date.now();if(this.logToFile)try{const e=this.logBuffer.map((e=>this.formatLogEntryForFile(e)));a.default.appendFileSync(this.logFilePath,e.join("\n")+"\n"),this.checkLogFileSize()}catch(e){console.error(`${u}Failed to write to log file:${c}`,e)}this.metrics.lastFlushTime=Date.now(),this.metrics.totalFlushes++,this.metrics.totalWriteTime+=Date.now()-e,this.logBuffer=[]}checkLogFileSize(){try{a.default.statSync(this.logFilePath).size/1048576>=this.maxLogFileSizeMb&&this.rotateLogFiles()}catch(e){}}rotateLogFiles(){if(this.logToFile)try{if(!a.default.existsSync(this.logFilePath))return;const e=s.default.dirname(this.logFilePath),t=s.default.extname(this.logFilePath),n=s.default.basename(this.logFilePath,t),r=(new Date).toISOString().replace(/:/g,"-").replace(/\..+/,"");for(let r=this.maxLogFiles-1;r>=0;r--){const o=s.default.join(e,`${n}.${r}${t}`);if(a.default.existsSync(o))if(r===this.maxLogFiles-1)a.default.unlinkSync(o);else{const i=s.default.join(e,`${n}.${r+1}${t}`);a.default.renameSync(o,i)}}const o=s.default.join(e,`${n}.0${t}`);a.default.renameSync(this.logFilePath,o),a.default.writeFileSync(this.logFilePath,`Log file created at ${r}\n`),this.info("Log file rotated",{oldFile:o,newFile:this.logFilePath})}catch(e){console.error(`${u}Failed to rotate log files:${c}`,e)}}log(e,t,n){if(e<this.logLevel)return;const r={timestamp:(new Date).toISOString(),level:e,message:t,context:n,service:this.serviceName,workerId:this.workerId,hostname:this.hostname};console.log(this.formatLogEntryForConsole(r)),this.logToFile&&this.addToBuffer(r)}debug(e,t){this.log(d.DEBUG,e,t)}info(e,t){this.log(d.INFO,e,t)}warn(e,t){this.log(d.WARN,e,t)}error(e,t){this.log(d.ERROR,e,t)}fatal(e,t){this.log(d.FATAL,e,t)}logPerformance(e,t,n){const r=Date.now()-t;this.debug(`Performance: ${e} completed in ${r}ms`,Object.assign(Object.assign({},n),{operation:e,durationMs:r,timestamp:(new Date).toISOString()}))}getMetrics(){const e=Date.now()-this.metrics.startTime,t=this.metrics.totalFlushes>0?this.metrics.totalWriteTime/this.metrics.totalFlushes:0;return{uptime:e,uptimeHuman:this.formatUptime(e),logCounts:this.metrics.logCount,totalLogs:Object.values(this.metrics.logCount).reduce(((e,t)=>e+t),0),flushes:this.metrics.totalFlushes,avgFlushTimeMs:t,maxBufferSize:this.metrics.maxBufferSize,currentBufferSize:this.logBuffer.length}}formatUptime(e){const t=Math.floor(e/1e3),n=Math.floor(t/60),r=Math.floor(n/60);return`${Math.floor(r/24)}d ${r%24}h ${n%60}m ${t%60}s`}shutdown(){this.flushInterval&&(clearInterval(this.flushInterval),this.flushInterval=null),this.rotationInterval&&(clearInterval(this.rotationInterval),this.rotationInterval=null),this.flushLogs(),this.info("Logger shutdown",this.getMetrics())}}t.Logger=y,t.logger=y.getInstance()},6325:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.createSession=void 0;const s=a(n(8722));let o={};t.createSession=(e,t)=>r(void 0,void 0,void 0,(function*(){const n=t.chat.id;if(o[n])yield e.sendMessage(n,"You are already logged in.");else{const t=yield s.default.findOne({chat_id:n}).exec();if(t){const r=t.currentBlockchain,a=t[r]||[];o[n]={session:a,currentTradeIndex:0,telegramId:t.chat_id,userId:t._id,trades:[]},yield e.sendMessage(n,`Session created. You are now logged in to your ${r.toUpperCase()} wallet.`)}else yield e.sendMessage(n,"User not found. Please register first.")}}))},4035:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.handleSolDashboard=t.getTokenDetails=t.sellSolTokenPercentage=t.handleSolSellAll=t.handleSpendsol=t.handleSolChart=t.handleSolRefresh=t.askForSolToken=t.askForSolContractAddress=t.handleGenerateNewSolWallet=t.mainSolMenu=t.updateSolMessageWithTradeOptions=t.updateSolMessageWithSellOptions=t.createSolKeyboard=t.getsolTradeOptionsKeyboard=t.getSolSellOptionsKeyboard=t.getSolBalance=t.handleSOLScanContract=void 0,t.getSolTokenBalance=_,t.fetchAndDisplayActiveSolTrades=function(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield o.default.find({chatId:t,isSold:!1,blockchain:n}).exec();let a=0;if(0===r.length)return void(yield e.sendMessage(t,"No active trades found!"));yield k(e,t,r,a)}catch(n){console.error("Error fetching trades:",n),yield e.sendMessage(t,"An error occurred while fetching trades.")}}))},t.displayCurrentSolTrade=k;const s=a(n(8722)),o=a(n(6786)),i=n(9558),d=n(8322);Object.defineProperty(t,"handleSolDashboard",{enumerable:!0,get:function(){return d.handleSolDashboard}});const c=n(4653),l=n(8014),u=n(8014),g=n(4736),y=n(871),h=n(8491);let p={},f={};const m=process.env.SOL_PROVIDER_URL;if(!m)throw new Error("PROVIDER_URL is not defined in the environment variables.");function _(e,t,n){return r(this,void 0,void 0,(function*(){try{const r=yield e.getParsedTokenAccountsByOwner(new h.PublicKey(t),{mint:new h.PublicKey(n)});if(0===r.value.length)throw new Error("Token account not found");const a=r.value[0].account.data.parsed.info.tokenAmount.uiAmount;return console.log("Balance (using Solana-Web3.js): ",a),a}catch(e){throw console.error("Error fetching token balance:",e),e}}))}new h.Connection(m),t.handleSOLScanContract=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{if(!a)throw new Error("Invalid contract address.");const o=(0,g.getCurrentContractAddress)(y.BlockchainType.SOL);o&&p[o]&&delete p[o],p[a]=p[a]||{},(0,g.setCurrentContractAddress)(y.BlockchainType.SOL,a);const d=y.TraderFactory.getTrader(y.BlockchainType.SOL),c=yield d.checkHoneypot(a),l=yield(0,i.getTokenInfoFromDexscreener)(a,y.BlockchainType.SOL),u=new h.Connection(process.env.SOL_PROVIDER_URL),m=yield((e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=new h.PublicKey(t),r=yield e.getParsedAccountInfo(n);if(!r||!r.value||r.value.data instanceof Buffer)throw new Error("Could not find mint account");const a=r.value.data.parsed.info,s=a.supply,o=a.decimals;return s/Math.pow(10,o)}catch(e){throw console.error(`Error fetching token total supply: ${e}`),e}})))(u,a);if(null===m)throw new Error("Failed to fetch total supply.");const _=m,v=l.priceUsd*_,b=e=>e>=1e12?(e/1e12).toFixed(2)+"T":e>=1e9?(e/1e9).toFixed(2)+"B":e>=1e6?(e/1e6).toFixed(2)+"M":e>=1e3?(e/1e3).toFixed(2)+"K":e.toFixed(2),k=b(_),w=b(v),S=b(l.liquidity.usd),T=l.priceChange.h1||0,A=T>0?"🟢🟢🟢🟢🟢🟢":"🔴🔴🔴🔴🔴🔴",O=Math.ceil(parseFloat(c.buyTax)),E=Math.ceil(parseFloat(c.sellTax)),x=`\n🪙 ${l.symbol} (${l.name}) || 📈 ${T>0?"+":""}${T}%  || 🏦 ${l.dexId} || ${l.chainId}\n\n${A}  Price Change: ${T>0?"+":""}${T}%\n\nCA: ${l.address}\nLP: ${l.pairAddress}\n\n💼 TOTAL SUPPLY: ${k} ${l.symbol}\n🏷️ MC: $${w}\n💧 LIQUIDITY: $${S}\n💵 PRICE: $${l.priceUsd}\n\n🔍 Honeypot/Rugpull Risk: ${c.risk}\n📉 Buy Tax: ${O}%\n📈 Sell Tax: ${E}%\n\n⚠️⚠️⚠️⚠️ ${c.riskDescriptions}\n      `,B=yield s.default.findOne({chat_id:n}).exec();B&&(B.solCurrentTokenName=l.name,B.solCurrentContractAddress=a,yield B.save()),p[a]={totalSupply:k,marketCap:v,checkHoneypot:c,tokenInfo:l,resultMessage:x};const M=f[n];M&&(yield e.deleteMessage(n,M));const N=yield e.sendMessage(n,x,{parse_mode:"Markdown",reply_markup:(0,t.createSolKeyboard)()});return f[n]=N.message_id,N.message_id}catch(t){console.error("Error scanning contract:",t),yield e.sendMessage(n,"There was an error scanning the contract. Please try again later.")}})),t.getSolBalance=e=>r(void 0,void 0,void 0,(function*(){const t=process.env.SOL_PROVIDER_URL;if(!t)throw new Error("Provider is not defined in the .env file");const n=new h.Connection(t);try{const t=new h.PublicKey(e);return((yield n.getBalance(t))/h.LAMPORTS_PER_SOL).toFixed(6)}catch(e){throw console.error("Error fetching balance:",e),new Error("Unable to fetch balance for Solana wallet.")}})),t.getSolSellOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_sol"})},{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_sol"})}],[{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_sol"})},{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_sol"})}],[{text:"Back",callback_data:JSON.stringify({command:"back_sol"})}]]})),t.getsolTradeOptionsKeyboard=()=>r(void 0,void 0,void 0,(function*(){return[[{text:"Trade Option 1",callback_data:JSON.stringify({command:"trade_option_1"})},{text:"Trade Option 2",callback_data:JSON.stringify({command:"trade_option_2"})}]]})),t.createSolKeyboard=()=>({inline_keyboard:[[{text:"◀️ Previous",callback_data:JSON.stringify({command:"previous_sol"})},{text:"▶️ Next",callback_data:JSON.stringify({command:"next_sol"})}],[{text:"🔄 Refresh",callback_data:JSON.stringify({command:"refresh_sol"})},{text:"💸 Sell",callback_data:JSON.stringify({command:"sell_sol"})}],[{text:"📈 Chart",callback_data:JSON.stringify({command:"chart_sol"})},{text:"💸 Spend X sol",callback_data:JSON.stringify({command:"spend_sol"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}),t.updateSolMessageWithSellOptions=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getSolSellOptionsKeyboard)(),s=(0,g.getCurrentContractAddress)(y.BlockchainType.SOL);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update sell options:",e)}})),t.updateSolMessageWithTradeOptions=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=yield(0,t.getsolTradeOptionsKeyboard)(),s=(0,g.getCurrentContractAddress)(y.BlockchainType.SOL);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r}})}catch(e){console.error("Failed to update trade options:",e)}})),t.mainSolMenu=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,t.createSolKeyboard)(),s=(0,g.getCurrentContractAddress)(y.BlockchainType.SOL);if(!s)return void console.error("No contract address found in global state.");const o=(0,t.getTokenDetails)(s);if(!o||!o.resultMessage)return void console.error("No result message found for the token address.");yield e.editMessageText(o.resultMessage,{chat_id:n,message_id:a,parse_mode:"Markdown",reply_markup:{inline_keyboard:r.inline_keyboard}})}catch(t){console.error("Failed to update sol menu:",t),yield e.sendMessage(n,"Failed to update the menu. Please try again later.")}})),t.handleGenerateNewSolWallet=(e,t)=>r(void 0,void 0,void 0,(function*(){try{if(!(yield s.default.findOne({chat_id:t})))return void(yield e.sendMessage(t,"User not found."));const n=(0,c.generateSOLWallet)();yield s.default.updateOne({chat_id:t},{$set:{"sol_wallet.address":n.address,"sol_wallet.private_key":n.privateKey}}),yield e.sendMessage(t,`✅ You clicked "Generate New Wallet" in the sol blockchain.\nNew wallet address: \`\`\`${n.address}\`\`\`\nNew wallet Private Key: \`\`\`${n.privateKey}\`\`\``),yield(0,d.handleSolDashboard)(e,t)}catch(n){console.error("Failed to generate new wallet:",n),yield e.sendMessage(t,"There was an error generating a new wallet. Please try again later.")}}));let v={};function b(e){return r(this,arguments,void 0,(function*(e,t=100){try{const n=yield s.default.findOne({chat_id:e});if(!n)throw new Error("User not found");const r=(0,g.getCurrentContractAddress)(y.BlockchainType.SOL);if(!r)throw new Error("No contract address found");const a=y.TraderFactory.getTrader(y.BlockchainType.SOL),o={slippage:5},i=yield a.sellTokens(r,t,n.sol_wallet.address,o);if(!i.success)throw new Error(i.error||"Unknown error");return i.transactionHash||""}catch(e){throw e}}))}function k(e,t,n,a,o){return r(this,void 0,void 0,(function*(){const r=n[a],i=`\n<b>Contract:</b> ${r.contractName} (${r.contractAddress})\n<b>Wallet:</b> ${r.walletId}\n<b>Buy Amount:</b> ${r.buyAmount}  <b>Sell Amount:</b> ${r.sellAmount}\n<b>Sold:</b> ${r.isSold?"Yes":"No"}\n  `;yield s.default.findOneAndUpdate({chat_id:t},{solCurrentContractAddress:r.contractAddress,solCurrentTokenName:r.contractName}),(0,g.setCurrentContractAddress)(y.BlockchainType.SOL,r.contractAddress);const d={parse_mode:"HTML",disable_web_page_preview:!0,reply_markup:{inline_keyboard:[[{text:"Sell 25%",callback_data:JSON.stringify({command:"sell_25_sol"})},{text:"Sell 50%",callback_data:JSON.stringify({command:"sell_50_sol"})}],[{text:"Sell 75%",callback_data:JSON.stringify({command:"sell_75_sol"})},{text:"Sell All",callback_data:JSON.stringify({command:"sell_all_sol"})}],[{text:"Previous",callback_data:JSON.stringify({command:"previous_sol"})},{text:"Next",callback_data:JSON.stringify({command:"next_sol"})}],[{text:"* Dismiss message",callback_data:JSON.stringify({command:"dismiss_message"})}]]}};o?yield e.editMessageText(i,Object.assign({chat_id:t,message_id:o},d)):yield e.sendMessage(t,i,d)}))}t.askForSolContractAddress=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(v[t]){const n=v[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,l.INPUT_SOL_CONTRACT_ADDRESS,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return v[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received contract address:",e.text),v[t]=Object.assign(Object.assign({},v[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid contract address."))}))))}))}catch(e){throw console.error("Failed to ask for contract address:",e),e}})),t.askForSolToken=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{if(v[t]){const n=v[t];if(n.promptId)try{yield e.deleteMessage(t,n.promptId)}catch(e){console.error("Failed to delete previous prompt message:",e)}if(n.replyId)try{yield e.deleteMessage(t,n.replyId)}catch(e){console.error("Failed to delete previous reply message:",e)}}const n=yield e.sendMessage(t,l.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return v[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received Sol token:",e.text),v[t]=Object.assign(Object.assign({},v[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid BSC token."))}))))}))}catch(e){throw console.error("Failed to ask for BSC token:",e),e}})),t.handleSolRefresh=(e,n,a)=>r(void 0,void 0,void 0,(function*(){try{const r=(0,g.getCurrentContractAddress)(y.BlockchainType.SOL);if(!r)throw new Error("No contract address is currently set.");if(yield(0,t.handleSOLScanContract)(e,n,r),a){const t=a.id;yield e.answerCallbackQuery(t,{text:"Token Refresh Successfully!",show_alert:!0})}}catch(t){console.error("Failed to refresh contract details:",t),yield e.sendMessage(n,"Failed to refresh contract details. Please try again later.")}})),t.handleSolChart=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=(0,g.getCurrentContractAddress)(y.BlockchainType.SOL);if(n){const r=`https://dexscreener.com/solana/${n}`,a=yield e.sendMessage(t,`<a href="${r}">Click here to view the chart on Dexscreener</a>`,{parse_mode:"HTML"});v[t]={promptId:a.message_id}}else{const n=yield e.sendMessage(t,"Error: Current contract address is not set.");v[t]={promptId:n.message_id}}}catch(e){console.error("Failed to handle chart action:",e)}})),t.handleSpendsol=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield e.sendMessage(t,u.SET_SOL_SPEND,{parse_mode:"HTML",reply_markup:{force_reply:!0}});return v[t]={promptId:n.message_id},new Promise(((a,s)=>{e.onReplyToMessage(t,n.message_id,(e=>r(void 0,void 0,void 0,(function*(){e.text?(console.log("Received buy amount:",e.text),v[t]=Object.assign(Object.assign({},v[t]),{replyId:e.message_id}),a(e.text)):s(new Error("Invalid buy amount."))}))))}))}catch(e){throw console.error("Error handling spend sol:",e),new Error("Failed to handle spend sol action.")}})),t.handleSolSellAll=(e,t)=>r(void 0,void 0,void 0,(function*(){try{const n=yield s.default.findOne({chat_id:t}).exec();if(!n)throw new Error("User not found");const{address:r}=n.sol_wallet,a=n.solCurrentContractAddress||"";if(!a)throw new Error("No contract address found for user");const o=new h.Connection(process.env.SOL_PROVIDER_URL),i=yield _(o,r,a);if(isNaN(i)||i<=0)throw new Error("Invalid token balance");const d=yield b(t,100);d?yield e.sendMessage(t,`✅ Successfully sold all your tokens:\n\n🔗 Transaction Hash: ${d}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${d}\nTo see your transaction`):yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling all tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling all tokens: ${r}`)}})),t.sellSolTokenPercentage=(e,t,n)=>r(void 0,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chat_id:t}).exec();if(!r)throw new Error("User not found");const{address:a}=r.sol_wallet,o=r.solCurrentContractAddress;if(!o)throw new Error("No contract address found for user");const i=new h.Connection(process.env.SOL_PROVIDER_URL),d=yield _(i,a,o);if(isNaN(d)||d<=0)throw new Error("Insufficient token balance");if(d*(n/100)<=0)throw new Error("Invalid percentage value");console.log("Percentage to sell:",n);const c=yield b(t,n);c?yield e.sendMessage(t,`✅ Successfully sold ${n}% of your tokens:\n\n🔗 Transaction Hash: ${c}\n\n👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${c}\nTo see your transaction`):yield e.sendMessage(t,"⚠️ Transaction failed. Please check your wallet and try again.")}catch(n){console.error("Error selling tokens:",n);const r=n instanceof Error?n.message:"An unknown error occurred";yield e.sendMessage(t,`An error occurred while selling tokens: ${r}`)}})),t.getTokenDetails=e=>p[e]},6580:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getTokenInfoFromDexscreener=u,t.getLiquidityInfo=function(e,t){return r(this,void 0,void 0,(function*(){var n,r;const a=Date.now(),s=`liquidity:${t}:${e}`;try{const o=yield d.cacheService.get(s);if(o)return i.logger.debug(`Using cached liquidity info for ${e} on ${t}`,{tokenAddress:e,blockchain:t}),o;const c=yield u(e,t);if(!c){const e={hasLiquidity:!1,liquidityUsd:0};return yield d.cacheService.set(s,e,l),e}const g=(null===(n=c.liquidity)||void 0===n?void 0:n.usd)||0,y=g>0,h={hasLiquidity:y,liquidityUsd:g,pairAddress:c.pairAddress,dexId:c.dexId,priceUsd:c.priceUsd,volume24h:null===(r=c.volume)||void 0===r?void 0:r.h24,priceChange:c.priceChange};return yield d.cacheService.set(s,h,l),i.logger.logPerformance("Liquidity info",a,{tokenAddress:e,blockchain:t,hasLiquidity:y,liquidityUsd:g}),h}catch(n){return i.logger.error(`Failed to get liquidity info for ${e} on ${t}`,{error:n.message,tokenAddress:e,blockchain:t}),{hasLiquidity:!1,liquidityUsd:0}}}))},t.getComprehensiveTokenInfo=function(e,t){return r(this,void 0,void 0,(function*(){const n=Date.now(),r=`token:${t}:${e}`;try{const a=yield d.cacheService.get(r);if(a)return i.logger.debug(`Using cached comprehensive token info for ${e} on ${t}`,{tokenAddress:e,blockchain:t}),a;const s=yield u(e,t),o=yield(0,c.checkIfHoneypot)(e,t),g={name:(null==s?void 0:s.name)||"Unknown",symbol:(null==s?void 0:s.symbol)||"UNKNOWN",decimals:18,address:e,blockchain:t,price:(null==s?void 0:s.priceUsd)||0,marketCap:(null==s?void 0:s.fdv)||0,isHoneypot:o.isHoneypot,honeypotInfo:o};return yield d.cacheService.set(r,g,l),i.logger.logPerformance("Comprehensive token info",n,{tokenAddress:e,blockchain:t}),g}catch(n){return i.logger.error(`Failed to get comprehensive token info for ${e} on ${t}`,{error:n.message,tokenAddress:e,blockchain:t}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:t}}}))},t.formatTokenInfo=function(e){let t="";return t+="**Token Information**\n\n",t+=`**Name:** ${e.name}\n`,t+=`**Symbol:** ${e.symbol}\n`,t+=`**Address:** \`${e.address}\`\n`,t+=`**Blockchain:** ${e.blockchain}\n`,e.price&&(t+=`**Price:** $${e.price.toFixed(8)}\n`),e.marketCap&&(t+=`**Market Cap:** $${e.marketCap.toLocaleString()}\n`),void 0!==e.isHoneypot&&(t+=`**Honeypot:** ${e.isHoneypot?"🚨 YES":"✅ NO"}\n`),t};const s=a(n(8938)),o=n(871),i=n(1213),d=n(8708),c=n(2969),l=300;function u(e,t){return r(this,void 0,void 0,(function*(){var n,r,a,c;const u=Date.now(),g=`dexscreener:${t}:${e}`;try{const y=yield d.cacheService.get(g);if(y)return i.logger.debug(`Using cached token info for ${e} on ${t}`,{tokenAddress:e,blockchain:t}),y;const h=function(e){switch(e){case o.BlockchainType.ETH:return"ethereum";case o.BlockchainType.BSC:return"bsc";case o.BlockchainType.SOL:return"solana";case o.BlockchainType.BASE:return"base";default:throw new Error(`Unsupported blockchain: ${e}`)}}(t),p=`https://api.dexscreener.com/latest/dex/tokens/${e}`,f=(yield s.default.get(p)).data;if(!f.pairs||0===f.pairs.length)return i.logger.warn(`No pairs found for token ${e} on ${t}`,{tokenAddress:e,blockchain:t}),null;const m=f.pairs.find((e=>e.chainId===h));if(!m)return i.logger.warn(`No pair found for token ${e} on ${t}`,{tokenAddress:e,blockchain:t,availableChains:f.pairs.map((e=>e.chainId))}),null;const _={name:m.baseToken.name,symbol:m.baseToken.symbol,priceUsd:parseFloat(m.priceUsd||"0"),liquidity:{usd:(null===(n=m.liquidity)||void 0===n?void 0:n.usd)||0},fdv:m.fdv||0,volume:{h24:(null===(r=m.volume)||void 0===r?void 0:r.h24)||0},priceChange:{h1:(null===(a=m.priceChange)||void 0===a?void 0:a.h1)||0,h24:(null===(c=m.priceChange)||void 0===c?void 0:c.h24)||0},pairAddress:m.pairAddress,dexId:m.dexId,chainId:m.chainId};return yield d.cacheService.set(g,_,l),i.logger.logPerformance("Dexscreener token info",u,{tokenAddress:e,blockchain:t}),_}catch(n){return i.logger.error(`Failed to get token info from Dexscreener for ${e} on ${t}`,{error:n.message,tokenAddress:e,blockchain:t}),null}}))}},6086:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.BaseNetworkTrader=t.BASE_CONFIG=void 0;const s=n(9321),o=n(871),i=n(1213),d=n(4653),c=n(9088),l=n(9558),u=a(n(3883)),g=a(n(8938));t.BASE_CONFIG={rpcUrl:process.env.BASE_PROVIDER_URL||"https://mainnet.base.org",routerAddress:process.env.BASE_ROUTER_ADDRESS||"******************************************",wethAddress:process.env.BASE_WETH_ADDRESS||"******************************************",explorerUrl:"https://basescan.org",gasLimit:3e5,defaultSlippage:10,apiUrl:process.env.BASENODE_API_URL||"https://base.api.0x.org/swap/v1/quote"};class y extends o.BaseTrader{constructor(e){super(e),this.provider=new s.ethers.providers.JsonRpcProvider(t.BASE_CONFIG.rpcUrl),this.apiKey=process.env.MASTER_KEY||"",i.logger.info("Base Network Trader initialized",{rpcUrl:t.BASE_CONFIG.rpcUrl})}getBlockchainType(){return o.BlockchainType.BASE}buyTokens(e,n,a,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Buying tokens on Base",{tokenAddress:e,amount:n,walletAddress:a,options:o});try{const d=yield this.getPrivateKey(a);if(!d)throw new Error("Private key not found for wallet");const c=new s.ethers.Wallet(d,this.provider),l=s.ethers.utils.parseEther(n),u="******************************************",y=e,h=((null==o?void 0:o.slippage)||t.BASE_CONFIG.defaultSlippage)/100,p=process.env.EVM_ADMIN_ADDRESS||"",f=.08,m=`buyToken=${y}&sellToken=${u}&sellAmount=${l.toString()}&slippagePercentage=${h}&takerAddress=${a}&feeRecipient=${p}&buyTokenPercentageFee=${f}`,_=`${t.BASE_CONFIG.apiUrl}?${m}`,v=(yield g.default.get(_,{headers:{"0x-api-key":this.apiKey}})).data,b={to:v.to,data:v.data,value:s.ethers.BigNumber.from(v.value),gasLimit:(null==o?void 0:o.gasLimit)?s.ethers.BigNumber.from(o.gasLimit):s.ethers.BigNumber.from(t.BASE_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?s.ethers.utils.parseUnits(o.gasPrice,"gwei"):s.ethers.utils.parseUnits("0.05","gwei")},k=yield c.sendTransaction(b),w=yield k.wait();return i.logger.info("Base buy transaction successful",{transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toString(),tokenAddress:e}),i.logger.logPerformance("Base buyTokens",r,{tokenAddress:e,transactionHash:w.transactionHash}),{success:!0,transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toNumber(),effectiveGasPrice:w.effectiveGasPrice.toString(),amountIn:n,amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("Base buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:a}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,a,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Selling tokens on Base",{tokenAddress:e,percentage:n,walletAddress:a,options:o});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const d=yield this.getPrivateKey(a);if(!d)throw new Error("Private key not found for wallet");const c=new s.ethers.Wallet(d,this.provider),l=new s.ethers.Contract(e,u.default,c),y=yield l.balanceOf(a),h=yield l.decimals(),p=y.mul(n).div(100);if(p.isZero())throw new Error("No tokens to sell");const f=process.env.EVM_SPENDER_ADDRESS||"";if((yield l.allowance(a,f)).lt(p)){const t=yield l.approve(f,s.ethers.constants.MaxUint256);yield t.wait(),i.logger.info("Approved spender to spend tokens",{tokenAddress:e,walletAddress:a,transactionHash:t.hash})}const m=e,_="******************************************",v=((null==o?void 0:o.slippage)||t.BASE_CONFIG.defaultSlippage)/100,b=process.env.EVM_ADMIN_ADDRESS||"",k=.08,w=`buyToken=${_}&sellToken=${m}&sellAmount=${p.toString()}&slippagePercentage=${v}&takerAddress=${a}&feeRecipient=${b}&buyTokenPercentageFee=${k}`,S=`${t.BASE_CONFIG.apiUrl}?${w}`,T=(yield g.default.get(S,{headers:{"0x-api-key":this.apiKey}})).data,A={to:T.to,data:T.data,value:s.ethers.BigNumber.from(T.value||0),gasLimit:(null==o?void 0:o.gasLimit)?s.ethers.BigNumber.from(o.gasLimit):s.ethers.BigNumber.from(t.BASE_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?s.ethers.utils.parseUnits(o.gasPrice,"gwei"):s.ethers.utils.parseUnits("0.05","gwei")},O=yield c.sendTransaction(A),E=yield O.wait();return i.logger.info("Base sell transaction successful",{transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toString(),tokenAddress:e,percentage:n}),i.logger.logPerformance("Base sellTokens",r,{tokenAddress:e,transactionHash:E.transactionHash,percentage:n}),{success:!0,transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toNumber(),effectiveGasPrice:E.effectiveGasPrice.toString(),amountIn:s.ethers.utils.formatUnits(p,h),amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("Base sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:a,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=new s.ethers.Contract(e,u.default,this.provider),[n,r,a,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),d=yield(0,l.getTokenInfoFromDexscreener)(e,o.BlockchainType.BASE),c=yield this.checkHoneypot(e);return{name:n,symbol:r,decimals:a,totalSupply:s.ethers.utils.formatUnits(i,a),address:e,blockchain:o.BlockchainType.BASE,price:"string"==typeof(null==d?void 0:d.priceUsd)?parseFloat(d.priceUsd):(null==d?void 0:d.priceUsd)||0,marketCap:"string"==typeof(null==d?void 0:d.marketCap)?parseFloat(d.marketCap):(null==d?void 0:d.marketCap)||0,isHoneypot:(null==c?void 0:c.isHoneypot)||!1,honeypotInfo:c}}catch(t){return i.logger.error("Failed to get Base token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:o.BlockchainType.BASE}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.provider.getBalance(e);return s.ethers.utils.formatEther(t)}catch(t){return i.logger.error("Failed to get Base wallet balance",{error:t.message,walletAddress:e}),"0"}}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{const n=new s.ethers.Contract(e,u.default,this.provider),r=yield n.balanceOf(t),a=yield n.decimals();return s.ethers.utils.formatUnits(r,a)}catch(n){return i.logger.error("Failed to get Base token balance",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,c.checkEVMHoneypot)(e,o.BlockchainType.BASE)}catch(t){return i.logger.error("Failed to check if Base token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,d.generateBASEWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:o.BlockchainType.BASE})}catch(e){throw i.logger.error("Failed to generate Base wallet",{error:e.message}),e}}))}sendMessage(e,t){return r(this,void 0,void 0,(function*(){try{yield this.bot.sendMessage(e,t,{parse_mode:"Markdown"})}catch(e){console.error("Failed to send message:",e)}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}}t.BaseNetworkTrader=y},1583:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.BscTrader=t.BSC_CONFIG=void 0;const s=n(9321),o=n(871),i=n(1213),d=n(4653),c=n(9088),l=n(9558),u=a(n(3883)),g=a(n(3455));t.BSC_CONFIG={rpcUrl:process.env.BSC_PROVIDER_URL||"https://bsc-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3",routerAddress:process.env.BSC_ROUTER_ADDRESS||"******************************************",wbnbAddress:process.env.BSC_WBNB_ADDRESS||"******************************************",explorerUrl:"https://bscscan.com",gasLimit:3e5,defaultSlippage:12};class y extends o.BaseTrader{constructor(e){super(e),this.fallbackProviders=[];const{providerManager:r}=n(5488),a=r.getProvider("bsc");this.provider=a||new s.ethers.providers.JsonRpcProvider(t.BSC_CONFIG.rpcUrl),this.fallbackProviders=r.getAllProviders("bsc"),i.logger.info("BSC Trader initialized",{rpcUrl:t.BSC_CONFIG.rpcUrl,fallbackCount:this.fallbackProviders.length})}getBlockchainType(){return o.BlockchainType.BSC}executeWithFallback(e){return r(this,void 0,void 0,(function*(){try{return yield e(this.provider)}catch(t){i.logger.warn("Primary BSC provider failed, trying fallbacks",{error:t.message});for(const t of this.fallbackProviders)try{return yield e(t)}catch(e){i.logger.debug("Fallback BSC provider failed",{error:e.message})}throw t}}))}buyTokens(e,n,a,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Buying tokens on BSC",{tokenAddress:e,amount:n,walletAddress:a,options:o});try{const d=yield this.getPrivateKey(a);if(!d)throw new Error("Private key not found for wallet");const c=new s.ethers.Wallet(d,this.provider),l=new s.ethers.Contract(t.BSC_CONFIG.routerAddress,g.default,c),u=s.ethers.utils.parseEther(n),y=[t.BSC_CONFIG.wbnbAddress,e],h=(null==o?void 0:o.slippage)||t.BSC_CONFIG.defaultSlippage,[,p]=yield l.getAmountsOut(u,y),f=p.mul(100-h).div(100),m=(null==o?void 0:o.gasLimit)||t.BSC_CONFIG.gasLimit,_=(null==o?void 0:o.gasPrice)?s.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice(),v=Math.floor(Date.now()/1e3)+1200,b=yield l.swapExactETHForTokensSupportingFeeOnTransferTokens(f,y,a,v,{value:u,gasLimit:m,gasPrice:_}),k=yield b.wait();return i.logger.info("BSC buy transaction successful",{transactionHash:k.transactionHash,blockNumber:k.blockNumber,gasUsed:k.gasUsed.toString(),tokenAddress:e}),i.logger.logPerformance("BSC buyTokens",r,{tokenAddress:e,transactionHash:k.transactionHash}),{success:!0,transactionHash:k.transactionHash,blockNumber:k.blockNumber,gasUsed:k.gasUsed.toNumber(),effectiveGasPrice:k.effectiveGasPrice.toString(),amountIn:n,amountOut:s.ethers.utils.formatUnits(f,18),timestamp:Date.now()}}catch(t){return i.logger.error("BSC buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:a}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,a,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Selling tokens on BSC",{tokenAddress:e,percentage:n,walletAddress:a,options:o});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const d=yield this.getPrivateKey(a);if(!d)throw new Error("Private key not found for wallet");const c=new s.ethers.Wallet(d,this.provider),l=new s.ethers.Contract(e,u.default,c),y=new s.ethers.Contract(t.BSC_CONFIG.routerAddress,g.default,c),h=yield l.balanceOf(a),p=yield l.decimals(),f=h.mul(n).div(100);if(f.isZero())throw new Error("No tokens to sell");if((yield l.allowance(a,t.BSC_CONFIG.routerAddress)).lt(f)){const n=yield l.approve(t.BSC_CONFIG.routerAddress,s.ethers.constants.MaxUint256);yield n.wait(),i.logger.info("Approved router to spend tokens",{tokenAddress:e,walletAddress:a,transactionHash:n.hash})}const m=[e,t.BSC_CONFIG.wbnbAddress],_=(null==o?void 0:o.slippage)||t.BSC_CONFIG.defaultSlippage,[,v]=yield y.getAmountsOut(f,m),b=v.mul(100-_).div(100),k=(null==o?void 0:o.gasLimit)||t.BSC_CONFIG.gasLimit,w=(null==o?void 0:o.gasPrice)?s.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice(),S=Math.floor(Date.now()/1e3)+1200,T=yield y.swapExactTokensForETHSupportingFeeOnTransferTokens(f,b,m,a,S,{gasLimit:k,gasPrice:w}),A=yield T.wait();return i.logger.info("BSC sell transaction successful",{transactionHash:A.transactionHash,blockNumber:A.blockNumber,gasUsed:A.gasUsed.toString(),tokenAddress:e,percentage:n}),i.logger.logPerformance("BSC sellTokens",r,{tokenAddress:e,transactionHash:A.transactionHash,percentage:n}),{success:!0,transactionHash:A.transactionHash,blockNumber:A.blockNumber,gasUsed:A.gasUsed.toNumber(),effectiveGasPrice:A.effectiveGasPrice.toString(),amountIn:s.ethers.utils.formatUnits(f,p),amountOut:s.ethers.utils.formatEther(b),timestamp:Date.now()}}catch(t){return i.logger.error("BSC sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:a,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=new s.ethers.Contract(e,u.default,this.provider),[n,r,a,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),d=yield(0,l.getTokenInfoFromDexscreener)(e,o.BlockchainType.BSC),c=yield this.checkHoneypot(e);return{name:n,symbol:r,decimals:a,totalSupply:s.ethers.utils.formatUnits(i,a),address:e,blockchain:o.BlockchainType.BSC,price:"string"==typeof(null==d?void 0:d.priceUsd)?parseFloat(d.priceUsd):(null==d?void 0:d.priceUsd)||0,marketCap:"string"==typeof(null==d?void 0:d.marketCap)?parseFloat(d.marketCap):(null==d?void 0:d.marketCap)||0,isHoneypot:(null==c?void 0:c.isHoneypot)||!1,honeypotInfo:c}}catch(t){return i.logger.error("Failed to get BSC token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:o.BlockchainType.BSC}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.executeWithFallback((t=>t.getBalance(e)));return s.ethers.utils.formatEther(t)}catch(t){return i.logger.error("Failed to get BSC wallet balance after trying all providers",{error:t.message,walletAddress:e}),"0"}}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{return yield this.executeWithFallback((n=>r(this,void 0,void 0,(function*(){const r=new s.ethers.Contract(e,u.default,n),a=yield r.balanceOf(t),o=yield r.decimals();return s.ethers.utils.formatUnits(a,o)}))))}catch(n){return i.logger.error("Failed to get BSC token balance after trying all providers",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,c.checkEVMHoneypot)(e,o.BlockchainType.BSC)}catch(t){return i.logger.error("Failed to check if BSC token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,d.generateBSCWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:o.BlockchainType.BSC})}catch(e){throw i.logger.error("Failed to generate BSC wallet",{error:e.message}),e}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}}t.BscTrader=y},5512:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.EthTrader=t.ETH_CONFIG=void 0;const s=n(9321),o=n(871),i=n(1213),d=n(4653),c=n(9088),l=n(9558),u=a(n(3883)),g=a(n(8938));t.ETH_CONFIG={rpcUrl:process.env.ETH_PROVIDER_URL||"https://mainnet.infura.io/v3/********************************",routerAddress:process.env.ETH_ROUTER_ADDRESS||"******************************************",wethAddress:process.env.ETH_WETH_ADDRESS||"******************************************",explorerUrl:"https://etherscan.io",gasLimit:35e4,defaultSlippage:10,apiUrl:process.env.ETHNODE_API_URL||"https://api.0x.org/swap/v1/quote"};class y extends o.BaseTrader{constructor(e){super(e),this.provider=new s.ethers.providers.JsonRpcProvider(t.ETH_CONFIG.rpcUrl),this.apiKey=process.env.MASTER_KEY||"",i.logger.info("ETH Trader initialized",{rpcUrl:t.ETH_CONFIG.rpcUrl})}getBlockchainType(){return o.BlockchainType.ETH}buyTokens(e,n,a,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Buying tokens on Ethereum",{tokenAddress:e,amount:n,walletAddress:a,options:o});try{const d=yield this.getPrivateKey(a);if(!d)throw new Error("Private key not found for wallet");const c=new s.ethers.Wallet(d,this.provider),l=s.ethers.utils.parseEther(n),u="******************************************",y=e,h=((null==o?void 0:o.slippage)||t.ETH_CONFIG.defaultSlippage)/100,p=process.env.EVM_ADMIN_ADDRESS||"",f=.08,m=`buyToken=${y}&sellToken=${u}&sellAmount=${l.toString()}&slippagePercentage=${h}&takerAddress=${a}&feeRecipient=${p}&buyTokenPercentageFee=${f}`,_=`${t.ETH_CONFIG.apiUrl}?${m}`,v=(yield g.default.get(_,{headers:{"0x-api-key":this.apiKey}})).data,b={to:v.to,data:v.data,value:s.ethers.BigNumber.from(v.value),gasLimit:(null==o?void 0:o.gasLimit)?s.ethers.BigNumber.from(o.gasLimit):s.ethers.BigNumber.from(t.ETH_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?s.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice()},k=yield c.sendTransaction(b),w=yield k.wait();return i.logger.info("ETH buy transaction successful",{transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toString(),tokenAddress:e}),i.logger.logPerformance("ETH buyTokens",r,{tokenAddress:e,transactionHash:w.transactionHash}),{success:!0,transactionHash:w.transactionHash,blockNumber:w.blockNumber,gasUsed:w.gasUsed.toNumber(),effectiveGasPrice:w.effectiveGasPrice.toString(),amountIn:n,amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("ETH buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:a}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,a,o){return r(this,void 0,void 0,(function*(){const r=Date.now();i.logger.info("Selling tokens on Ethereum",{tokenAddress:e,percentage:n,walletAddress:a,options:o});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const d=yield this.getPrivateKey(a);if(!d)throw new Error("Private key not found for wallet");const c=new s.ethers.Wallet(d,this.provider),l=new s.ethers.Contract(e,u.default,c),y=yield l.balanceOf(a),h=yield l.decimals(),p=y.mul(n).div(100);if(p.isZero())throw new Error("No tokens to sell");const f=process.env.EVM_SPENDER_ADDRESS||"";if((yield l.allowance(a,f)).lt(p)){const t=yield l.approve(f,s.ethers.constants.MaxUint256);yield t.wait(),i.logger.info("Approved spender to spend tokens",{tokenAddress:e,walletAddress:a,transactionHash:t.hash})}const m=e,_="******************************************",v=((null==o?void 0:o.slippage)||t.ETH_CONFIG.defaultSlippage)/100,b=process.env.EVM_ADMIN_ADDRESS||"",k=.08,w=`buyToken=${_}&sellToken=${m}&sellAmount=${p.toString()}&slippagePercentage=${v}&takerAddress=${a}&feeRecipient=${b}&buyTokenPercentageFee=${k}`,S=`${t.ETH_CONFIG.apiUrl}?${w}`,T=(yield g.default.get(S,{headers:{"0x-api-key":this.apiKey}})).data,A={to:T.to,data:T.data,value:s.ethers.BigNumber.from(T.value||0),gasLimit:(null==o?void 0:o.gasLimit)?s.ethers.BigNumber.from(o.gasLimit):s.ethers.BigNumber.from(t.ETH_CONFIG.gasLimit),gasPrice:(null==o?void 0:o.gasPrice)?s.ethers.utils.parseUnits(o.gasPrice,"gwei"):yield this.provider.getGasPrice()},O=yield c.sendTransaction(A),E=yield O.wait();return i.logger.info("ETH sell transaction successful",{transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toString(),tokenAddress:e,percentage:n}),i.logger.logPerformance("ETH sellTokens",r,{tokenAddress:e,transactionHash:E.transactionHash,percentage:n}),{success:!0,transactionHash:E.transactionHash,blockNumber:E.blockNumber,gasUsed:E.gasUsed.toNumber(),effectiveGasPrice:E.effectiveGasPrice.toString(),amountIn:s.ethers.utils.formatUnits(p,h),amountOut:"Unknown",timestamp:Date.now()}}catch(t){return i.logger.error("ETH sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:a,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=new s.ethers.Contract(e,u.default,this.provider),[n,r,a,i]=yield Promise.all([t.name(),t.symbol(),t.decimals(),t.totalSupply()]),d=yield(0,l.getTokenInfoFromDexscreener)(e,o.BlockchainType.ETH),c=yield this.checkHoneypot(e);return{name:n,symbol:r,decimals:a,totalSupply:s.ethers.utils.formatUnits(i,a),address:e,blockchain:o.BlockchainType.ETH,price:"string"==typeof(null==d?void 0:d.priceUsd)?parseFloat(d.priceUsd):(null==d?void 0:d.priceUsd)||0,marketCap:"string"==typeof(null==d?void 0:d.marketCap)?parseFloat(d.marketCap):(null==d?void 0:d.marketCap)||0,isHoneypot:(null==c?void 0:c.isHoneypot)||!1,honeypotInfo:c}}catch(t){return i.logger.error("Failed to get ETH token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:18,address:e,blockchain:o.BlockchainType.ETH}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.provider.getBalance(e);return s.ethers.utils.formatEther(t)}catch(t){return i.logger.error("Failed to get ETH wallet balance",{error:t.message,walletAddress:e}),"0"}}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{const n=new s.ethers.Contract(e,u.default,this.provider),r=yield n.balanceOf(t),a=yield n.decimals();return s.ethers.utils.formatUnits(r,a)}catch(n){return i.logger.error("Failed to get ETH token balance",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,c.checkEVMHoneypot)(e,o.BlockchainType.ETH)}catch(t){return i.logger.error("Failed to check if ETH token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,d.generateETHWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:o.BlockchainType.ETH})}catch(e){throw i.logger.error("Failed to generate ETH wallet",{error:e.message}),e}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}}t.EthTrader=y},9497:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.SolTrader=t.SOL_CONFIG=void 0;const s=n(8491),o=n(7018),i=n(871),d=n(1213),c=n(4653),l=n(2969),u=n(9558),g=a(n(8574)),y=a(n(6713));t.SOL_CONFIG={rpcUrl:process.env.SOL_PROVIDER_URL||"https://api.mainnet-beta.solana.com",adminPublicKey:process.env.SOL_ADMIN_PUBLIC_KEY||"",solanaAddress:"So11111111111111111111111111111111111111112",explorerUrl:"https://solscan.io",defaultSlippage:100,jupiterApiUrl:"https://quote-api.jup.ag/v6"};class h extends i.BaseTrader{constructor(e){super(e),this.connection=new s.Connection(t.SOL_CONFIG.rpcUrl),d.logger.info("SOL Trader initialized",{rpcUrl:t.SOL_CONFIG.rpcUrl})}getBlockchainType(){return i.BlockchainType.SOL}buyTokens(e,n,a,o){return r(this,void 0,void 0,(function*(){const r=Date.now();d.logger.info("Buying tokens on Solana",{tokenAddress:e,amount:n,walletAddress:a,options:o});try{const i=yield this.getPrivateKey(a);if(!i)throw new Error("Private key not found for wallet");const c=g.default.decode(i),l=s.Keypair.fromSecretKey(c),u=BigInt(Math.round(parseFloat(n)*s.LAMPORTS_PER_SOL)),y=.01*parseFloat(n),h=(BigInt(Math.round(y*s.LAMPORTS_PER_SOL)),(null==o?void 0:o.slippage)?100*o.slippage:t.SOL_CONFIG.defaultSlippage),p=yield this.getJupiterQuote(t.SOL_CONFIG.solanaAddress,e,u,h),f=yield this.getJupiterSwapTransaction(p,l.publicKey.toString()),m=Buffer.from(f,"base64"),_=s.VersionedTransaction.deserialize(m);t.SOL_CONFIG.adminPublicKey&&d.logger.info("Would add fee transfer instruction",{feeAmount:y,adminPublicKey:t.SOL_CONFIG.adminPublicKey}),_.sign([l]);const v=yield this.executeTransaction(_);return d.logger.info("SOL buy transaction successful",{transactionHash:v,tokenAddress:e}),d.logger.logPerformance("SOL buyTokens",r,{tokenAddress:e,transactionHash:v}),{success:!0,transactionHash:v,amountIn:n,timestamp:Date.now()}}catch(t){return d.logger.error("SOL buy transaction failed",{error:t.message,tokenAddress:e,walletAddress:a}),{success:!1,error:t.message,timestamp:Date.now()}}}))}sellTokens(e,n,a,i){return r(this,void 0,void 0,(function*(){const r=Date.now();d.logger.info("Selling tokens on Solana",{tokenAddress:e,percentage:n,walletAddress:a,options:i});try{if(n<=0||n>100)throw new Error("Percentage must be between 1 and 100");const c=yield this.getPrivateKey(a);if(!c)throw new Error("Private key not found for wallet");const l=g.default.decode(c),u=s.Keypair.fromSecretKey(l),y=yield this.getTokenBalance(e,a);if("0"===y)throw new Error("No token balance found");const h=new s.PublicKey(e),p=(yield(0,o.getMint)(this.connection,h)).decimals,f=parseFloat(y),m=BigInt(Math.floor(f*Math.pow(10,p)*n/100));if(m<=BigInt(0))throw new Error("No tokens to sell");const _=.01*Number(m),v=m-BigInt(Math.floor(_)),b=(null==i?void 0:i.slippage)?100*i.slippage:t.SOL_CONFIG.defaultSlippage,k=yield this.getJupiterQuote(e,t.SOL_CONFIG.solanaAddress,v,b),w=yield this.getJupiterSwapTransaction(k,u.publicKey.toString()),S=Buffer.from(w,"base64"),T=s.VersionedTransaction.deserialize(S);t.SOL_CONFIG.adminPublicKey&&d.logger.info("Would add fee transfer instruction",{feeAmountInTokens:_,adminPublicKey:t.SOL_CONFIG.adminPublicKey}),T.sign([u]);const A=yield this.executeTransaction(T);return d.logger.info("SOL sell transaction successful",{transactionHash:A,tokenAddress:e,percentage:n}),d.logger.logPerformance("SOL sellTokens",r,{tokenAddress:e,transactionHash:A,percentage:n}),{success:!0,transactionHash:A,amountIn:m.toString(),timestamp:Date.now()}}catch(t){return d.logger.error("SOL sell transaction failed",{error:t.message,tokenAddress:e,walletAddress:a,percentage:n}),{success:!1,error:t.message,timestamp:Date.now()}}}))}getTokenInfo(e){return r(this,void 0,void 0,(function*(){try{const t=yield this.connection.getTokenSupply(new s.PublicKey(e)),n=yield(0,u.getTokenInfoFromDexscreener)(e,i.BlockchainType.SOL),r=yield this.checkHoneypot(e);return{name:(null==n?void 0:n.name)||"Unknown",symbol:(null==n?void 0:n.symbol)||"UNKNOWN",decimals:t.value.decimals,totalSupply:t.value.amount,address:e,blockchain:i.BlockchainType.SOL,price:"string"==typeof(null==n?void 0:n.priceUsd)?parseFloat(n.priceUsd):(null==n?void 0:n.priceUsd)||0,marketCap:"string"==typeof(null==n?void 0:n.marketCap)?parseFloat(n.marketCap):(null==n?void 0:n.marketCap)||0,isHoneypot:(null==r?void 0:r.isHoneypot)||!1,honeypotInfo:r}}catch(t){return d.logger.error("Failed to get SOL token info",{error:t.message,tokenAddress:e}),{name:"Unknown",symbol:"UNKNOWN",decimals:9,address:e,blockchain:i.BlockchainType.SOL}}}))}getWalletBalance(e){return r(this,void 0,void 0,(function*(){try{const t=new s.PublicKey(e);return((yield this.connection.getBalance(t))/s.LAMPORTS_PER_SOL).toFixed(9)}catch(t){return d.logger.error("Failed to get SOL wallet balance",{error:t.message,walletAddress:e}),"0"}}))}checkHoneypot(e){return r(this,void 0,void 0,(function*(){try{return yield(0,l.checkIfHoneypot)(e,i.BlockchainType.SOL)}catch(t){return d.logger.error("Failed to check if SOL token is honeypot",{error:t.message,tokenAddress:e}),{isHoneypot:!1,message:"Failed to check"}}}))}generateWallet(){return r(this,void 0,void 0,(function*(){try{const e=(0,c.generateSOLWallet)(),t=yield this.getWalletBalance(e.address);return Object.assign(Object.assign({},e),{balance:t,blockchain:i.BlockchainType.SOL})}catch(e){throw d.logger.error("Failed to generate SOL wallet",{error:e.message}),e}}))}getPrivateKey(e){return r(this,void 0,void 0,(function*(){return null}))}getJupiterQuote(e,n,a,s){return r(this,void 0,void 0,(function*(){const r=yield(0,y.default)(`${t.SOL_CONFIG.jupiterApiUrl}/quote?inputMint=${e}&outputMint=${n}&amount=${a}&slippageBps=${s}`),o=yield r.json();if(!o)throw new Error("Failed to get quote data from Jupiter");return o}))}getJupiterSwapTransaction(e,n){return r(this,void 0,void 0,(function*(){const r=yield(0,y.default)(`${t.SOL_CONFIG.jupiterApiUrl}/swap`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({quoteResponse:e,userPublicKey:n,wrapAndUnwrapSol:!0,dynamicComputeUnitLimit:!0,prioritizationFeeLamports:2e6,autoMultiplier:2})}),a=yield r.json();if(!a||!a.swapTransaction)throw new Error("Failed to get swap transaction data from Jupiter");return a.swapTransaction}))}executeTransaction(e){return r(this,void 0,void 0,(function*(){const t=e.serialize(),n=yield this.connection.sendRawTransaction(t,{skipPreflight:!0,maxRetries:3}),r=yield this.connection.confirmTransaction(n,"confirmed");if(r.value.err)throw new Error(`Transaction failed: ${r.value.err.toString()}`);return n}))}getTokenBalance(e,t){return r(this,void 0,void 0,(function*(){try{const n=new s.PublicKey(t),r=new s.PublicKey(e),a=yield this.connection.getParsedTokenAccountsByOwner(n,{mint:r});if(0===a.value.length)return"0";const o=a.value[0].account.data.parsed.info,i=Number(o.tokenAmount.amount),d=o.tokenAmount.decimals;return(i/Math.pow(10,d)).toString()}catch(n){return d.logger.error("Failed to get token balance",{error:n.message,tokenAddress:e,walletAddress:t}),"0"}}))}}t.SolTrader=h},871:function(e,t){var n,r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTrader=t.TraderFactory=t.BlockchainType=void 0,function(e){e.BSC="bsc",e.ETH="eth",e.SOL="sol",e.BASE="base"}(n||(t.BlockchainType=n={}));class a{static registerTrader(e){this.traders.set(e.getBlockchainType(),e)}static getTrader(e){const t=this.traders.get(e);if(!t)throw new Error(`No trader registered for blockchain: ${e}`);return t}static hasTrader(e){return this.traders.has(e)}}t.TraderFactory=a,a.traders=new Map,t.BaseTrader=class{constructor(e){this.bot=e}sendMessage(e,t){return r(this,void 0,void 0,(function*(){try{yield this.bot.sendMessage(e,t,{parse_mode:"Markdown"})}catch(e){console.error("Failed to send message:",e)}}))}}},3003:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.initializeTraders=function(e){try{const t=new o.BscTrader(e),n=new i.EthTrader(e),r=new d.SolTrader(e),a=new c.BaseNetworkTrader(e);s.TraderFactory.registerTrader(t),s.TraderFactory.registerTrader(n),s.TraderFactory.registerTrader(r),s.TraderFactory.registerTrader(a),l.logger.info("All blockchain traders initialized and registered")}catch(e){throw l.logger.error("Failed to initialize traders",{error:e.message}),e}},t.getTrader=function(e){try{return s.TraderFactory.getTrader(e)}catch(t){throw l.logger.error("Failed to get trader",{blockchain:e,error:t.message}),t}};const s=n(871),o=n(1583),i=n(5512),d=n(9497),c=n(6086),l=n(1213);a(n(871),t),a(n(1583),t),a(n(5512),t),a(n(9497),t),a(n(6086),t)},5943:function(e,t,n){var r=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.UserService=void 0;const a=n(6131);t.UserService={createUser:e=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.create(e)}catch(e){throw console.log(e),new Error(e.message)}})),findById:e=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.findById(e)}catch(e){throw new Error(e.message)}})),findOne:e=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.findOne(Object.assign(Object.assign({},e),{retired:!1}))}catch(e){throw new Error(e.message)}})),findLastOne:e=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.findOne(e).sort({updatedAt:-1})}catch(e){throw new Error(e.message)}})),find:e=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.find(e)}catch(e){throw new Error(e.message)}})),findAndSort:e=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.find(e).sort({retired:1,nonce:1}).exec()}catch(e){throw new Error(e.message)}})),updateOne:(e,t)=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.findByIdAndUpdate(e,t,{new:!0})}catch(e){throw new Error(e.message)}})),findAndUpdateOne:(e,t)=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.findOneAndUpdate(e,t,{new:!0})}catch(e){throw new Error(e.message)}})),updateMany:(e,t)=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.updateMany(e,{$set:t})}catch(e){throw new Error(e.message)}})),deleteOne:e=>r(void 0,void 0,void 0,(function*(){try{return yield a.UserSchema.findOneAndDelete(e)}catch(e){throw new Error(e.message)}})),extractUniqueCode:e=>{const t=e.split(" ");return t.length>1?t[1]:null},extractPNLdata:e=>{const t=e.split(" ");return t.length>1&&t[1].endsWith("png")?t[1].replace("png",".png"):null}}},7095:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.initializeUtilityServices=function(){const{logger:e}=n(1213);e.info("Initializing utility services..."),e.info("All utility services initialized successfully")},a(n(4653),t),a(n(6325),t),a(n(5943),t)},4653:function(e,t,n){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.generateBASEWallet=t.generateSOLWallet=t.generateBSCWallet=t.generateETHWallet=void 0;const a=n(8491),s=n(9321),o=r(n(8574));t.generateETHWallet=()=>{const e=s.ethers.Wallet.createRandom();return{blockchain:"eth",network:"mainnet",address:e.address,privateKey:e.privateKey,rpcUrl:"https://mainnet.infura.io/v3/********************************",mnemonic:e.mnemonic.phrase}},t.generateBSCWallet=()=>{const e=s.ethers.Wallet.createRandom();return{blockchain:"bsc",network:"mainnet",address:e.address,privateKey:e.privateKey,rpcUrl:"https://bsc-dataseed.binance.org/",mnemonic:e.mnemonic.phrase}},t.generateSOLWallet=()=>{const e=a.Keypair.generate();return{blockchain:"sol",network:"mainnet",address:e.publicKey.toString(),privateKey:o.default.encode(e.secretKey),rpcUrl:"https://api.mainnet-beta.solana.com"}},t.generateBASEWallet=()=>{try{const e=s.ethers.Wallet.createRandom();return{blockchain:"base",network:"mainnet",address:e.address,privateKey:e.privateKey,rpcUrl:"https://mainnet.base.org/",mnemonic:e.mnemonic.phrase}}catch(e){throw new Error("Failed to generate BASE wallet. Consult BASE documentation.")}}},5055:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var a=Object.getOwnPropertyDescriptor(t,n);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,a)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return a(t,e),t},o=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(a,s){function o(e){try{d(r.next(e))}catch(e){s(e)}}function i(e){try{d(r.throw(e))}catch(e){s(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,i)}d((r=r.apply(e,t||[])).next())}))},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.walletMonitorService=t.WalletMonitorService=void 0,t.initializeWalletMonitor=function(e){t.walletMonitorService.bot=e,t.walletMonitorService.start().catch((e=>{l.logger.error("Failed to start wallet monitor service",{error:e.message})}))};const d=n(9321),c=n(8491),l=n(1213),u=n(5488),g=s(n(872)),y=i(n(8722)),h=n(8708),p="wallet_monitor:active";class f{constructor(e){this.monitoringInterval=null,this.eventListenerRestartInterval=null,this.activeListeners=new Map,this.bot=e,this.solConnection=new c.Connection(process.env.SOL_PROVIDER_URL||"https://api.mainnet-beta.solana.com","confirmed"),process.setMaxListeners(20)}start(){return o(this,void 0,void 0,(function*(){try{l.logger.info("Starting wallet monitoring service"),yield h.cacheService.set(p,!0),yield this.setupAllWalletListeners(),this.startPolling(),this.eventListenerRestartInterval=setInterval((()=>this.restartEventListeners()),18e5),l.logger.info("Wallet monitoring service started successfully")}catch(e){l.logger.error("Error starting wallet monitoring service",{error:e.message})}}))}stop(){return o(this,void 0,void 0,(function*(){l.logger.info("Stopping wallet monitoring service"),this.monitoringInterval&&(clearInterval(this.monitoringInterval),this.monitoringInterval=null),this.eventListenerRestartInterval&&(clearInterval(this.eventListenerRestartInterval),this.eventListenerRestartInterval=null),this.removeAllEventListeners(),yield h.cacheService.set(p,!1),l.logger.info("Wallet monitoring service stopped")}))}setupAllWalletListeners(){return o(this,void 0,void 0,(function*(){var e,t,n,r;try{const a=yield y.default.find({$or:[{"eth_wallet.address":{$exists:!0,$ne:""}},{"bsc_wallet.address":{$exists:!0,$ne:""}},{"base_wallet.address":{$exists:!0,$ne:""}},{"sol_wallet.address":{$exists:!0,$ne:""}}]});l.logger.info(`Setting up wallet listeners for ${a.length} users`);for(const s of a)(null===(e=s.eth_wallet)||void 0===e?void 0:e.address)&&(yield this.setupWalletListener(s._id,s.chat_id,s.eth_wallet.address,g.BlockchainType.ETH)),(null===(t=s.bsc_wallet)||void 0===t?void 0:t.address)&&(yield this.setupWalletListener(s._id,s.chat_id,s.bsc_wallet.address,g.BlockchainType.BSC)),(null===(n=s.base_wallet)||void 0===n?void 0:n.address)&&(yield this.setupWalletListener(s._id,s.chat_id,s.base_wallet.address,g.BlockchainType.BASE)),(null===(r=s.sol_wallet)||void 0===r?void 0:r.address)&&(yield this.ensureWalletBalanceRecord(s._id,s.chat_id,s.sol_wallet.address,g.BlockchainType.SOL))}catch(e){l.logger.error("Error setting up wallet listeners",{error:e.message})}}))}setupWalletListener(e,t,n,r){return o(this,void 0,void 0,(function*(){try{if(yield this.ensureWalletBalanceRecord(e,t,n,r),r===g.BlockchainType.SOL)return;const a=u.providerManager.getProvider(r);if(!a)return void l.logger.warn(`No provider available for ${r}, skipping event listener`);this.removeEventListener(n,r);const s=`${r}:${n.toLowerCase()}`,i={topics:[null,null,d.ethers.utils.hexZeroPad(n,32)]},c=e=>o(this,void 0,void 0,(function*(){try{const t=yield this.getBalance(n,r),a=yield g.default.updateBalance(n,r,t,e.transactionHash,g.TransactionType.INCOMING);a&&(yield g.default.sendBalanceAlert(this.bot,a))}catch(t){l.logger.error(`Error processing wallet event for ${n}`,{error:t.message,walletAddress:n,blockchain:r,transactionHash:e.transactionHash})}}));a.on(i,c),this.activeListeners.set(s,c),l.logger.debug(`Set up event listener for ${n} on ${r}`)}catch(e){l.logger.error(`Error setting up wallet listener for ${n} on ${r}`,{error:e.message,walletAddress:n,blockchain:r})}}))}removeEventListener(e,t){const n=`${t}:${e.toLowerCase()}`,r=this.activeListeners.get(n);if(r){const a=u.providerManager.getProvider(t);a&&a.removeListener("logs",r),this.activeListeners.delete(n),l.logger.debug(`Removed event listener for ${e} on ${t}`)}}removeAllEventListeners(){for(const[e,t]of this.activeListeners.entries()){const[n]=e.split(":"),r=n,a=u.providerManager.getProvider(r);a&&a.removeListener("logs",t)}this.activeListeners.clear(),l.logger.info("Removed all wallet event listeners")}restartEventListeners(){return o(this,void 0,void 0,(function*(){l.logger.info("Restarting wallet event listeners"),this.removeAllEventListeners(),yield this.setupAllWalletListeners()}))}startPolling(){this.monitoringInterval&&clearInterval(this.monitoringInterval),this.monitoringInterval=setInterval((()=>this.pollWalletBalances()),6e4),l.logger.info("Started polling wallet balances every 60 seconds")}pollWalletBalances(){return o(this,void 0,void 0,(function*(){try{const e=yield g.default.find({});l.logger.debug(`Polling ${e.length} wallet balances`);for(const t of e)try{const e=yield this.getBalance(t.walletAddress,t.blockchain);if(e===t.balance)continue;const n=yield g.default.updateBalance(t.walletAddress,t.blockchain,e);n&&(yield g.default.sendBalanceAlert(this.bot,n))}catch(e){l.logger.error(`Error polling wallet balance for ${t.walletAddress}`,{error:e.message,walletAddress:t.walletAddress,blockchain:t.blockchain})}}catch(e){l.logger.error("Error polling wallet balances",{error:e.message})}}))}getBalance(e,t){return o(this,void 0,void 0,(function*(){try{switch(t){case g.BlockchainType.ETH:return yield this.getEthBalance(e);case g.BlockchainType.BSC:return yield this.getBscBalance(e);case g.BlockchainType.BASE:return yield this.getBaseBalance(e);case g.BlockchainType.SOL:return(yield this.getSolBalance(e)).toString();default:throw new Error(`Unsupported blockchain: ${t}`)}}catch(n){throw l.logger.error(`Error getting balance for ${e} on ${t}`,{error:n.message,walletAddress:e,blockchain:t}),n}}))}getEthBalance(e){return o(this,void 0,void 0,(function*(){try{const t=u.providerManager.getProvider(g.BlockchainType.ETH);if(!t)throw new Error("ETH provider not available");const n=yield t.getBalance(e);return d.ethers.utils.formatEther(n)}catch(t){throw l.logger.error(`Failed to get ETH balance for ${e}`,{error:t.message,address:e}),t}}))}getBscBalance(e){return o(this,void 0,void 0,(function*(){try{const t=u.providerManager.getProvider(g.BlockchainType.BSC);if(!t)throw new Error("BSC provider not available");const n=yield t.getBalance(e);return d.ethers.utils.formatEther(n)}catch(t){throw l.logger.error(`Failed to get BSC balance for ${e}`,{error:t.message,address:e}),t}}))}getBaseBalance(e){return o(this,void 0,void 0,(function*(){try{const t=u.providerManager.getProvider(g.BlockchainType.BASE);if(!t)throw new Error("BASE provider not available");const n=yield t.getBalance(e);return d.ethers.utils.formatEther(n)}catch(t){throw l.logger.error(`Failed to get BASE balance for ${e}`,{error:t.message,address:e}),t}}))}getSolBalance(e){return o(this,void 0,void 0,(function*(){try{return(yield this.solConnection.getBalance(new c.PublicKey(e)))/1e9}catch(t){throw l.logger.error(`Failed to get SOL balance for ${e}`,{error:t.message,address:e}),t}}))}ensureWalletBalanceRecord(e,t,n,r){return o(this,void 0,void 0,(function*(){try{let a=yield g.default.findOne({walletAddress:n,blockchain:r});if(!a){const s=yield this.getBalance(n,r);a=new g.default({userId:e,chatId:t,walletAddress:n,blockchain:r,balance:s,previousBalance:s,lastUpdated:new Date}),yield a.save(),l.logger.info(`Created new wallet balance record for ${n} on ${r}`)}return a}catch(e){throw l.logger.error(`Error ensuring wallet balance record for ${n} on ${r}`,{error:e.message,walletAddress:n,blockchain:r}),e}}))}}t.WalletMonitorService=f,t.walletMonitorService=new f(null)},7018:e=>{e.exports=require("@solana/spl-token")},8491:e=>{e.exports=require("@solana/web3.js")},8938:e=>{e.exports=require("axios")},8574:e=>{e.exports=require("bs58")},6713:e=>{e.exports=require("cross-fetch")},818:e=>{e.exports=require("dotenv")},9321:e=>{e.exports=require("ethers")},6261:e=>{e.exports=require("events")},7252:e=>{e.exports=require("express")},6037:e=>{e.exports=require("mongoose")},6209:e=>{e.exports=require("node-telegram-bot-api")},4437:e=>{e.exports=require("socket.io")},9907:e=>{e.exports=require("cluster")},9896:e=>{e.exports=require("fs")},8611:e=>{e.exports=require("http")},857:e=>{e.exports=require("os")},6928:e=>{e.exports=require("path")},3883:e=>{e.exports=JSON.parse('[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"minTokensBeforeSwap","type":"uint256"}],"name":"MinTokensBeforeSwapUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"tokensSwapped","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"ethReceived","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"tokensIntoLiqudity","type":"uint256"}],"name":"SwapAndLiquify","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"SwapAndLiquifyEnabledUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"inputs":[],"name":"_charityFee","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"_liquidityFee","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"_maxTxAmount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"_taxFee","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"subtractedValue","type":"uint256"}],"name":"decreaseAllowance","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"tAmount","type":"uint256"}],"name":"deliver","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"excludeFromFee","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"excludeFromReward","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"includeInFee","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"includeInReward","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"addedValue","type":"uint256"}],"name":"increaseAllowance","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isExcludedFromFee","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isExcludedFromReward","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"tAmount","type":"uint256"},{"internalType":"bool","name":"deductTransferFee","type":"bool"}],"name":"reflectionFromToken","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"renounceOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"charityFee","type":"uint256"}],"name":"setCharityFeePercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"liquidityFee","type":"uint256"}],"name":"setLiquidityFeePercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"maxTxPercent","type":"uint256"}],"name":"setMaxTxPercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"_enabled","type":"bool"}],"name":"setSwapAndLiquifyEnabled","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"taxFee","type":"uint256"}],"name":"setTaxFeePercent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"swapAndLiquifyEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"rAmount","type":"uint256"}],"name":"tokenFromReflection","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalFees","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"recipient","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"address","name":"recipient","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"uniswapV2Pair","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"uniswapV2Router","outputs":[{"internalType":"contract IUniswapV2Router02","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"stateMutability":"payable","type":"receive"}]')},3455:e=>{e.exports=JSON.parse('[{"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"uint256","name":"amountOutMin","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"swapExactTokensForTokens","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"uint256","name":"amountOutMin","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"swapExactTokensForETH","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountOutMin","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"swapExactETHForTokens","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"payable","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"}],"name":"getAmountsOut","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"amountOut","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"}],"name":"getAmountsIn","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"view","type":"function"}]')}},t={};!function n(r){var a=t[r];if(void 0!==a)return a.exports;var s=t[r]={exports:{}};return e[r].call(s.exports,s,s.exports,n),s.exports}(6997)})();