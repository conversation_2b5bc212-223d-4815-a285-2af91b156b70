/**
 * ETH Scan Service
 * 
 * This service provides functionality for interacting with the Ethereum blockchain
 * and fetching data from Etherscan.
 */

import { ethers } from 'ethers';
import axios from 'axios';
import { logger } from '../logger/LoggerService';
import { BlockchainType, TraderFactory } from '../trading/TradingInterface';

// ETH configuration
const ETH_RPC_URL = process.env.ETH_PROVIDER_URL || 'https://mainnet.infura.io/v3/your-infura-key';
const ETH_SCAN_API_KEY = process.env.ETH_SCAN_API_KEY || '';
const ETH_SCAN_API_URL = 'https://api.etherscan.io/api';

// Create provider
const provider = new ethers.providers.JsonRpcProvider(ETH_RPC_URL);

/**
 * Get token information from Etherscan
 * @param contractAddress Token contract address
 * @returns Token information
 */
export async function getTokenInfo(contractAddress: string) {
  try {
    logger.info(`Getting token info for ${contractAddress} from Etherscan`, {
      contractAddress
    });

    // Get token name and symbol
    const tokenContract = new ethers.Contract(
      contractAddress,
      [
        'function name() view returns (string)',
        'function symbol() view returns (string)',
        'function decimals() view returns (uint8)',
        'function totalSupply() view returns (uint256)'
      ],
      provider
    );

    const [name, symbol, decimals, totalSupply] = await Promise.all([
      tokenContract.name(),
      tokenContract.symbol(),
      tokenContract.decimals(),
      tokenContract.totalSupply()
    ]);

    // Format total supply
    const formattedTotalSupply = ethers.utils.formatUnits(totalSupply, decimals);

    logger.info(`Token info retrieved for ${contractAddress}`, {
      contractAddress,
      name,
      symbol,
      decimals,
      totalSupply: formattedTotalSupply
    });

    return {
      name,
      symbol,
      decimals,
      totalSupply: formattedTotalSupply
    };
  } catch (error) {
    logger.error(`Error getting token info for ${contractAddress}`, {
      contractAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Get token balance for a wallet
 * @param contractAddress Token contract address
 * @param walletAddress Wallet address
 * @returns Token balance
 */
export async function getTokenBalance(contractAddress: string, walletAddress: string) {
  try {
    logger.info(`Getting token balance for ${walletAddress}`, {
      contractAddress,
      walletAddress
    });

    // Get token decimals
    const tokenContract = new ethers.Contract(
      contractAddress,
      [
        'function balanceOf(address) view returns (uint256)',
        'function decimals() view returns (uint8)'
      ],
      provider
    );

    const [balance, decimals] = await Promise.all([
      tokenContract.balanceOf(walletAddress),
      tokenContract.decimals()
    ]);

    // Format balance
    const formattedBalance = ethers.utils.formatUnits(balance, decimals);

    logger.info(`Token balance retrieved for ${walletAddress}`, {
      contractAddress,
      walletAddress,
      balance: formattedBalance
    });

    return formattedBalance;
  } catch (error) {
    logger.error(`Error getting token balance for ${walletAddress}`, {
      contractAddress,
      walletAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Get ETH balance for a wallet
 * @param walletAddress Wallet address
 * @returns ETH balance
 */
export async function getEthBalance(walletAddress: string) {
  try {
    logger.info(`Getting ETH balance for ${walletAddress}`, {
      walletAddress
    });

    // Get ETH balance
    const balance = await provider.getBalance(walletAddress);
    
    // Format balance
    const formattedBalance = ethers.utils.formatEther(balance);

    logger.info(`ETH balance retrieved for ${walletAddress}`, {
      walletAddress,
      balance: formattedBalance
    });

    return formattedBalance;
  } catch (error) {
    logger.error(`Error getting ETH balance for ${walletAddress}`, {
      walletAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Get transaction receipt from Etherscan
 * @param txHash Transaction hash
 * @returns Transaction receipt
 */
export async function getTransactionReceipt(txHash: string) {
  try {
    logger.info(`Getting transaction receipt for ${txHash}`, {
      txHash
    });

    // Get transaction receipt
    const receipt = await provider.getTransactionReceipt(txHash);

    logger.info(`Transaction receipt retrieved for ${txHash}`, {
      txHash,
      status: receipt.status
    });

    return receipt;
  } catch (error) {
    logger.error(`Error getting transaction receipt for ${txHash}`, {
      txHash,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Check if a token is a honeypot
 * @param contractAddress Token contract address
 * @returns Honeypot check result
 */
export async function checkHoneypot(contractAddress: string) {
  try {
    logger.info(`Checking if ${contractAddress} is a honeypot`, {
      contractAddress
    });

    // Use the trader to check if the token is a honeypot
    const trader = TraderFactory.getTrader(BlockchainType.ETH);
    const result = await trader.checkHoneypot(contractAddress);

    logger.info(`Honeypot check completed for ${contractAddress}`, {
      contractAddress,
      isHoneypot: result.isHoneypot,
      reason: result.honeypotReason
    });

    return result;
  } catch (error) {
    logger.error(`Error checking honeypot for ${contractAddress}`, {
      contractAddress,
      error: (error as Error).message
    });
    
    return {
      isHoneypot: false,
      honeypotReason: `Error checking honeypot: ${(error as Error).message}`
    };
  }
}
