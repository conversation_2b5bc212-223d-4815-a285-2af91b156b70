/**
 * Trading Interface Module
 *
 * This module defines common interfaces for blockchain trading services
 * to ensure consistent implementation across different blockchains.
 */

import TelegramBot from 'node-telegram-bot-api';

/**
 * Supported blockchain types
 */
export enum BlockchainType {
  BSC = 'bsc',
  ETH = 'eth',
  SOL = 'sol',
  BASE = 'base'
}

/**
 * Token information interface
 */
export interface TokenInfo {
  name: string;
  symbol: string;
  decimals: number;
  totalSupply?: string;
  marketCap?: number;
  price?: number;
  address: string;
  blockchain: BlockchainType;
  isHoneypot?: boolean;
  honeypotInfo?: any;
}

/**
 * Wallet information interface
 */
export interface WalletInfo {
  address: string;
  privateKey: string;
  blockchain: BlockchainType;
  balance?: string;
  tokens?: TokenInfo[];
}

/**
 * Trade options interface
 */
export interface TradeOptions {
  slippage?: number;
  gasPrice?: string;
  gasLimit?: number;
  deadline?: number;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
}

/**
 * Trade result interface
 */
export interface TradeResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  blockNumber?: number;
  gasUsed?: number;
  effectiveGasPrice?: string;
  amountIn?: string;
  amountOut?: string;
  timestamp: number;
}

/**
 * Common interface for all blockchain traders
 */
export interface BlockchainTrader {
  /**
   * Get the blockchain type
   */
  getBlockchainType(): BlockchainType;

  /**
   * Buy tokens
   * @param tokenAddress The address of the token to buy
   * @param amount The amount to spend in native currency
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  buyTokens(tokenAddress: string, amount: string, walletAddress: string, options?: TradeOptions): Promise<TradeResult>;

  /**
   * Sell tokens
   * @param tokenAddress The address of the token to sell
   * @param percentage The percentage of tokens to sell (0-100)
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  sellTokens(tokenAddress: string, percentage: number, walletAddress: string, options?: TradeOptions): Promise<TradeResult>;

  /**
   * Get token information
   * @param tokenAddress The address of the token
   */
  getTokenInfo(tokenAddress: string): Promise<TokenInfo>;

  /**
   * Get wallet balance
   * @param walletAddress The wallet address
   */
  getWalletBalance(walletAddress: string): Promise<string>;

  /**
   * Get token balance for a wallet
   * @param tokenAddress The address of the token
   * @param walletAddress The wallet address
   */
  getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string>;

  /**
   * Check if a token is a honeypot
   * @param tokenAddress The address of the token
   */
  checkHoneypot(tokenAddress: string): Promise<any>;

  /**
   * Generate a new wallet
   */
  generateWallet(): Promise<WalletInfo>;
}

/**
 * Factory for creating blockchain traders
 */
export class TraderFactory {
  private static traders: Map<BlockchainType, BlockchainTrader> = new Map();

  /**
   * Register a trader for a blockchain
   * @param trader The trader to register
   */
  public static registerTrader(trader: BlockchainTrader): void {
    this.traders.set(trader.getBlockchainType(), trader);
  }

  /**
   * Get a trader for a blockchain
   * @param blockchain The blockchain type
   */
  public static getTrader(blockchain: BlockchainType): BlockchainTrader {
    const trader = this.traders.get(blockchain);
    if (!trader) {
      throw new Error(`No trader registered for blockchain: ${blockchain}`);
    }
    return trader;
  }

  /**
   * Check if a trader is registered for a blockchain
   * @param blockchain The blockchain type
   */
  public static hasTrader(blockchain: BlockchainType): boolean {
    return this.traders.has(blockchain);
  }
}

/**
 * Base trader implementation with common functionality
 */
export abstract class BaseTrader implements BlockchainTrader {
  protected bot: TelegramBot;

  constructor(bot: TelegramBot) {
    this.bot = bot;
  }

  abstract getBlockchainType(): BlockchainType;
  abstract buyTokens(tokenAddress: string, amount: string, walletAddress: string, options?: TradeOptions): Promise<TradeResult>;
  abstract sellTokens(tokenAddress: string, percentage: number, walletAddress: string, options?: TradeOptions): Promise<TradeResult>;
  abstract getTokenInfo(tokenAddress: string): Promise<TokenInfo>;
  abstract getWalletBalance(walletAddress: string): Promise<string>;
  abstract getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string>;
  abstract checkHoneypot(tokenAddress: string): Promise<any>;
  abstract generateWallet(): Promise<WalletInfo>;

  /**
   * Send a message to a Telegram chat
   * @param chatId The chat ID
   * @param message The message to send
   */
  protected async sendMessage(chatId: number, message: string): Promise<void> {
    try {
      await this.bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }
}
