import mongoose, { Document, Schema } from 'mongoose';

export interface ICronJob extends Document {
    chatId: number;
    contractAddress: string;
    blockchain: string;
    cronTime: string;
}

const CronJobSchema: Schema = new Schema({
    chatId: Number,
    contractAddress: String,
    blockchain: String,
    cronTime: String,
});

const CronJob = mongoose.model<ICronJob>('CronJob', CronJobSchema);
export default CronJob;
