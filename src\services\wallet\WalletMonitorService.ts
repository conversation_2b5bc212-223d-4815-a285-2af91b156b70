/**
 * Wallet Monitor Service
 *
 * This service monitors wallet balances across different blockchains
 * and provides real-time notifications when balances change.
 */

import { ethers } from 'ethers';
import { Connection, PublicKey } from '@solana/web3.js';
import TelegramBot from 'node-telegram-bot-api';
import { logger } from '../logger/LoggerService';
import { providerManager } from '../blockchain/ProviderService';
import WalletBalance, { BlockchainType, TransactionType, IWalletBalance } from '../../models/wallet-balance.model';
import User from '../../models/user.models';
import { cacheService } from '../cache/RedisService';
import { sendToUser } from '../../utils/socket.utils';

// Cache keys
const MONITORING_ACTIVE_KEY = 'wallet_monitor:active';

// Monitoring intervals
const POLLING_INTERVAL = 60 * 1000; // 1 minute
const EVENT_LISTENER_RESTART_INTERVAL = 30 * 60 * 1000; // 30 minutes

export class WalletMonitorService {
  private bot: TelegramBot;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private eventListenerRestartInterval: NodeJS.Timeout | null = null;
  private activeListeners: Map<string, ethers.providers.Listener> = new Map();
  private solConnection: Connection;

  constructor(bot: TelegramBot) {
    this.bot = bot;
    this.solConnection = new Connection(
      process.env.SOL_PROVIDER_URL || 'https://api.mainnet-beta.solana.com',
      'confirmed'
    );

    // Increase max listeners to avoid memory leak warnings
    process.setMaxListeners(20);
  }

  /**
   * Start monitoring wallets
   */
  public async start(): Promise<void> {
    try {
      logger.info('Starting wallet monitoring service');

      // Set monitoring as active
      await cacheService.set(MONITORING_ACTIVE_KEY, true);

      // Set up event listeners for all wallets
      await this.setupAllWalletListeners();

      // Start polling for wallets that don't support events
      this.startPolling();

      // Set up interval to restart event listeners periodically
      this.eventListenerRestartInterval = setInterval(
        () => this.restartEventListeners(),
        EVENT_LISTENER_RESTART_INTERVAL
      );

      logger.info('Wallet monitoring service started successfully');
    } catch (error) {
      logger.error('Error starting wallet monitoring service', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Stop monitoring wallets
   */
  public async stop(): Promise<void> {
    logger.info('Stopping wallet monitoring service');

    // Clear intervals
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.eventListenerRestartInterval) {
      clearInterval(this.eventListenerRestartInterval);
      this.eventListenerRestartInterval = null;
    }

    // Remove all event listeners
    this.removeAllEventListeners();

    // Set monitoring as inactive
    await cacheService.set(MONITORING_ACTIVE_KEY, false);

    logger.info('Wallet monitoring service stopped');
  }

  /**
   * Set up event listeners for all wallets
   */
  private async setupAllWalletListeners(): Promise<void> {
    try {
      // Get all users with wallets
      const users = await User.find({
        $or: [
          { 'eth_wallet.address': { $exists: true, $ne: '' } },
          { 'bsc_wallet.address': { $exists: true, $ne: '' } },
          { 'base_wallet.address': { $exists: true, $ne: '' } },
          { 'sol_wallet.address': { $exists: true, $ne: '' } }
        ]
      });

      logger.info(`Setting up wallet listeners for ${users.length} users`);

      // Set up listeners for each user's wallets
      for (const user of users) {
        // ETH wallet
        if (user.eth_wallet?.address) {
          await this.setupWalletListener(
            user._id,
            user.chat_id,
            user.eth_wallet.address,
            BlockchainType.ETH
          );
        }

        // BSC wallet
        if (user.bsc_wallet?.address) {
          await this.setupWalletListener(
            user._id,
            user.chat_id,
            user.bsc_wallet.address,
            BlockchainType.BSC
          );
        }

        // BASE wallet
        if (user.base_wallet?.address) {
          await this.setupWalletListener(
            user._id,
            user.chat_id,
            user.base_wallet.address,
            BlockchainType.BASE
          );
        }

        // SOL wallet - no event listeners, will be handled by polling
        if (user.sol_wallet?.address) {
          await this.ensureWalletBalanceRecord(
            user._id,
            user.chat_id,
            user.sol_wallet.address,
            BlockchainType.SOL
          );
        }
      }
    } catch (error) {
      logger.error('Error setting up wallet listeners', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Set up a listener for a specific wallet
   */
  private async setupWalletListener(
    userId: any,
    chatId: number,
    walletAddress: string,
    blockchain: BlockchainType
  ): Promise<void> {
    try {
      // Ensure we have a wallet balance record
      await this.ensureWalletBalanceRecord(userId, chatId, walletAddress, blockchain);

      // Skip Solana (no event support)
      if (blockchain === BlockchainType.SOL) {
        return;
      }

      // Get provider
      const provider = providerManager.getProvider(blockchain);
      if (!provider) {
        logger.warn(`No provider available for ${blockchain}, skipping event listener`);
        return;
      }

      // Remove any existing listener
      this.removeEventListener(walletAddress, blockchain);

      // Create a unique key for this wallet
      const listenerKey = `${blockchain}:${walletAddress.toLowerCase()}`;

      // Create a filter for incoming transactions
      const incomingFilter = {
        topics: [
          null, // Any event
          null, // Any from address
          ethers.utils.hexZeroPad(walletAddress, 32) // To address (for incoming transfers)
        ]
      };

      // Create a listener function
      const listener = async (log: ethers.providers.Log) => {
        try {
          // Get current balance
          const balance = await this.getBalance(walletAddress, blockchain);

          // Update the balance record
          const walletBalance = await WalletBalance.updateBalance(
            walletAddress,
            blockchain,
            balance,
            log.transactionHash,
            TransactionType.INCOMING
          );

          // Send alert if balance changed
          if (walletBalance) {
            await WalletBalance.sendBalanceAlert(this.bot, walletBalance);
          }
        } catch (error) {
          logger.error(`Error processing wallet event for ${walletAddress}`, {
            error: (error as Error).message,
            walletAddress,
            blockchain,
            transactionHash: log.transactionHash
          });
        }
      };

      // Register the listener
      provider.on(incomingFilter, listener);
      this.activeListeners.set(listenerKey, listener);

      logger.debug(`Set up event listener for ${walletAddress} on ${blockchain}`);
    } catch (error) {
      logger.error(`Error setting up wallet listener for ${walletAddress} on ${blockchain}`, {
        error: (error as Error).message,
        walletAddress,
        blockchain
      });
    }
  }

  /**
   * Remove an event listener
   */
  private removeEventListener(walletAddress: string, blockchain: BlockchainType): void {
    const listenerKey = `${blockchain}:${walletAddress.toLowerCase()}`;
    const listener = this.activeListeners.get(listenerKey);

    if (listener) {
      const provider = providerManager.getProvider(blockchain);
      if (provider) {
        provider.removeListener('logs', listener);
      }
      this.activeListeners.delete(listenerKey);
      logger.debug(`Removed event listener for ${walletAddress} on ${blockchain}`);
    }
  }

  /**
   * Remove all event listeners
   */
  private removeAllEventListeners(): void {
    for (const [key, listener] of this.activeListeners.entries()) {
      const [blockchainStr] = key.split(':');
      const blockchain = blockchainStr as BlockchainType;
      const provider = providerManager.getProvider(blockchain);
      if (provider) {
        provider.removeListener('logs', listener);
      }
    }

    this.activeListeners.clear();
    logger.info('Removed all wallet event listeners');
  }

  /**
   * Restart all event listeners
   */
  private async restartEventListeners(): Promise<void> {
    logger.info('Restarting wallet event listeners');
    this.removeAllEventListeners();
    await this.setupAllWalletListeners();
  }

  /**
   * Start polling for wallet balances
   */
  private startPolling(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(
      () => this.pollWalletBalances(),
      POLLING_INTERVAL
    );

    logger.info(`Started polling wallet balances every ${POLLING_INTERVAL / 1000} seconds`);
  }

  /**
   * Poll all wallet balances
   */
  private async pollWalletBalances(): Promise<void> {
    try {
      // Get all wallet balance records
      const walletBalances = await WalletBalance.find({});

      logger.debug(`Polling ${walletBalances.length} wallet balances`);

      // Check each wallet balance
      for (const walletBalance of walletBalances) {
        try {
          // Get current balance
          const balance = await this.getBalance(
            walletBalance.walletAddress,
            walletBalance.blockchain
          );

          // Skip if balance hasn't changed
          if (balance === walletBalance.balance) {
            continue;
          }

          // Update the balance record
          const updatedWalletBalance = await WalletBalance.updateBalance(
            walletBalance.walletAddress,
            walletBalance.blockchain,
            balance
          );

          // Send alert if balance changed
          if (updatedWalletBalance) {
            await WalletBalance.sendBalanceAlert(this.bot, updatedWalletBalance);
          }
        } catch (error) {
          logger.error(`Error polling wallet balance for ${walletBalance.walletAddress}`, {
            error: (error as Error).message,
            walletAddress: walletBalance.walletAddress,
            blockchain: walletBalance.blockchain
          });
        }
      }
    } catch (error) {
      logger.error('Error polling wallet balances', {
        error: (error as Error).message
      });
    }
  }

  /**
   * Get balance for a wallet
   */
  private async getBalance(walletAddress: string, blockchain: BlockchainType): Promise<string> {
    try {
      switch (blockchain) {
        case BlockchainType.ETH:
          return await this.getEthBalance(walletAddress);
        case BlockchainType.BSC:
          return await this.getBscBalance(walletAddress);
        case BlockchainType.BASE:
          return await this.getBaseBalance(walletAddress);
        case BlockchainType.SOL:
          return (await this.getSolBalance(walletAddress)).toString();
        default:
          throw new Error(`Unsupported blockchain: ${blockchain}`);
      }
    } catch (error) {
      logger.error(`Error getting balance for ${walletAddress} on ${blockchain}`, {
        error: (error as Error).message,
        walletAddress,
        blockchain
      });
      throw error;
    }
  }

  /**
   * Get ETH balance
   */
  private async getEthBalance(address: string): Promise<string> {
    try {
      const provider = providerManager.getProvider(BlockchainType.ETH);
      if (!provider) {
        throw new Error('ETH provider not available');
      }

      const balance = await provider.getBalance(address);
      return ethers.utils.formatEther(balance);
    } catch (error) {
      logger.error(`Failed to get ETH balance for ${address}`, {
        error: (error as Error).message,
        address
      });
      throw error;
    }
  }

  /**
   * Get BSC balance
   */
  private async getBscBalance(address: string): Promise<string> {
    try {
      const provider = providerManager.getProvider(BlockchainType.BSC);
      if (!provider) {
        throw new Error('BSC provider not available');
      }

      const balance = await provider.getBalance(address);
      return ethers.utils.formatEther(balance);
    } catch (error) {
      logger.error(`Failed to get BSC balance for ${address}`, {
        error: (error as Error).message,
        address
      });
      throw error;
    }
  }

  /**
   * Get BASE balance
   */
  private async getBaseBalance(address: string): Promise<string> {
    try {
      const provider = providerManager.getProvider(BlockchainType.BASE);
      if (!provider) {
        throw new Error('BASE provider not available');
      }

      const balance = await provider.getBalance(address);
      return ethers.utils.formatEther(balance);
    } catch (error) {
      logger.error(`Failed to get BASE balance for ${address}`, {
        error: (error as Error).message,
        address
      });
      throw error;
    }
  }

  /**
   * Get SOL balance
   */
  private async getSolBalance(address: string): Promise<number> {
    try {
      const balance = await this.solConnection.getBalance(new PublicKey(address));
      return balance / 1e9;
    } catch (error) {
      logger.error(`Failed to get SOL balance for ${address}`, {
        error: (error as Error).message,
        address
      });
      throw error;
    }
  }

  /**
   * Ensure a wallet balance record exists
   */
  private async ensureWalletBalanceRecord(
    userId: any,
    chatId: number,
    walletAddress: string,
    blockchain: BlockchainType
  ): Promise<IWalletBalance> {
    try {
      // Check if record exists
      let walletBalance = await WalletBalance.findOne({
        walletAddress,
        blockchain
      });

      if (!walletBalance) {
        // Get initial balance
        const balance = await this.getBalance(walletAddress, blockchain);

        // Create new record
        walletBalance = new WalletBalance({
          userId,
          chatId,
          walletAddress,
          blockchain,
          balance,
          previousBalance: balance,
          lastUpdated: new Date()
        });

        await walletBalance.save();
        logger.info(`Created new wallet balance record for ${walletAddress} on ${blockchain}`);
      }

      return walletBalance;
    } catch (error) {
      logger.error(`Error ensuring wallet balance record for ${walletAddress} on ${blockchain}`, {
        error: (error as Error).message,
        walletAddress,
        blockchain
      });
      throw error;
    }
  }
}

// Create singleton instance
export const walletMonitorService = new WalletMonitorService(null as any);

// Initialize with bot
export function initializeWalletMonitor(bot: TelegramBot): void {
  (walletMonitorService as any).bot = bot;
  walletMonitorService.start().catch(error => {
    logger.error('Failed to start wallet monitor service', {
      error: (error as Error).message
    });
  });
}
