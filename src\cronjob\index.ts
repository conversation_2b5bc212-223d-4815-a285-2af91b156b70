/**
 * Cron<PERSON>b Module
 *
 * This module exports functions for managing snipe jobs and monitoring tokens.
 */

import { logger } from '../services/logger/LoggerService';
import { updateTokens, startPeriodicSnipeUpdates, initializeSocketServer } from './snipemonitor';

// Import trader implementations
import '../services/trading/BscTrader';
import '../services/trading/EthTrader';
import '../services/trading/BaseTrader';
import '../services/trading/SolTrader';

/**
 * Register all traders with the factory
 * This is handled automatically when the trader files are imported
 */
export function registerTraders(): void {
  logger.info('Traders registered automatically on import');
}



/**
 * Initialize the snipe monitor
 * @param bot Telegram bot instance
 * @param port Socket.IO server port
 */
export function initializeSnipeMonitor(bot: any, port: number = 3000): void {
  // Register all traders
  registerTraders();

  // Initialize the socket server and start periodic updates
  initializeSocketServer(port, bot);

  logger.info('Snipe monitor initialized', { port });
}

// Export functions from SnipeMonitor
export { updateTokens, startPeriodicSnipeUpdates };
