"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSession = void 0;
const user_models_1 = __importDefault(require("../../src/models/user.models"));
let userSessions = {};
const createSession = (bot, msg) => __awaiter(void 0, void 0, void 0, function* () {
    const chatId = msg.chat.id;
    if (!userSessions[chatId]) {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (user) {
            const currentBlockchain = user.currentBlockchain;
            const login = user[currentBlockchain] || [];
            userSessions[chatId] = {
                session: login,
                currentTradeIndex: 0,
                telegramId: user.chat_id,
                userId: user._id,
                trades: [],
            };
            yield bot.sendMessage(chatId, `Session created. You are now logged in to your ${currentBlockchain.toUpperCase()} wallet.`);
        }
        else {
            yield bot.sendMessage(chatId, 'User not found. Please register first.');
        }
    }
    else {
        yield bot.sendMessage(chatId, 'You are already logged in.');
    }
});
exports.createSession = createSession;
