/**
 * BSC Trader Implementation
 *
 * This module implements the BlockchainTrader interface for the Binance Smart Chain.
 */

import TelegramBot from 'node-telegram-bot-api';
import { ethers } from 'ethers';
import {
  BaseTrader,
  BlockchainType,
  TokenInfo,
  TradeOptions,
  TradeResult,
  WalletInfo
} from './TradingInterface';
import { logger } from '../logger/LoggerService';
import { generateBSCWallet } from '../wallet.service';
import { checkEVMHoneypot } from '../checkEVMHoneyPot.service';
import { getTokenInfoFromDexscreener } from '../dexscreener.service';
import tokenAbi from '../../abis/ERC20ABI.json';
import routerAbi from '../../abis/PancakeRouterABI.json';

/**
 * BSC-specific configuration
 */
export const BSC_CONFIG = {
  rpcUrl: process.env.BSC_PROVIDER_URL || 'https://bsc-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3',
  routerAddress: process.env.BSC_ROUTER_ADDRESS || '******************************************', // PancakeSwap Router
  wbnbAddress: process.env.BSC_WBNB_ADDRESS || '******************************************', // Wrapped BNB
  explorerUrl: 'https://bscscan.com',
  gasLimit: 300000,
  defaultSlippage: 12, // 12%
};

/**
 * BSC Trader implementation
 */
export class BscTrader extends BaseTrader {
  private provider: ethers.providers.JsonRpcProvider;
  private fallbackProviders: ethers.providers.JsonRpcProvider[] = [];

  constructor(bot: TelegramBot) {
    super(bot);

    // Import provider manager
    const { providerManager } = require('../blockchain/ProviderService');

    // Get primary provider
    const primaryProvider = providerManager.getProvider('bsc');

    if (primaryProvider) {
      this.provider = primaryProvider;
    } else {
      // Fallback to direct initialization if provider manager fails
      this.provider = new ethers.providers.JsonRpcProvider(BSC_CONFIG.rpcUrl);
    }

    // Get all providers as fallbacks
    this.fallbackProviders = providerManager.getAllProviders('bsc');

    logger.info('BSC Trader initialized', {
      rpcUrl: BSC_CONFIG.rpcUrl,
      fallbackCount: this.fallbackProviders.length
    });
  }

  /**
   * Get the blockchain type
   */
  getBlockchainType(): BlockchainType {
    return BlockchainType.BSC;
  }

  /**
   * Execute a provider call with automatic fallback
   * @param operation Function that takes a provider and returns a promise
   * @returns The result of the operation
   */
  private async executeWithFallback<T>(
    operation: (provider: ethers.providers.JsonRpcProvider) => Promise<T>
  ): Promise<T> {
    try {
      // Try with primary provider first
      return await operation(this.provider);
    } catch (error) {
      logger.warn('Primary BSC provider failed, trying fallbacks', {
        error: (error as Error).message
      });

      // Try each fallback provider
      for (const fallbackProvider of this.fallbackProviders) {
        try {
          return await operation(fallbackProvider);
        } catch (fallbackError) {
          // Continue to next fallback
          logger.debug('Fallback BSC provider failed', {
            error: (fallbackError as Error).message
          });
        }
      }

      // If all fallbacks fail, throw the original error
      throw error;
    }
  }

  /**
   * Buy tokens on BSC
   * @param tokenAddress The address of the token to buy
   * @param amount The amount of BNB to spend
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async buyTokens(
    tokenAddress: string,
    amount: string,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Buying tokens on BSC', {
      tokenAddress,
      amount,
      walletAddress,
      options
    });

    try {
      // Get the wallet's private key from the database
      // In a real implementation, you would retrieve this securely
      // For now, we'll assume it's passed in or retrieved elsewhere
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a wallet instance
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // Create a router contract instance
      const router = new ethers.Contract(
        BSC_CONFIG.routerAddress,
        routerAbi,
        wallet
      );

      // Calculate the amount in wei
      const amountInWei = ethers.utils.parseEther(amount);

      // Set up the path (BNB -> Token)
      const path = [BSC_CONFIG.wbnbAddress, tokenAddress];

      // Calculate the minimum amount out based on slippage
      const slippage = options?.slippage || BSC_CONFIG.defaultSlippage;
      const [, amountsOut] = await router.getAmountsOut(amountInWei, path);
      const minAmountOut = amountsOut.mul(100 - slippage).div(100);

      // Set up transaction parameters
      const gasLimit = options?.gasLimit || BSC_CONFIG.gasLimit;
      const gasPrice = options?.gasPrice
        ? ethers.utils.parseUnits(options.gasPrice, 'gwei')
        : await this.provider.getGasPrice();

      // Set deadline to 20 minutes from now
      const deadline = Math.floor(Date.now() / 1000) + 20 * 60;

      // Execute the swap
      const tx = await router.swapExactETHForTokensSupportingFeeOnTransferTokens(
        minAmountOut,
        path,
        walletAddress,
        deadline,
        {
          value: amountInWei,
          gasLimit,
          gasPrice
        }
      );

      // Wait for the transaction to be mined
      const receipt = await tx.wait();

      logger.info('BSC buy transaction successful', {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        tokenAddress
      });

      // Log performance
      logger.logPerformance('BSC buyTokens', startTime, {
        tokenAddress,
        transactionHash: receipt.transactionHash
      });

      return {
        success: true,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toNumber(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        amountIn: amount,
        amountOut: ethers.utils.formatUnits(minAmountOut, 18), // This is approximate
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('BSC buy transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Sell tokens on BSC
   * @param tokenAddress The address of the token to sell
   * @param percentage The percentage of tokens to sell (0-100)
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async sellTokens(
    tokenAddress: string,
    percentage: number,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Selling tokens on BSC', {
      tokenAddress,
      percentage,
      walletAddress,
      options
    });

    try {
      // Validate percentage
      if (percentage <= 0 || percentage > 100) {
        throw new Error('Percentage must be between 1 and 100');
      }

      // Get the wallet's private key
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a wallet instance
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        wallet
      );

      // Create router contract instance
      const router = new ethers.Contract(
        BSC_CONFIG.routerAddress,
        routerAbi,
        wallet
      );

      // Get token balance and decimals
      const balance = await tokenContract.balanceOf(walletAddress);
      const decimals = await tokenContract.decimals();

      // Calculate amount to sell based on percentage
      const amountToSell = balance.mul(percentage).div(100);

      if (amountToSell.isZero()) {
        throw new Error('No tokens to sell');
      }

      // Check if we need to approve the router
      const allowance = await tokenContract.allowance(walletAddress, BSC_CONFIG.routerAddress);
      if (allowance.lt(amountToSell)) {
        // Approve the router to spend tokens
        const approveTx = await tokenContract.approve(
          BSC_CONFIG.routerAddress,
          ethers.constants.MaxUint256
        );
        await approveTx.wait();
        logger.info('Approved router to spend tokens', {
          tokenAddress,
          walletAddress,
          transactionHash: approveTx.hash
        });
      }

      // Set up the path (Token -> BNB)
      const path = [tokenAddress, BSC_CONFIG.wbnbAddress];

      // Calculate the minimum amount out based on slippage
      const slippage = options?.slippage || BSC_CONFIG.defaultSlippage;
      const [, amountsOut] = await router.getAmountsOut(amountToSell, path);
      const minAmountOut = amountsOut.mul(100 - slippage).div(100);

      // Set up transaction parameters
      const gasLimit = options?.gasLimit || BSC_CONFIG.gasLimit;
      const gasPrice = options?.gasPrice
        ? ethers.utils.parseUnits(options.gasPrice, 'gwei')
        : await this.provider.getGasPrice();

      // Set deadline to 20 minutes from now
      const deadline = Math.floor(Date.now() / 1000) + 20 * 60;

      // Execute the swap
      const tx = await router.swapExactTokensForETHSupportingFeeOnTransferTokens(
        amountToSell,
        minAmountOut,
        path,
        walletAddress,
        deadline,
        {
          gasLimit,
          gasPrice
        }
      );

      // Wait for the transaction to be mined
      const receipt = await tx.wait();

      logger.info('BSC sell transaction successful', {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        tokenAddress,
        percentage
      });

      // Log performance
      logger.logPerformance('BSC sellTokens', startTime, {
        tokenAddress,
        transactionHash: receipt.transactionHash,
        percentage
      });

      return {
        success: true,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toNumber(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        amountIn: ethers.utils.formatUnits(amountToSell, decimals),
        amountOut: ethers.utils.formatEther(minAmountOut),
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('BSC sell transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress,
        percentage
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get token information
   * @param tokenAddress The address of the token
   */
  async getTokenInfo(tokenAddress: string): Promise<TokenInfo> {
    try {
      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        this.provider
      );

      // Get basic token information
      const [name, symbol, decimals, totalSupply] = await Promise.all([
        tokenContract.name(),
        tokenContract.symbol(),
        tokenContract.decimals(),
        tokenContract.totalSupply()
      ]);

      // Get additional information from Dexscreener
      const dexInfo = await getTokenInfoFromDexscreener(tokenAddress, BlockchainType.BSC);

      // Check if token is a honeypot
      const honeypotInfo = await this.checkHoneypot(tokenAddress);

      return {
        name,
        symbol,
        decimals,
        totalSupply: ethers.utils.formatUnits(totalSupply, decimals),
        address: tokenAddress,
        blockchain: BlockchainType.BSC,
        price: typeof dexInfo?.priceUsd === 'string' ? parseFloat(dexInfo.priceUsd) : (dexInfo?.priceUsd || 0),
        marketCap: typeof dexInfo?.marketCap === 'string' ? parseFloat(dexInfo.marketCap) : (dexInfo?.marketCap || 0),
        isHoneypot: honeypotInfo?.isHoneypot || false,
        honeypotInfo
      };
    } catch (error: any) {
      logger.error('Failed to get BSC token info', {
        error: error.message,
        tokenAddress
      });

      // Return minimal information
      return {
        name: 'Unknown',
        symbol: 'UNKNOWN',
        decimals: 18,
        address: tokenAddress,
        blockchain: BlockchainType.BSC
      };
    }
  }

  /**
   * Get wallet balance
   * @param walletAddress The wallet address
   */
  async getWalletBalance(walletAddress: string): Promise<string> {
    try {
      // Use executeWithFallback to handle provider failures
      const balance = await this.executeWithFallback(provider =>
        provider.getBalance(walletAddress)
      );

      return ethers.utils.formatEther(balance);
    } catch (error: any) {
      logger.error('Failed to get BSC wallet balance after trying all providers', {
        error: error.message,
        walletAddress
      });
      return '0';
    }
  }

  /**
   * Get token balance for a wallet
   * @param tokenAddress The address of the token
   * @param walletAddress The wallet address
   */
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string> {
    try {
      // Use executeWithFallback to handle provider failures
      return await this.executeWithFallback(async (provider) => {
        // Create token contract instance
        const tokenContract = new ethers.Contract(
          tokenAddress,
          tokenAbi,
          provider
        );

        // Get token balance and decimals
        const balance = await tokenContract.balanceOf(walletAddress);
        const decimals = await tokenContract.decimals();

        return ethers.utils.formatUnits(balance, decimals);
      });
    } catch (error: any) {
      logger.error('Failed to get BSC token balance after trying all providers', {
        error: error.message,
        tokenAddress,
        walletAddress
      });
      return '0';
    }
  }

  /**
   * Check if a token is a honeypot
   * @param tokenAddress The address of the token
   */
  async checkHoneypot(tokenAddress: string): Promise<any> {
    try {
      return await checkEVMHoneypot(tokenAddress, BlockchainType.BSC);
    } catch (error: any) {
      logger.error('Failed to check if BSC token is honeypot', {
        error: error.message,
        tokenAddress
      });
      return { isHoneypot: false, message: 'Failed to check' };
    }
  }

  /**
   * Generate a new BSC wallet
   */
  async generateWallet(): Promise<WalletInfo> {
    try {
      const wallet = generateBSCWallet();
      const balance = await this.getWalletBalance(wallet.address);

      return {
        ...wallet,
        balance,
        blockchain: BlockchainType.BSC
      };
    } catch (error: any) {
      logger.error('Failed to generate BSC wallet', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get a wallet's private key (this would be implemented securely in production)
   * @param walletAddress The wallet address
   */
  private async getPrivateKey(walletAddress: string): Promise<string | null> {
    // In a real implementation, you would retrieve this securely from a database
    // For now, we'll return null and assume it's handled elsewhere
    return null;
  }
}
