import { ethers } from 'ethers';
import dotenv from 'dotenv';

dotenv.config();

// Initialize providers for EVM-compatible blockchains with retry logic
const createProvider = (url: string, chainId?: number) => {
    const provider = new ethers.providers.JsonRpcProvider(url, chainId);
    
    // Add error handling
    provider.on('error', (error) => {
        console.error(`Provider error for ${url}:`, error);
    });

    return provider;
};

const providers = {
    eth: createProvider(process.env.ETH_PROVIDER_URL || '', 1),
    bsc: createProvider(process.env.BSC_PROVIDER_URL || '', 56),
    base: createProvider(process.env.BASE_PROVIDER_URL || '', 8453)
};

// Verify provider connections
const verifyProviders = async () => {
    for (const [chain, provider] of Object.entries(providers)) {
        try {
            const network = await provider.getNetwork();
            console.log(`${chain.toUpperCase()} Network:`, network);
        } catch (error) {
            console.error(`Failed to connect to ${chain.toUpperCase()} network:`, error);
        }
    }
};

// Call verifyProviders on startup
verifyProviders().catch(console.error);

// Wallet private keys and addresses
const privateKeys = {
    eth: process.env.ETH_PRIVATE_KEY || '4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b',
    bsc: process.env.BSC_PRIVATE_KEY || '4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b',
    base: process.env.BASE_PRIVATE_KEY || '4ced40a6eee04d1b194b42b95b682595ec39fa8df151cbfc126898511a36b70b'
};

const wallets = {
    eth: new ethers.Wallet(privateKeys.eth, providers.eth),
    bsc: new ethers.Wallet(privateKeys.bsc, providers.bsc),
    base: new ethers.Wallet(privateKeys.base, providers.base)
};

// List of token addresses to check balances for
const tokenAddresses = [
    process.env.TOKEN_ADDRESS_1 || '',
    process.env.TOKEN_ADDRESS_2 || '',
    process.env.TOKEN_ADDRESS_3 || '',
    // Add more token addresses as needed
];

const ERC20_ABI = [
    'function balanceOf(address owner) view returns (uint256)',
    'function decimals() view returns (uint8)',
    'function symbol() view returns (string)',
];

// Function to get token balance on EVM chains
const getTokenBalanceEVM = async (wallet: ethers.Wallet, tokenAddress: string) => {
    try {
        const tokenContract = new ethers.Contract(tokenAddress, ERC20_ABI, wallet);
        const [balance, decimals, symbol] = await Promise.all([
            tokenContract.balanceOf(wallet.address),
            tokenContract.decimals(),
            tokenContract.symbol()
        ]);
        return { tokenAddress, balance, symbol, decimals };
    } catch (error) {
        console.error(`Error fetching balance for token ${tokenAddress} on ${wallet}:`, error);
        return null;
    }
};

// Fetch and display balances for all tokens in each wallet on all EVM chains
export const fetchBalancesForAllChains = async () => {
    const balances: { [key: string]: { [key: string]: string } } = {};

    for (const [chain, wallet] of Object.entries(wallets)) {
        balances[chain] = {};
        
        // Get native token balance
        const nativeBalance = await wallet.getBalance();
        balances[chain]['native'] = ethers.utils.formatEther(nativeBalance);

        // Get token balances
        for (const tokenAddress of tokenAddresses) {
            if (!tokenAddress) continue; // Skip empty addresses
            
            try {
                const balanceData = await getTokenBalanceEVM(wallet, tokenAddress);
                if (balanceData) {
                    const { balance, decimals, symbol } = balanceData;
                    balances[chain][tokenAddress] = ethers.utils.formatUnits(balance, decimals);
                }
            } catch (error) {
                console.error(`Error fetching balance for token ${tokenAddress} on ${chain}:`, error);
            }
        }
    }

    return balances;
};

// Example usage
fetchBalancesForAllChains().catch(console.error);
