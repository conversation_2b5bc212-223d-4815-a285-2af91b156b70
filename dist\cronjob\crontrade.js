"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchAndCategorizeTrades = fetchAndCategorizeTrades;
exports.setupBscMonitoring = setupBscMonitoring;
exports.setupBaseMonitoring = setupBaseMonitoring;
exports.setupSolMonitoring = setupSolMonitoring;
exports.setupEthMonitoring = setupEthMonitoring;
const autotrade_model_1 = __importDefault(require("../models/autotrade.model")); // Update with the actual path to your model file
const basetrade_1 = require("../screens/trade/basetrade"); // Update with actual path
const bsctrade_1 = require("../screens/trade/bsctrade"); // Update with actual path
const soltrade_1 = require("../screens/trade/soltrade"); // Update with actual path
const ethtrade_1 = require("../screens/trade/ethtrade"); // Update with actual path
const user_models_1 = __importDefault(require("../models/user.models")); // Update with actual path
function fetchAndCategorizeTrades() {
    return __awaiter(this, void 0, void 0, function* () {
        // Fetch all unsold trades that are ongoing
        const unsoldTrades = yield autotrade_model_1.default.find({
            isSold: false,
            isInitialized: 'ongoing'
        }).exec();
        // Initialize the categories
        const categorizedTrades = {
            base: [],
            eth: [],
            sol: [],
            bsc: []
        };
        // Categorize the trades based on the blockchain field
        unsoldTrades.forEach((trade) => {
            switch (trade.blockchain) {
                case 'base':
                    categorizedTrades.base.push(trade);
                    break;
                case 'eth':
                    categorizedTrades.eth.push(trade);
                    break;
                case 'sol':
                    categorizedTrades.sol.push(trade);
                    break;
                case 'bsc':
                    categorizedTrades.bsc.push(trade);
                    break;
                default:
                    console.warn(`Unknown blockchain type: ${trade.blockchain}`);
            }
        });
        return categorizedTrades;
    });
}
// Function to set up monitoring for base trades
function setupBscMonitoring() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Fetch unsold Solana trades
            const unsoldTrades = yield autotrade_model_1.default.find({
                isSold: false,
                isInitialized: 'ongoing',
                blockchain: 'bsc'
            }).exec();
            // Fetch all users (chatIds) from the database
            const users = yield user_models_1.default.find().exec();
            // Iterate over each user and their trades
            for (const user of users) {
                const userTrades = unsoldTrades.filter(trade => trade.chatId === user.chat_id);
                for (const trade of userTrades) {
                    const jobKey = `${trade.chatId}_${trade.contractAddress}_${trade.blockchain}`;
                    // Start the interval monitoring
                    (0, bsctrade_1.monitorBscPriceAndSell)(null, trade.chatId, trade);
                    // Remove existing cron job record from the database
                    // await CronJob.findOneAndDelete({
                    //     chatId: trade.chatId,
                    //     contractAddress: trade.contractAddress,
                    //     blockchain: trade.blockchain,
                    // });
                }
            }
        }
        catch (error) {
            console.error('Error setting up Solana trade monitoring:', error);
        }
    });
}
function setupBaseMonitoring() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Fetch unsold Solana trades
            const unsoldTrades = yield autotrade_model_1.default.find({
                isSold: false,
                isInitialized: 'ongoing',
                blockchain: 'base'
            }).exec();
            // Fetch all users (chatIds) from the database
            const users = yield user_models_1.default.find().exec();
            // Iterate over each user and their trades
            for (const user of users) {
                const userTrades = unsoldTrades.filter(trade => trade.chatId === user.chat_id);
                for (const trade of userTrades) {
                    const jobKey = `${trade.chatId}_${trade.contractAddress}_${trade.blockchain}`;
                    // Start the interval monitoring
                    (0, basetrade_1.monitorBasePriceAndSell)(null, trade.chatId, trade);
                    // Remove existing cron job record from the database
                    // await CronJob.findOneAndDelete({
                    //     chatId: trade.chatId,
                    //     contractAddress: trade.contractAddress,
                    //     blockchain: trade.blockchain,
                    // });
                }
            }
        }
        catch (error) {
            console.error('Error setting up Solana trade monitoring:', error);
        }
    });
}
function setupSolMonitoring() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Fetch unsold Solana trades
            const unsoldTrades = yield autotrade_model_1.default.find({
                isSold: false,
                isInitialized: 'ongoing',
                blockchain: 'sol'
            }).exec();
            // Fetch all users (chatIds) from the database
            const users = yield user_models_1.default.find().exec();
            // Iterate over each user and their trades
            for (const user of users) {
                const userTrades = unsoldTrades.filter(trade => trade.chatId === user.chat_id);
                for (const trade of userTrades) {
                    const jobKey = `${trade.chatId}_${trade.contractAddress}_${trade.blockchain}`;
                    // Start the interval monitoring
                    (0, soltrade_1.monitorSolPriceAndSell)(null, trade.chatId, trade);
                    // Remove existing cron job record from the database
                    // await CronJob.findOneAndDelete({
                    //     chatId: trade.chatId,
                    //     contractAddress: trade.contractAddress,
                    //     blockchain: trade.blockchain,
                    // });
                }
            }
        }
        catch (error) {
            console.error('Error setting up Solana trade monitoring:', error);
        }
    });
}
function setupEthMonitoring() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Fetch unsold Solana trades
            const unsoldTrades = yield autotrade_model_1.default.find({
                isSold: false,
                isInitialized: 'ongoing',
                blockchain: 'eth'
            }).exec();
            // Fetch all users (chatIds) from the database
            const users = yield user_models_1.default.find().exec();
            // Iterate over each user and their trades
            for (const user of users) {
                const userTrades = unsoldTrades.filter(trade => trade.chatId === user.chat_id);
                for (const trade of userTrades) {
                    const jobKey = `${trade.chatId}_${trade.contractAddress}_${trade.blockchain}`;
                    // Start the interval monitoring
                    (0, ethtrade_1.monitorEthPriceAndSell)(null, trade.chatId, trade);
                    // Remove existing cron job record from the database
                    // await CronJob.findOneAndDelete({
                    //     chatId: trade.chatId,
                    //     contractAddress: trade.contractAddress,
                    //     blockchain: trade.blockchain,
                    // });
                }
            }
        }
        catch (error) {
            console.error('Error setting up Eth trade monitoring:', error);
        }
    });
}
