"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
// Define the user schema
const userSchema = new mongoose_1.Schema({
    chat_id: { type: Number, required: true },
    first_name: { type: String, required: true },
    last_name: { type: String },
    username: { type: String },
    sol_wallet: {
        address: { type: String, required: true },
        private_key: { type: String, required: true },
    },
    bsc_wallet: {
        address: { type: String, required: true },
        private_key: { type: String, required: true },
    },
    base_wallet: {
        address: { type: String, required: true },
        private_key: { type: String, required: true },
    },
    eth_wallet: {
        address: { type: String, required: true },
        private_key: { type: String, required: true },
    },
    preset_setting: { type: [Number], default: [0.01, 1, 5, 10] },
    nonce: { type: Number, default: 0 },
    retired: { type: Boolean, default: false },
    referrer_code: { type: String, default: null },
    referrer_wallet: { type: String, default: null },
    referral_code: { type: String, default: null },
    referral_date: { type: String, default: null },
    schedule: { type: String, default: '60' },
    burn_fee: { type: Boolean, default: false },
    auto_buy: { type: Boolean, default: false },
    auto_buy_amount: { type: String, default: '0' },
    auto_sell_amount: { type: String, default: '0' },
    trades: {
        bsc: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'Trade', default: [] }],
        sol: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'Trade', default: [] }],
        eth: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'Trade', default: [] }],
        base: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'Trade', default: [] }],
    },
    currentBlockchain: {
        type: String,
        enum: ['bsc', 'sol', 'eth', 'base'],
        default: 'bsc',
    },
    sol_config: {
        slippage: { type: mongoose_1.Schema.Types.Decimal128, default: 0.6 },
        antirug: { type: Boolean, default: false },
        antiMev: { type: Boolean, default: false },
        maxGasPrice: { type: mongoose_1.Schema.Types.Decimal128, default: 10 },
        maxGasLimit: { type: Number, default: 1000000 },
        autoBuy: { type: Boolean, default: false },
        minLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxMarketCap: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoSell: { type: Boolean, default: false },
        sellHigh: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        sellLow: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoApprove: { type: Boolean, default: false },
    },
    bsc_config: {
        slippage: { type: mongoose_1.Schema.Types.Decimal128, default: 0.1 },
        antirug: { type: Boolean, default: false },
        antiMev: { type: Boolean, default: false },
        maxGasPrice: { type: mongoose_1.Schema.Types.Decimal128, default: 10 },
        maxGasLimit: { type: Number, default: 1000000 },
        autoBuy: { type: Boolean, default: false },
        minLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxMarketCap: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoSell: { type: Boolean, default: false },
        sellHigh: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        sellLow: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoApprove: { type: Boolean, default: false },
    },
    base_config: {
        slippage: { type: mongoose_1.Schema.Types.Decimal128, default: 0.1 },
        antirug: { type: Boolean, default: false },
        antiMev: { type: Boolean, default: false },
        maxGasPrice: { type: mongoose_1.Schema.Types.Decimal128, default: 10 },
        maxGasLimit: { type: Number, default: 1000000 },
        autoBuy: { type: Boolean, default: false },
        minLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxMarketCap: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoSell: { type: Boolean, default: false },
        sellHigh: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        sellLow: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoApprove: { type: Boolean, default: false },
    },
    eth_config: {
        slippage: { type: mongoose_1.Schema.Types.Decimal128, default: 0.2 },
        antirug: { type: Boolean, default: false },
        antiMev: { type: Boolean, default: false },
        maxGasPrice: { type: mongoose_1.Schema.Types.Decimal128, default: 10 },
        maxGasLimit: { type: Number, default: 1000000 },
        autoBuy: { type: Boolean, default: false },
        minLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxMarketCap: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        maxLiquidity: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoSell: { type: Boolean, default: false },
        sellHigh: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        sellLow: { type: mongoose_1.Schema.Types.Decimal128, default: 0 },
        autoApprove: { type: Boolean, default: false },
    },
    bsc_dashboard_message_id: { type: Number },
    bsc_dashboard_content: { type: String },
    bsc_dashboard_markup: { type: mongoose_1.Schema.Types.Mixed },
    eth_dashboard_message_id: { type: Number },
    eth_dashboard_content: { type: String },
    eth_dashboard_markup: { type: mongoose_1.Schema.Types.Mixed },
    sol_dashboard_message_id: { type: Number },
    sol_dashboard_content: { type: String },
    sol_dashboard_markup: { type: mongoose_1.Schema.Types.Mixed },
    base_dashboard_message_id: { type: Number },
    base_dashboard_content: { type: String },
    base_dashboard_markup: { type: mongoose_1.Schema.Types.Mixed },
    bscCurrentContractAddress: { type: String },
    solCurrentContractAddress: { type: String },
    baseCurrentContractAddress: { type: String },
    ethCurrentContractAddress: { type: String },
    bscCurrentTokenName: { type: String },
    solCurrentTokenName: { type: String },
    baseCurrentTokenName: { type: String },
    ethCurrentTokenName: { type: String },
    snipes: [{ type: mongoose_1.Schema.Types.ObjectId, ref: 'Snipe', default: [] }],
});
const UserModel = mongoose_1.default.model('User', userSchema);
exports.default = UserModel;
