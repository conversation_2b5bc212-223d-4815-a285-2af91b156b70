"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeSocketServer = exports.startPeriodicSnipeUpdates = exports.updateTokens = void 0;
const axios_1 = __importDefault(require("axios"));
const http_1 = __importDefault(require("http"));
const socket_io_1 = require("socket.io");
const snipe_model_1 = __importDefault(require("../models/snipe.model"));
const baseswap_1 = require("./cronSniper/baseswap");
const pancake_1 = require("./cronSniper/pancake");
const uniswap_1 = require("./cronSniper/uniswap");
const jupiter_1 = require("./cronSniper/jupiter");
const BATCH_SIZE = 50;
const UPDATE_INTERVAL = 30000;
const server = http_1.default.createServer();
const io = new socket_io_1.Server(server);
// Function to get liquidity info from Dexscreener
const getLiquidityInfoFromDexscreener = (contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    const url = `https://api.dexscreener.com/latest/dex/tokens/${contractAddress}`;
    try {
        const response = yield axios_1.default.get(url);
        if (!response.data.pairs || response.data.pairs.length === 0) {
            return {
                hasLiquidity: false,
                message: `No liquidity found for the token ${contractAddress}.`,
            };
        }
        return {
            hasLiquidity: true,
        };
    }
    catch (error) {
        console.error('Error fetching token info from Dexscreener:', error);
        throw new Error('Error fetching token info from Dexscreener. Please try again later.');
    }
});
const buyToken = (blockchain, chatId, contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    console.log(`Initiating purchase for token ${contractAddress} on ${blockchain.toUpperCase()}.`);
    try {
        switch (blockchain) {
            case 'bsc':
                yield (0, pancake_1.buyBscTokens)(chatId, contractAddress);
                break;
            case 'sol':
                yield (0, jupiter_1.buySolTokens)(chatId, contractAddress);
                break;
            case 'eth':
                yield (0, uniswap_1.buyEthTokens)(chatId, contractAddress);
                break;
            case 'base':
                yield (0, baseswap_1.buyBaseTokens)(chatId, contractAddress);
                break;
            default:
                throw new Error(`Unsupported blockchain: ${blockchain}`);
        }
        console.log(`Successfully initiated purchase for token ${contractAddress} on ${blockchain.toUpperCase()}.`);
        return true;
    }
    catch (error) {
        console.error(`Failed to initiate purchase for token ${contractAddress} on ${blockchain.toUpperCase()}:`, error);
        return false;
    }
});
const sendMessageToUser = (bot, chatId, message) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        yield bot.sendMessage(chatId, message);
        console.log(`Message sent to chat ID ${chatId}: ${message}`);
    }
    catch (error) {
        console.error(`Failed to send message to chat ID ${chatId}:`, error);
    }
});
const updateTokens = (bot) => __awaiter(void 0, void 0, void 0, function* () {
    //   console.log('Started checking tokens');
    try {
        const snipes = yield snipe_model_1.default.find({
            'config.isConfigured': true,
            'config.isBought': false,
            $or: [
                { 'config.isExecuted_bsc': false },
                { 'config.isExecuted_sol': false },
                { 'config.isExecuted_eth': false },
                { 'config.isExecuted_base': false },
            ],
        });
        const tokenAddresses = snipes.map(snipe => snipe.contractAddress);
        for (let i = 0; i < tokenAddresses.length; i += BATCH_SIZE) {
            const batch = tokenAddresses.slice(i, i + BATCH_SIZE);
            const tokenDataPromises = batch.map(address => getLiquidityInfoFromDexscreener(address));
            const tokenData = yield Promise.all(tokenDataPromises);
            batch.forEach((address, index) => __awaiter(void 0, void 0, void 0, function* () {
                const data = tokenData[index];
                const snipe = snipes.find(s => s.contractAddress === address);
                if (snipe && data.hasLiquidity) {
                    const blockchain = snipe.blockchain;
                    console.log(`Initiating purchase for token ${address} on ${blockchain.toUpperCase()}.`);
                    const purchaseSuccessful = yield buyToken(blockchain, snipe.chatId, address);
                    if (purchaseSuccessful) {
                        snipe.set({
                            hasLiquidity: data.hasLiquidity,
                            lastPriceUpdate: new Date(),
                            'config.isBought': true,
                            [`config.isExecuted_${blockchain}`]: true,
                        });
                        yield snipe.save();
                        io.emit('tokenUpdate', {
                            tokenAddress: snipe.contractAddress,
                            tokenInfo: {
                                hasLiquidity: snipe.hasLiquidity,
                                message: 'Liquidity detected and token purchased.',
                            },
                        });
                        console.log(`Successfully bought token ${address} on ${blockchain.toUpperCase()}.`);
                        console.log(`Emitted update for token address: ${address}`);
                        yield sendMessageToUser(bot, snipe.chatId, `Success! Token ${address} has been sniped on ${blockchain.toUpperCase()}.`);
                    }
                    else {
                        yield sendMessageToUser(bot, snipe.chatId, `Failed to buy token ${address} on ${blockchain.toUpperCase()}.`);
                    }
                }
                else {
                    console.log(`No liquidity found for token ${address} or snipe not found.`);
                }
                yield new Promise(resolve => setTimeout(resolve, 1000));
            }));
            yield new Promise(resolve => setTimeout(resolve, 1000));
        }
    }
    catch (error) {
        console.error('Error updating tokens:', error);
    }
});
exports.updateTokens = updateTokens;
const startPeriodicSnipeUpdates = (bot) => {
    setInterval(() => (0, exports.updateTokens)(bot), UPDATE_INTERVAL);
};
exports.startPeriodicSnipeUpdates = startPeriodicSnipeUpdates;
const initializeSocketServer = (port, bot) => {
    server.listen(port, () => {
        console.log(`Socket.IO server is running on http://localhost:${port}`);
    });
    (0, exports.startPeriodicSnipeUpdates)(bot);
};
exports.initializeSocketServer = initializeSocketServer;
