import mongoose, { Document, Schema } from 'mongoose';

export interface Trade extends Document {
  chatId: number;
  blockchain: string;
  contractAddress: string;
  contractName: string;
  buyAmount: number;
  sellAmount: number;
  isSold: boolean;
  config: {
    takeProfit: number;
    stopLoss: number;
    spendAmount: number;
  };
}

const tradeSchema = new Schema<Trade>({
  chatId: { type: Number, required: true },
  blockchain: { type: String, required: true },
  contractAddress: { type: String },
  contractName: { type: String, default: '' }, 
  buyAmount: { type: Number, default: 0 },
  sellAmount: { type: Number, default: 0 },
  isSold: { type: Boolean, default: false },
  config: {
    takeProfit: { type: Number, default: 0 },
    stopLoss: { type: Number, default: 0 },
    spendAmount: { type: Number, default: 0 },
  },
});


const TradeModel = mongoose.model<Trade>('Trade', tradeSchema);

export async function updateTradeAsSold(_id: unknown, contractAddress: string, p0: number, p1: string): Promise<void> {
  await TradeModel.findOneAndUpdate(
    { contractAddress },
    { isSold: true }
  ).exec();
}
export default TradeModel;
