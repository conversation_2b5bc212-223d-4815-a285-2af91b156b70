/**
 * Solana Honeypot Checker Service
 * 
 * This service checks if a Solana token is a honeypot by analyzing various factors
 * such as program ownership, metadata, and transaction history.
 */

import { Connection, PublicKey } from '@solana/web3.js';
import { logger } from './logger/LoggerService';

/**
 * Honeypot check result interface
 */
export interface HoneypotCheckResult {
  isHoneypot: boolean;
  honeypotReason?: string;
  hasMetadata?: boolean;
  hasTransactionHistory?: boolean;
  isVerified?: boolean;
}

/**
 * Check if a token is a honeypot on Solana
 * @param contractAddress Token mint address
 * @returns Honeypot check result
 */
export async function checkSolanaHoneypot(
  contractAddress: string
): Promise<HoneypotCheckResult> {
  try {
    logger.info(`Checking if ${contractAddress} is a honeypot on Solana`, {
      contractAddress
    });

    // Default result
    const result: HoneypotCheckResult = {
      isHoneypot: false,
      hasMetadata: false,
      hasTransactionHistory: false,
      isVerified: false
    };

    // Get the Solana provider URL
    const providerUrl = process.env.SOL_PROVIDER_URL || 'https://api.mainnet-beta.solana.com';
    
    // Create connection
    const connection = new Connection(providerUrl);
    
    try {
      // Check if the mint exists
      const mintPubkey = new PublicKey(contractAddress);
      const accountInfo = await connection.getAccountInfo(mintPubkey);
      
      if (!accountInfo) {
        result.isHoneypot = true;
        result.honeypotReason = 'Token mint does not exist';
        return result;
      }
      
      // Check if the mint has the correct owner (Token Program)
      const TOKEN_PROGRAM_ID = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
      if (!accountInfo.owner.equals(TOKEN_PROGRAM_ID)) {
        result.isHoneypot = true;
        result.honeypotReason = 'Token mint has incorrect program owner';
        return result;
      }
      
      // Check transaction history (simplified)
      const signatures = await connection.getSignaturesForAddress(mintPubkey, { limit: 10 });
      result.hasTransactionHistory = signatures.length > 0;
      
      if (signatures.length < 3) {
        // Not necessarily a honeypot, but suspicious if very few transactions
        logger.warn(`Token ${contractAddress} has very few transactions`, {
          contractAddress,
          transactionCount: signatures.length
        });
      }
      
      // Check for metadata (simplified)
      const metadataProgramId = new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s');
      const [metadataAddress] = PublicKey.findProgramAddressSync(
        [
          Buffer.from('metadata'),
          metadataProgramId.toBuffer(),
          mintPubkey.toBuffer(),
        ],
        metadataProgramId
      );
      
      const metadataInfo = await connection.getAccountInfo(metadataAddress);
      result.hasMetadata = !!metadataInfo;
      
      // Tokens without metadata are suspicious
      if (!result.hasMetadata) {
        logger.warn(`Token ${contractAddress} has no metadata`, {
          contractAddress
        });
      }
      
      logger.info(`Honeypot check completed for ${contractAddress}`, {
        contractAddress,
        isHoneypot: result.isHoneypot,
        hasMetadata: result.hasMetadata,
        hasTransactionHistory: result.hasTransactionHistory
      });
      
      return result;
    } catch (error) {
      logger.error(`Error checking Solana token ${contractAddress}`, {
        contractAddress,
        error: (error as Error).message
      });
      
      result.isHoneypot = true;
      result.honeypotReason = `Error checking token: ${(error as Error).message}`;
      return result;
    }
  } catch (error) {
    logger.error(`Error in checkSolanaHoneypot for ${contractAddress}`, {
      contractAddress,
      error: (error as Error).message
    });
    
    return {
      isHoneypot: false,
      honeypotReason: `Error checking honeypot: ${(error as Error).message}`
    };
  }
}
