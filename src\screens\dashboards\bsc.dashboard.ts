import TelegramBot, { SendMessageOptions } from 'node-telegram-bot-api';
import { ethers } from 'ethers';
import User from '../../models/user.models';

export const getBscBalance = async (address: string): Promise<string> => {
  const providerUrl = process.env.BSC_PROVIDER_URL;

  if (!providerUrl) {
    throw new Error('BSC_PROVIDER_URL is not defined in the .env file');
  }

  const providerBSC = new ethers.providers.JsonRpcProvider(providerUrl);
  const balanceBSC = await providerBSC.getBalance(address);
  return parseFloat(ethers.utils.formatEther(balanceBSC)).toFixed(6);
};

export const handleBscDashboard = async (bot: TelegramBot, chat_id: number) => {
  const user = await User.findOne({ chat_id });

  if (!user) {
    bot.sendMessage(chat_id, 'User not found.');
    return;
  }

  const firstName = user.first_name || 'User';
  const bscWallet = user.bsc_wallet?.address || 'Not set';
  const balance = bscWallet !== 'Not set' ? await getBscBalance(bscWallet) : '0.0 BNB';

  const welcomeMessage = `👋 Welcome, ${firstName}, to your BSC Dashboard!
\n📍 **Address:** \`${bscWallet}\`
\n🔗 **Blockchain:** BSC
\n💰 **Balance:** \`${balance} BNB\`
\n⚙️ **Settings:** [Antirug, etc.]`;

  const options: SendMessageOptions = {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_bsc' }) }
        ],
        [
          { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
          { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_bsc' }) }
        ],
        [
          { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_bsc' }) },
          { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_bsc_wallet' }) }
        ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ]
      ]
    }
  };

  const sentMessage = await bot.sendMessage(chat_id, welcomeMessage, options);

  await User.updateOne({ chat_id: chat_id }, {
    $set: {
      'bsc_dashboard_message_id': sentMessage.message_id,
      'bsc_dashboard_content': welcomeMessage,
      'bsc_dashboard_markup': options.reply_markup
    }
  });
};
