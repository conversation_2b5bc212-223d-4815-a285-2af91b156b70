"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const user_models_1 = __importDefault(require("../models/user.models"));
const snipeWelcomeHandler = (bot, msg) => __awaiter(void 0, void 0, void 0, function* () {
    const { id: chat_id } = msg.chat;
    try {
        // Check if the user already exists in the database
        const user = yield user_models_1.default.findOne({ chat_id });
        if (user) {
            const caption = `👋 Select a blockchain to snipe!\n\n`;
            yield bot.sendMessage(chat_id, caption, {
                parse_mode: 'HTML',
                disable_web_page_preview: true,
                reply_markup: {
                    inline_keyboard: [
                        [
                            {
                                text: 'BSC',
                                callback_data: JSON.stringify({ command: 'scan_snipe_bsc' }),
                            },
                            {
                                text: 'BASE',
                                callback_data: JSON.stringify({ command: 'scan_snipe_base' }),
                            },
                        ],
                        [
                            {
                                text: 'SOL',
                                callback_data: JSON.stringify({ command: 'scan_snipe_sol' }),
                            },
                            {
                                text: 'ETH',
                                callback_data: JSON.stringify({ command: 'scan_snipe_eth' }),
                            },
                        ],
                        [
                            {
                                text: '* Dismiss message',
                                callback_data: JSON.stringify({ command: 'dismiss_message' }),
                            },
                        ],
                    ],
                },
            });
        }
    }
    catch (error) {
        console.error('Error handling wallet selection:', error);
        yield bot.sendMessage(chat_id, 'An error occurred while processing your request. Please try again later.');
    }
});
exports.default = snipeWelcomeHandler;
