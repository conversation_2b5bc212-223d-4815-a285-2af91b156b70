import { providers, Wallet, ethers } from 'ethers';
import Snipe from '../../models/snipe.model'; // Adjust the path as needed
import User from '../../models/user.models'; // Adjust the path as needed
import Trade from '../../models/trade.model'; // Adjust the path as needed
import axios from 'axios';
import dotenv from 'dotenv';
dotenv.config();

const adminAddress: string = process.env.EVM_ADMIN_ADDRESS || '';
const providerUrl = process.env.BASE_PROVIDER_URL;

if (!adminAddress) {
    throw new Error('ADMIN_ADDRESS environment variable is not set');
}

if (!providerUrl) {
    throw new Error('BASE_PROVIDER_URL environment variable is not set');
}

const apiKey = process.env.MASTER_KEY || '';

const headers = {
  '0x-api-key': apiKey,
};

async function getSwapQuote(url: string) {
    const response = await axios.get(url,{ headers });
    return response.data;
}

export async function buyBaseTokens(chatId: number, contractAddress: string): Promise<providers.TransactionReceipt> {
    // console.log("Starting token purchase on baseSwap");

    try {
        // Fetch user and Snipe configuration
        const user = await User.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }

        const snipe = await Snipe.findOne({
            chatId: chatId,
            contractAddress: contractAddress,
            blockchain: 'base'
        }).exec();

        if (!snipe) {
            throw new Error('Snipe configuration not found');
        }

        const { private_key, address } = user.base_wallet;
        const baseCurrentTokenName = user.baseCurrentTokenName;

        const spendBase = snipe.config.spend_base;
        // console.log(spendBase)
        const slippageBase = snipe.config.slippage_base;

        if (spendBase === undefined || spendBase === null || isNaN(spendBase)) {
            throw new Error('Invalid spend_base value');
        }

        if (slippageBase === undefined || slippageBase === null || isNaN(slippageBase)) {
            throw new Error('Invalid slippage_base value');
        }

        const buyToken = contractAddress;
        // console.log("Buying", buyToken);

        const provider = new providers.JsonRpcProvider(providerUrl);
        const wallet = new Wallet(private_key, provider);

        const spendBaseWei = ethers.utils.parseEther(spendBase.toString());
        // console.log(`Spend Amount (in Wei): ${spendBaseWei.toString()}`);

        const sellToken = '******************************************';
        const feeRecipient = adminAddress;
        const buyTokenPercentageFee = 0.8;

        // Convert slippage percentage
        const slippagePercentage = slippageBase / 100;

        let queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${spendBaseWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;

        
      const baseApiUrl = process.env.BASENODE_API_URL;
      const url = `${baseApiUrl}?${queryParams}`;

        const quote = await getSwapQuote(url);

        const buyTx = {
            to: quote.to,
            data: quote.data,
            value: ethers.BigNumber.from(quote.value),
            gasLimit: ethers.BigNumber.from(1000000),
            gasPrice: ethers.utils.parseUnits('0.05', 'gwei'),
        };

        // console.log('Signing and sending the buy transaction...');
        const buyTxResponse = await wallet.sendTransaction(buyTx);

        // console.log(`Buy Transaction Hash: ${buyTxResponse.hash}`);

        const receipt = await buyTxResponse.wait();

        // console.log(`Buy Transaction Mined! Block Number: ${receipt.blockNumber}`);

        const existingTrade = await Trade.findOne({
            userId: user._id,
            contractAddress: buyToken,
            isSold: false,
        }).exec();

        if (existingTrade) {
            existingTrade.buyAmount += parseFloat(spendBase.toString());
            await existingTrade.save();
            // console.log('Updated existing trade with new buy amount');
        } else {
            const newTrade = new Trade({
                userId: user._id,
                chatId: chatId,
                walletId: user.base_wallet.address,
                contractAddress: buyToken,
                contractName: baseCurrentTokenName,
                buyAmount: parseFloat(spendBase.toString()),
                sellAmount: 0,
                takeProfit: 0,
                stopLoss: 0,
                isSold: false,
                blockchain: 'base'
            });

            await newTrade.save();
            // console.log('Created new trade entry');
        }

        await User.updateOne(
            { _id: user._id },
            { $push: { [`trades.${'base'}`]: existingTrade ? existingTrade._id : user._id } }
        );

        // console.log('User trades updated successfully');

        return receipt;
    } catch (error) {
        console.error('Error purchasing tokens:', error);
        throw error;
    }
}
