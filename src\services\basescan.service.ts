import TelegramBot, { SendMessageOptions, InlineKeyboardButton } from 'node-telegram-bot-api';
import User from '../models/user.models';
import TradeModel  from '../models/trade.model';
import { getTokenInfoFromDexscreener } from '../services/dexscreener.service';
import tokenAbi from '../abis/ERC20ABI.json';
import { ethers } from 'ethers';
import { handleBaseDashboard } from '../screens/dashboards/base.dashboard';
import { generateBASEWallet } from './wallet.service';
import { INPUT_BASE_CONTRACT_ADDRESS, INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE } from '../botConvo';
import { SET_BASE_SPEND } from '../botConvo';
import { setCurrentContractAddress, getCurrentContractAddress } from '../globalState';
import { BlockchainType, TraderFactory } from './trading/TradingInterface';
import { checkIfHoneypot } from './honeypot/UnifiedHoneypotChecker';


interface TokenDetails {
  totalSupply?: any | null;
  marketCap?: number;
  checkHoneypot?: any;
  tokenInfo?: any;
  resultMessage?: any;
  tokenAddress?: any;
}

let localState: Record<string, TokenDetails> = {};
let messageIdStore: Record<string, number> = {};


// Create a provider instance
const providerUrl = process.env.BASE_PROVIDER_URL;
const provider = new ethers.providers.JsonRpcProvider(providerUrl);


if (!providerUrl) {
  throw new Error('PROVIDER is not defined in the environment variables.');
}


// Get total supply
const getTotalSupply = async (tokenAddress: string): Promise<string | null> => {
  try {
    // Create a contract instance
    const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, provider);

    // Call the decimals function
    const decimals = await tokenContract.decimals();

    // Call the totalSupply function
    const totalSupply = await tokenContract.totalSupply();

    // Convert the total supply from a BigNumber to a more readable format
    const formattedTotalSupply = ethers.utils.formatUnits(totalSupply, decimals);

    return formattedTotalSupply;
  } catch (error) {
    console.error('Error fetching total supply:', error);
    return null;
  }
};


// Handle BASE scan contract

export const handleBASEScanContract = async (bot: TelegramBot, chat_id: number, userAddress: string) => {
  try {
    if (!userAddress) {
      throw new Error('Invalid contract address.');
    }

    const previousContractAddress = getCurrentContractAddress(BlockchainType.BASE);
    if (previousContractAddress && localState[previousContractAddress]) {
        delete localState[previousContractAddress];
    }

    localState[userAddress] = localState[userAddress] || {};
    setCurrentContractAddress(BlockchainType.BASE, userAddress);

    // Check if the contract is a honeypot
    const honeypotResult = await checkIfHoneypot(userAddress, BlockchainType.BASE);

    // Get token info from Dexscreener
    const tokenInfo = await getTokenInfoFromDexscreener(userAddress);

    // Compute additional data
    const totalSupply = await getTotalSupply(userAddress);
    if (totalSupply === null) {
      throw new Error('Failed to fetch total supply.');
    }

    const totalSupplyNumber = Number(totalSupply);
    const priceUsdNumber = Number(tokenInfo.priceUsd);
    const marketCap = priceUsdNumber * totalSupplyNumber;

    // Utility function to format numbers for display
    const formatNumber = (num: number): string => {
        if (num >= 1_000_000_000_000) {
            return (num / 1_000_000_000_000).toFixed(2) + 'T'; // Trillions
        } else if (num >= 1_000_000_000) {
            return (num / 1_000_000_000).toFixed(2) + 'B'; // Billions
        } else if (num >= 1_000_000) {
            return (num / 1_000_000).toFixed(2) + 'M'; // Millions
        } else if (num >= 1_000) {
            return (num / 1_000).toFixed(2) + 'K'; // Thousands
        } else {
            return num.toFixed(2); // Less than a thousand
        }
    };

    const formattedTotalSupply = formatNumber(totalSupplyNumber);
    const formattedMarketCap = formatNumber(marketCap);
    const formattedLiquidity = formatNumber(Number(tokenInfo.liquidity.usd));

    // Use h1 (1 hour) price change if available, otherwise default to 0
    const priceChange = tokenInfo.priceChange.h1 || 0;
    const priceChangeEmoji = priceChange > 0 ? '🟢🟢🟢🟢🟢🟢' : '🔴🔴🔴🔴🔴🔴';

    const roundedBuyTax = Math.ceil(parseFloat(honeypotResult.buyTax));
    const roundedSellTax = Math.ceil(parseFloat(honeypotResult.sellTax));

    const resultMessage = `
🪙 ${tokenInfo.symbol} (${tokenInfo.name}) || 📈 ${priceChange > 0 ? '+' : ''}${priceChange}%  || 🏦 ${tokenInfo.dexId} || ${tokenInfo.chainId}

${priceChangeEmoji}  Price Change (1h): ${priceChange > 0 ? '+' : ''}${priceChange}%

CA: ${tokenInfo.address}
LP: ${tokenInfo.pairAddress}

💼 TOTAL SUPPLY: ${formattedTotalSupply} ${tokenInfo.symbol}
🏷️ MC: $${formattedMarketCap}
💧 LIQUIDITY: $${formattedLiquidity}
💵 PRICE: $${tokenInfo.priceUsd}

🔍 Honeypot/Rugpull Risk: ${honeypotResult.risk}
📉 Buy Tax: ${roundedBuyTax}%
📈 Sell Tax: ${roundedSellTax}%

⚠️⚠️⚠️⚠️ ${honeypotResult.details || 'No specific risks identified'}
    `;

    const user = await User.findOne({ chat_id }).exec();
    if (user) {
        user.baseCurrentTokenName = tokenInfo.name;
        user.baseCurrentContractAddress = userAddress;
        await user.save();
    }

    localState[userAddress] = {
        totalSupply: totalSupplyNumber, // Store raw number
        marketCap: marketCap,          // Store raw number
        checkHoneypot: honeypotResult,
        tokenInfo,
        resultMessage
    };

    const previousMessageId = messageIdStore[chat_id];
    if (previousMessageId) {
        await bot.deleteMessage(chat_id, previousMessageId);
    }

    const sentMessage = await bot.sendMessage(chat_id, resultMessage, {
      parse_mode: 'Markdown',
      reply_markup: createbaseKeyboard()
    });

    messageIdStore[chat_id] = sentMessage.message_id;
    return sentMessage.message_id;

  } catch (error) {
    console.error('Error scanning contract:', error);
    await bot.sendMessage(chat_id, 'There was an error scanning the contract. Please try again later.');
  }
};



// Create sell options keyboard
export const getBaseSellOptionsKeyboard = async (): Promise<InlineKeyboardButton[][]> => {
  return [
    [
      { text: 'Sell All', callback_data: JSON.stringify({ command: 'sell_all_base' }) },
      { text: 'Sell 25%', callback_data: JSON.stringify({ command: 'sell_25_base' }) }
    ],
    [
      { text: 'Sell 50%', callback_data: JSON.stringify({ command: 'sell_50_base' }) },
      { text: 'Sell 75%', callback_data: JSON.stringify({ command: 'sell_75_base' }) }
    ],
    [
      { text: 'Back', callback_data: JSON.stringify({ command: 'back_base' }) }
    ]
  ];
};




export const createbaseKeyboard = (): { inline_keyboard: InlineKeyboardButton[][] } => {
  return {
    inline_keyboard: [
      [
        { text: '◀️ Previous', callback_data: JSON.stringify({ command: 'previous_base' }) },
        { text: '▶️ Next', callback_data: JSON.stringify({ command: 'next_base' }) }
      ],
      [
        { text: '🔄 Refresh', callback_data: JSON.stringify({ command: 'refresh_base' }) },
        { text: '💸 Sell', callback_data: JSON.stringify({ command: 'sell_base' }) }
      ],
      [
        { text: '📈 Chart', callback_data: JSON.stringify({ command: 'chart_base' }) },
        { text: '💸 Spend X Base', callback_data: JSON.stringify({ command: 'spend_base' }) }
      ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ]
      // [
      //   { text: 'Advanced Trade', callback_data: JSON.stringify({ command: 'snipetrade_dashboard_base' }) }
      // ]
    ]
  };
};

// Update message with sell options
export const updatebaseMessageWithSellOptions = async (bot: TelegramBot, chatId: number, messageId: number) => {
  try {
    const sellOptions = await getBaseSellOptionsKeyboard();

    // Fetch the current contract address from the global state
    const contractAddress = getCurrentContractAddress(BlockchainType.BASE);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    // Fetch token details using the contract address
    const tokenDetails = getTokenDetails(contractAddress);

    if (!tokenDetails || !tokenDetails.resultMessage) {
      console.error('No result message found for the token address.');
      return;
    }
    // Update the message with the new sell options
    await bot.editMessageText(tokenDetails.resultMessage, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: sellOptions
      }
    });
  } catch (error) {
    console.error('Failed to update sell options:', error);
  }
};



// export const updateBaseMessageWithTradeOptions = async (
//   bot: TelegramBot,
//   chatId: number,
//   messageId: number
// ): Promise<void> => {
//   try {
//     const tradeOptions = createBaseTradeKeyboard();

//     // Fetch the current contract address from the global state
//     const contractAddress = getCurrentContractAddress();
//     if (!contractAddress) {
//       console.error('No contract address found in global state.');
//       return;
//     }

//     await bot.editMessageReplyMarkup(
//       { inline_keyboard: tradeOptions.inline_keyboard },
//       { chat_id: chatId, message_id: messageId }
//     );
//   } catch (error) {
//     console.error('Failed to update trade options:', error);
//   }
// };

export const mainbaseMenu = async (bot: TelegramBot, chatId: number, messageId: number) => {
  try {
    // Create the base keyboard options
    const baseOptions = createbaseKeyboard();

    // Fetch the current contract address from the global state
    const contractAddress = getCurrentContractAddress(BlockchainType.BASE);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    // Fetch token details using the contract address
    const tokenDetails = getTokenDetails(contractAddress);

    if (!tokenDetails || !tokenDetails.resultMessage) {
      console.error('No result message found for the token address.');
      return;
    }

    // Update the message with the new base options
    await bot.editMessageText(tokenDetails.resultMessage, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: baseOptions.inline_keyboard
      }
    });
  } catch (error) {
    console.error('Failed to update base menu:', error);
    await bot.sendMessage(chatId, 'Failed to update the menu. Please try again later.');
  }
};


export const handleGenerateNewBaseWallet = async (bot: TelegramBot, chatId: number) => {
  try {
    const user = await User.findOne({ chat_id: chatId });

    if (!user) {
      await bot.sendMessage(chatId, 'User not found.');
      return;
    }

    const newWallet = generateBASEWallet();

    await User.updateOne(
      { chat_id: chatId },
      { $set: { 'base_wallet.address': newWallet.address, 'base_wallet.private_key': newWallet.privateKey } }
    );

    // Send confirmation message
    await bot.sendMessage(
      chatId,
      `✅ You clicked "Generate New Wallet" in the base blockchain.\nNew wallet address: \`\`\`${newWallet.address}\`\`\`\nNew wallet Private Key: \`\`\`${newWallet.privateKey}\`\`\``
    );
    await handleBaseDashboard(bot, chatId);
  } catch (error) {
    console.error('Failed to generate new wallet:', error);
    await bot.sendMessage(chatId, 'There was an error generating a new wallet. Please try again later.');
  }
};

// Message ID storage
let promptMessageIdStore: Record<number, { promptId?: number, replyId?: number }> = {};

export const askForBaseContractAddress = async (bot: TelegramBot, chatId: number, messageId: number): Promise<string> => {
  try {
    // Save previous message IDs if any
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    // Send message asking for contract address
    const sentMessage = await bot.sendMessage(chatId, INPUT_BASE_CONTRACT_ADDRESS, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    // Update the message ID store
    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received contract address:', msg.text); // Log the received text
          // Save the reply message ID
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid contract address.'));
        }
      });
    });
  } catch (error) {
    console.error('Failed to ask for contract address:', error);
    throw error;
  }
};

export const askForBaseToken = async (bot: TelegramBot, chatId: number, messageId: number): Promise<string> => {
  try {
    // Save previous message IDs if any
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    // Send message asking for BSC token
    const sentMessage = await bot.sendMessage(chatId, INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    // Update the message ID store
    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received Base token:', msg.text); // Log the received text
          // Save the reply message ID
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid BSC token.'));
        }
      });
    });
  } catch (error) {
    console.error('Failed to ask for BSC token:', error);
    throw error;
  }
};

let tradesState: any[] = []; // Store the fetched trades
let currentIndex: number = 0; // Track the current trade index



export async function fetchAndDisplayActiveBaseTrades(bot: TelegramBot, chatId: number, blockchain: string) {
  try {
    // Find active trades for the specific user based on chatId and blockchain
    const tradesState = await TradeModel.find({ chatId, isSold: false, blockchain }).exec();
    let currentIndex = 0;

    if (tradesState.length === 0) {
      await bot.sendMessage(chatId, 'No active trades found!');
      return;
    }

    // Display the current trade information
    await displayCurrentBaseTrade(bot, chatId, tradesState, currentIndex);
  } catch (error) {
    console.error('Error fetching trades:', error);
    await bot.sendMessage(chatId, 'An error occurred while fetching trades.');
  }
}


export async function displayCurrentBaseTrade(bot: TelegramBot, chatId: number, tradesState: any[], index: number, messageId?: number) {
  const trade = tradesState[index];
  const message = `
<b>Contract:</b> ${trade.contractName} (${trade.contractAddress})
<b>Wallet:</b> ${trade.walletId}
<b>Buy Amount:</b> ${trade.buyAmount}  <b>Sell Amount:</b> ${trade.sellAmount}
<b>Sold:</b> ${trade.isSold ? 'Yes' : 'No'}
  `;

  // Update user details
  await User.findOneAndUpdate({ chat_id: chatId }, {
    baseCurrentContractAddress: trade.contractAddress,
    baseCurrentTokenName: trade.contractName,
  });


  // Update global state
  setCurrentContractAddress(BlockchainType.BASE, trade.contractAddress);

  const options = {
    parse_mode: "HTML" as const,
    disable_web_page_preview: true,
    reply_markup: {
      inline_keyboard: [
        [
          {
            text: "Sell 25%",
            callback_data: JSON.stringify({ command: "sell_25_base" }),
          },
          {
            text: "Sell 50%",
            callback_data: JSON.stringify({ command: "sell_50_base" }),
          },
        ],
        [
          {
            text: "Sell 75%",
            callback_data: JSON.stringify({ command: "sell_75_base" }),
          },
          {
            text: "Sell All",
            callback_data: JSON.stringify({ command: "sell_all_base" }),
          },
        ],
        [
          {
            text: "Previous",
            callback_data: JSON.stringify({ command: "previous_base" }),
          },
          {
            text: "Next",
            callback_data: JSON.stringify({ command: "next_base" }),
          },
        ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ],
      ],
    },
  };

  if (messageId) {
    await bot.editMessageText(message, { chat_id: chatId, message_id: messageId, ...options });
  } else {
    await bot.sendMessage(chatId, message, options);
  }
}

export const handleBaseRefresh = async (bot: TelegramBot, chatId: number, callbackQuery?: TelegramBot.CallbackQuery) => {
  try {
    const currentContractAddress = getCurrentContractAddress(BlockchainType.BASE);
    if (!currentContractAddress) {
      throw new Error('No contract address is currently set.');
    }

    await handleBASEScanContract(bot, chatId, currentContractAddress);

    // If a callbackQuery is provided, show an alert
    if (callbackQuery) {
      const callbackQueryId = callbackQuery.id;
      await bot.answerCallbackQuery(callbackQueryId, {
        text: "Token Refresh Successfully!",
        show_alert: true
      });
    }

  } catch (error) {
    console.error('Failed to refresh contract details:', error);
    await bot.sendMessage(chatId, 'Failed to refresh contract details. Please try again later.');
  }
};


export const handleBaseChart = async (bot: TelegramBot, chatId: number) => {
  try {
    const currentContractAddress = getCurrentContractAddress(BlockchainType.BASE);

    if (currentContractAddress) {
      const dexscreenerUrl = `https://dexscreener.com/base/${currentContractAddress}`;
      const chartMessage = await bot.sendMessage(chatId, `<a href="${dexscreenerUrl}">Click here to view the chart on Dexscreener</a>`, { parse_mode: 'HTML' });

      promptMessageIdStore[chatId] = { promptId: chartMessage.message_id };
    } else {
      const errorMessage = await bot.sendMessage(chatId, 'Error: Current contract address is not set.');

      promptMessageIdStore[chatId] = { promptId: errorMessage.message_id };
    }
  } catch (error) {
    console.error('Failed to handle chart action:', error);
  }
};


export const handleSpendBase = async (bot: TelegramBot, chatId: number): Promise<string> => {
  try {
    const sentMessage = await bot.sendMessage(chatId, SET_BASE_SPEND, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received buy amount:', msg.text);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid buy amount.'));
        }
      });
    });

  } catch (error) {
    console.error('Error handling spend base:', error);
    throw new Error('Failed to handle spend base action.');
  }
};


// export async function getbaseTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
//   const tokenContract = new ethers.Contract(
//       tokenAddress,
//       ['function balanceOf(address) view returns (uint)', 'function decimals() view returns (uint)'],
//       provider
//   );

//   const balance = await tokenContract.balanceOf(userAddress);

//   const tokenDecimals = await tokenContract.decimals();

//   const balanceWithDecimals = ethers.utils.formatUnits(balance, tokenDecimals);
//   console.log(balanceWithDecimals);

//   return balanceWithDecimals;
// }



export const handleBaseSellAll = async (bot: TelegramBot, chatId: number) => {
  try {
    const user = await User.findOne({ chat_id: chatId }).exec();
    if (!user) {
      throw new Error('User not found');
    }

    const { address: userAddress } = user.base_wallet;
    const tokenAddress = user.baseCurrentContractAddress || '';

    if (!tokenAddress) {
      throw new Error('No contract address found for user');
    }

    // Get token balance
    const tokenBalanceString = await getbaseTokenBalance(tokenAddress, userAddress);
    const tokenBalanceNumber = parseFloat(tokenBalanceString);

    if (isNaN(tokenBalanceNumber) || tokenBalanceNumber <= 0) {
      throw new Error('Invalid token balance');
    }

    // Get the trader for BASE blockchain
    const trader = TraderFactory.getTrader(BlockchainType.BASE);

    // Sell 100% of tokens
    const receipt = await trader.sellTokens(tokenAddress, 100, userAddress);

    if (receipt && receipt.transactionHash && receipt.blockNumber) {
      const { transactionHash, blockNumber } = receipt;
      await bot.sendMessage(chatId, `✅ Successfully sold all your tokens:\n\n` +
        `🔗 Transaction Hash: ${transactionHash}\n` +
        `🧱 Block Number: ${blockNumber}\n\n` +
        `👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${transactionHash}\nTo see your transaction`);
    } else {
      await bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
    }
  } catch (error) {
    console.error('Error selling all tokens:', error);
    // Type guard for `Error` type
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    await bot.sendMessage(chatId, `An error occurred while selling all tokens: ${errorMessage}`);
  }
};

async function getbaseTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {

  const tokenContract = new ethers.Contract(
    tokenAddress,
    [
      'function balanceOf(address owner) view returns (uint256)',
      'function decimals() view returns (uint8)'
    ],
    provider
  );

  // Fetch balance and decimals
  const [balance, decimals] = await Promise.all([
    tokenContract.balanceOf(userAddress),
    tokenContract.decimals()
  ]);

  // Convert balance to a human-readable format
  const formattedBalance = ethers.utils.formatUnits(balance, decimals);
  return formattedBalance;
}


export const sellBaseTokenPercentage = async (bot: TelegramBot, chatId: number, percentage: number) => {
  try {
    const user = await User.findOne({ chat_id: chatId }).exec();
    if (!user) {
      throw new Error('User not found');
    }

    const { address: userAddress } = user.base_wallet;
    const tokenAddress = user.baseCurrentContractAddress;

    if (!tokenAddress) {
      throw new Error('No contract address found for user');
    }

    // Ensure tokenBalance is converted to a number
    const tokenBalanceString = await getbaseTokenBalance(tokenAddress, userAddress);
    const tokenBalance = parseFloat(tokenBalanceString); // Convert to number

    if (isNaN(tokenBalance) || tokenBalance <= 0) {
      throw new Error('Insufficient token balance');
    }

    const amountToSell = tokenBalance * (percentage / 100);
    if (amountToSell <= 0) {
      throw new Error('Invalid percentage value');
    }

    console.log('Amount to sell:', amountToSell);

    // Get the trader for BASE blockchain
    const trader = TraderFactory.getTrader(BlockchainType.BASE);

    // Sell the specified percentage of tokens
    const receipt = await trader.sellTokens(tokenAddress, percentage, userAddress);

    if (receipt && receipt.transactionHash && receipt.blockNumber) {
      const { transactionHash, blockNumber } = receipt;
      await bot.sendMessage(chatId, `✅ Successfully sold ${percentage}% of your tokens:\n\n` +
        `🔗 Transaction Hash: ${transactionHash}\n` +
        `🧱 Block Number: ${blockNumber}\n\n` +
        `👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${transactionHash}\nTo see your transaction`);
    } else {
      await bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
    }
  } catch (error) {
    console.error('Error selling tokens:', error);
    // Type guard for `Error` type
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    await bot.sendMessage(chatId, `An error occurred while selling tokens: ${errorMessage}`);
  }
};


// Retrieve token details by contract address
export const getTokenDetails = (contractAddress: string): TokenDetails | undefined => {
  return localState[contractAddress];
};
export { handleBaseDashboard };

