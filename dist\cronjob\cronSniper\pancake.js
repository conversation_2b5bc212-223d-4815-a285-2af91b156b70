"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buyBscTokens = buyBscTokens;
const ethers_1 = require("ethers");
const snipe_model_1 = __importDefault(require("../../models/snipe.model")); // Adjust the path as needed
const user_models_1 = __importDefault(require("../../models/user.models")); // Adjust the path as needed
const trade_model_1 = __importDefault(require("../../models/trade.model")); // Adjust the path as needed
const axios_1 = __importDefault(require("axios"));
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const adminAddress = process.env.EVM_ADMIN_ADDRESS || '';
const providerUrl = process.env.BSC_PROVIDER_URL;
if (!adminAddress) {
    throw new Error('ADMIN_ADDRESS environment variable is not set');
}
if (!providerUrl) {
    throw new Error('bsc_PROVIDER_URL environment variable is not set');
}
const apiKey = process.env.MASTER_KEY || '';
const headers = {
    '0x-api-key': apiKey,
};
function getSwapQuote(url) {
    return __awaiter(this, void 0, void 0, function* () {
        const response = yield axios_1.default.get(url, { headers });
        return response.data;
    });
}
function buyBscTokens(chatId, contractAddress) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log("Starting token purchase on bscSwap");
        try {
            // Fetch user and Snipe configuration
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user) {
                throw new Error('User not found');
            }
            const snipe = yield snipe_model_1.default.findOne({
                chatId: chatId,
                contractAddress: contractAddress,
                blockchain: 'bsc'
            }).exec();
            if (!snipe) {
                throw new Error('Snipe configuration not found');
            }
            const { private_key, address } = user.bsc_wallet;
            const bscCurrentTokenName = user.bscCurrentTokenName;
            const spendbsc = snipe.config.spend_bsc;
            console.log(spendbsc);
            const slippagebsc = snipe.config.slippage_bsc;
            if (spendbsc === undefined || spendbsc === null || isNaN(spendbsc)) {
                throw new Error('Invalid spend_bsc value');
            }
            if (slippagebsc === undefined || slippagebsc === null || isNaN(slippagebsc)) {
                throw new Error('Invalid slippage_bsc value');
            }
            const buyToken = contractAddress;
            console.log("Buying", buyToken);
            const provider = new ethers_1.providers.JsonRpcProvider(providerUrl);
            const wallet = new ethers_1.Wallet(private_key, provider);
            const spendbscWei = ethers_1.ethers.utils.parseEther(spendbsc.toString());
            console.log(`Spend Amount (in Wei): ${spendbscWei.toString()}`);
            const sellToken = '******************************************';
            const feeRecipient = adminAddress;
            const buyTokenPercentageFee = 0.8;
            // Convert slippage percentage
            const slippagePercentage = slippagebsc / 100;
            let queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${spendbscWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;
            const url = `https://bsc.api.0x.org/swap/v1/quote?${queryParams}`;
            const quote = yield getSwapQuote(url);
            const buyTx = {
                to: quote.to,
                data: quote.data,
                value: ethers_1.ethers.BigNumber.from(quote.value),
                gasLimit: ethers_1.ethers.BigNumber.from(1000000),
                gasPrice: ethers_1.ethers.utils.parseUnits('1.5', 'gwei'),
            };
            console.log('Signing and sending the buy transaction...');
            const buyTxResponse = yield wallet.sendTransaction(buyTx);
            console.log(`Buy Transaction Hash: ${buyTxResponse.hash}`);
            const receipt = yield buyTxResponse.wait();
            console.log(`Buy Transaction Mined! Block Number: ${receipt.blockNumber}`);
            const existingTrade = yield trade_model_1.default.findOne({
                userId: user._id,
                contractAddress: buyToken,
                isSold: false,
            }).exec();
            if (existingTrade) {
                existingTrade.buyAmount += parseFloat(spendbsc.toString());
                yield existingTrade.save();
                console.log('Updated existing trade with new buy amount');
            }
            else {
                const newTrade = new trade_model_1.default({
                    userId: user._id,
                    chatId: chatId,
                    walletId: user.bsc_wallet.address,
                    contractAddress: buyToken,
                    contractName: bscCurrentTokenName,
                    buyAmount: parseFloat(spendbsc.toString()),
                    sellAmount: 0,
                    takeProfit: 0,
                    stopLoss: 0,
                    isSold: false,
                    blockchain: 'bsc'
                });
                yield newTrade.save();
                console.log('Created new trade entry');
            }
            yield user_models_1.default.updateOne({ _id: user._id }, { $push: { [`trades.${'bsc'}`]: existingTrade ? existingTrade._id : user._id } });
            console.log('User trades updated successfully');
            return receipt;
        }
        catch (error) {
            console.error('Error purchasing tokens:', error);
            throw error;
        }
    });
}
