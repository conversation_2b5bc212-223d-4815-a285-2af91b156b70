import TelegramBot, { SendMessageOptions } from 'node-telegram-bot-api';
import { ethers } from 'ethers';
import User from '../../models/user.models';

export const getEthBalance = async (address: string): Promise<string> => {
  const providerUrl = process.env.ETH_PROVIDER_URL;

  if (!providerUrl) {
    throw new Error('Provider is not defined in the .env file');
  }

  const providereth = new ethers.providers.JsonRpcProvider(providerUrl);
  const balanceETH = await providereth.getBalance(address);
  return parseFloat(ethers.utils.formatEther(balanceETH)).toFixed(6);
};


export const handleEthDashboard = async (bot: TelegramBot, chat_id: number) => {
  const user = await User.findOne({ chat_id });

  if (!user) {
    bot.sendMessage(chat_id, 'User not found.');
    return;
  }

  const firstName = user.first_name || 'User';
  const ethWallet = user.eth_wallet?.address || 'Not set';
  const balance = ethWallet !== 'Not set' ? await getEthBalance(ethWallet) : '0.0 ETH';

  const welcomeMessage = `👋 Welcome, ${firstName}, to your ETH Dashboard!
\n📍 **Address:** \`${ethWallet}\`
\n🔗 **Blockchain:** ethereum
\n💰 **Balance:** \`${balance} ETH\`
\n⚙️ **Settings:** [Antirug, etc.]`;

  const options: SendMessageOptions = {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_eth' }) }
        ],
        [
          { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
          { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_eth' }) }
        ],
        [
          { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_eth' }) },
          { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_eth_wallet' }) }
        ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ]
      ]
    }
  };

  const sentMessage = await bot.sendMessage(chat_id, welcomeMessage, options);

  await User.updateOne({ chat_id: chat_id }, {
    $set: {
      'eth_dashboard_message_id': sentMessage.message_id,
      'eth_dashboard_content': welcomeMessage,
      'eth_dashboard_markup': options.reply_markup
    }
  });
};
