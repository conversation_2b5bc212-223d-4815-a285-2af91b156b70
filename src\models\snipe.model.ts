
import mongoose, { Document, Schema } from 'mongoose';

export interface ISnipeConfig {
  retries_bsc: number;
  retries_base: number;
  retries_sol: number;
  retries_eth: number;
  slippage_bsc: number;
  spend_bsc: number;
  timeDelay_bsc: number;
  blockDelay_bsc: number;
  retriesOnFail_bsc: number;
  slippage_base?: number;
  spend_base?: number;
  timeDelay_base?: number;
  blockDelay_base?: number;
  retriesOnFail_base?: number;
  slippage_sol?: number;
  spend_sol?: number;
  timeDelay_sol?: number;
  blockDelay_sol?: number;
  retriesOnFail_sol?: number;
  slippage_eth?: number;
  spend_eth?: number;
  timeDelay_eth?: number;
  blockDelay_eth?: number;
  retriesOnFail_eth?: number;
  isConfigured?: boolean;
  isBought?: boolean;
  isExecuted_bsc?: boolean;
  isExecuted_base?: boolean;
  isExecuted_sol?: boolean;
  isExecuted_eth?: boolean;
}

export interface ISnipe extends Document {
  chatId: number;
  blockchain: string;
  contractAddress: string;
  config: ISnipeConfig;
  hasLiquidity?: boolean;
  lastPriceUpdate?: Date;
}

const SnipeSchema: Schema = new Schema({
  chatId: { type: Number, required: true },
  blockchain: { type: String, required: true },
  contractAddress: { type: String, required: true },
  config: {
    retries_base: { type: Number},
    retries_eth: { type: Number},
    retries_sol: { type: Number},
    retries_bsc: { type: Number},

    slippage_bsc: { type: Number },
    spend_bsc: { type: Number },
    timeDelay_bsc: { type: Number },
    blockDelay_bsc: { type: Number },
    retriesOnFail_bsc: { type: Number },
    slippage_base: { type: Number },
    spend_base: { type: Number },
    timeDelay_base: { type: Number },
    blockDelay_base: { type: Number },
    retriesOnFail_base: { type: Number },
    slippage_sol: { type: Number },
    spend_sol: { type: Number },
    timeDelay_sol: { type: Number },
    blockDelay_sol: { type: Number },
    retriesOnFail_sol: { type: Number },
    slippage_eth: { type: Number },
    spend_eth: { type: Number },
    timeDelay_eth: { type: Number },
    blockDelay_eth: { type: Number },
    retriesOnFail_eth: { type: Number },
    isConfigured: { type: Boolean, default: false },
    isBought: { type: Boolean, default: false },
    isExecuted_bsc: { type: Boolean, default: false },
    isExecuted_base: { type: Boolean, default: false },
    isExecuted_sol: { type: Boolean, default: false },
    isExecuted_eth: { type: Boolean, default: false },
  },
  hasLiquidity: { type: Boolean, default: false },
  lastPriceUpdate: { type: Date },
});

const Snipe = mongoose.model<ISnipe>('Snipe', SnipeSchema);
export default Snipe;