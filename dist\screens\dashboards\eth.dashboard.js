"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleEthDashboard = exports.getEthBalance = void 0;
const ethers_1 = require("ethers");
const user_models_1 = __importDefault(require("../../models/user.models"));
const getEthBalance = (address) => __awaiter(void 0, void 0, void 0, function* () {
    const providerUrl = process.env.ETH_PROVIDER_URL;
    if (!providerUrl) {
        throw new Error('Provider is not defined in the .env file');
    }
    const providereth = new ethers_1.ethers.providers.JsonRpcProvider(providerUrl);
    const balanceETH = yield providereth.getBalance(address);
    return parseFloat(ethers_1.ethers.utils.formatEther(balanceETH)).toFixed(6);
});
exports.getEthBalance = getEthBalance;
const handleEthDashboard = (bot, chat_id) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const user = yield user_models_1.default.findOne({ chat_id });
    if (!user) {
        bot.sendMessage(chat_id, 'User not found.');
        return;
    }
    const firstName = user.first_name || 'User';
    const ethWallet = ((_a = user.eth_wallet) === null || _a === void 0 ? void 0 : _a.address) || 'Not set';
    const balance = ethWallet !== 'Not set' ? yield (0, exports.getEthBalance)(ethWallet) : '0.0 ETH';
    const welcomeMessage = `👋 Welcome, ${firstName}, to your ETH Dashboard!
\n📍 **Address:** \`${ethWallet}\`
\n🔗 **Blockchain:** ethereum
\n💰 **Balance:** \`${balance} ETH\`
\n⚙️ **Settings:** [Antirug, etc.]`;
    const options = {
        parse_mode: 'Markdown',
        reply_markup: {
            inline_keyboard: [
                [
                    { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_eth' }) }
                ],
                [
                    { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
                    { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_eth' }) }
                ],
                [
                    { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_eth' }) },
                    { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_eth_wallet' }) }
                ],
                [
                    {
                        text: "* Dismiss message",
                        callback_data: JSON.stringify({
                            command: "dismiss_message",
                        }),
                    },
                ]
            ]
        }
    };
    const sentMessage = yield bot.sendMessage(chat_id, welcomeMessage, options);
    yield user_models_1.default.updateOne({ chat_id: chat_id }, {
        $set: {
            'eth_dashboard_message_id': sentMessage.message_id,
            'eth_dashboard_content': welcomeMessage,
            'eth_dashboard_markup': options.reply_markup
        }
    });
});
exports.handleEthDashboard = handleEthDashboard;
