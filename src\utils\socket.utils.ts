/**
 * Socket.IO Utilities for Web Dashboard
 *
 * This module provides utilities for working with Socket.IO connections
 * to send real-time updates to users accessing the web dashboard.
 *
 * NOTE: This is NOT used for Telegram bot communication, which uses HTTP.
 * Socket.IO is only for browser-based real-time updates on the web interface.
 */

import { Server, Socket } from 'socket.io';
import { logger } from '../services/logger/LoggerService';

// Map to track user connections (chatId -> socketId)
export const userSockets: Map<number, Set<string>> = new Map();

// Map to track socket connections (socketId -> chatId)
export const socketUsers: Map<string, number> = new Map();

// Socket.IO server instance
let io: Server | null = null;

/**
 * Initialize the Socket.IO utilities with a server instance
 * @param server Socket.IO server instance
 */
export function initializeSocketUtils(server: Server): void {
  io = server;
}

/**
 * Register a socket connection for a user
 * @param socket Socket instance
 * @param chatId User's chat ID
 */
export function registerUserSocket(socket: Socket, chatId: number): void {
  if (!userSockets.has(chatId)) {
    userSockets.set(chatId, new Set());
  }
  userSockets.get(chatId)?.add(socket.id);
  socketUsers.set(socket.id, chatId);

  logger.debug(`Registered socket for user ${chatId}`, { socketId: socket.id, chatId });
}

/**
 * Unregister a socket connection
 * @param socketId Socket ID
 */
export function unregisterSocket(socketId: string): void {
  const chatId = socketUsers.get(socketId);
  if (chatId) {
    // Remove this socket from the user's set of sockets
    const userSocketSet = userSockets.get(chatId);
    if (userSocketSet) {
      userSocketSet.delete(socketId);
      if (userSocketSet.size === 0) {
        userSockets.delete(chatId);
      }
    }
    socketUsers.delete(socketId);

    logger.debug(`Unregistered socket for user ${chatId}`, { socketId, chatId });
  }
}

/**
 * Send data to a specific user via all their connected sockets
 * @param chatId User's chat ID
 * @param data Data to send
 * @param event Event name
 */
export function sendToUser(chatId: number, data: any, event: string): void {
  if (!io) {
    logger.warn('Socket.IO server not initialized');
    return;
  }

  const socketIds = userSockets.get(chatId);
  if (!socketIds || socketIds.size === 0) {
    // User has no active socket connections
    return;
  }

  // Send to all of the user's connected sockets
  for (const socketId of socketIds) {
    const socket = io.sockets.sockets.get(socketId);
    if (socket) {
      socket.emit(event, data);
      logger.debug(`Sent ${event} to user ${chatId}`, { socketId, chatId, event });
    }
  }
}

/**
 * Check if a user has any active socket connections
 * @param chatId User's chat ID
 * @returns True if the user has active connections
 */
export function isUserConnected(chatId: number): boolean {
  const socketIds = userSockets.get(chatId);
  return !!socketIds && socketIds.size > 0;
}

/**
 * Get the number of active connections for a user
 * @param chatId User's chat ID
 * @returns Number of active connections
 */
export function getUserConnectionCount(chatId: number): number {
  const socketIds = userSockets.get(chatId);
  return socketIds ? socketIds.size : 0;
}

/**
 * Get all connected users
 * @returns Array of chat IDs
 */
export function getConnectedUsers(): number[] {
  return Array.from(userSockets.keys());
}

/**
 * Get all socket IDs for a user
 * @param chatId User's chat ID
 * @returns Array of socket IDs
 */
export function getUserSocketIds(chatId: number): string[] {
  const socketIds = userSockets.get(chatId);
  return socketIds ? Array.from(socketIds) : [];
}
