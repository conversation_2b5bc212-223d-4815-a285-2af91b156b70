/**
 * Enhanced Logging Service
 *
 * Provides structured logging with support for different log levels,
 * file output, and performance metrics.
 */

import fs from 'fs';
import path from 'path';
import os from 'os';
import cluster from 'cluster';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, any>;
  service?: string;
  workerId?: string;
  hostname?: string;
}

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',

  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m'
};

// Performance metrics tracking
interface PerformanceMetrics {
  startTime: number;
  logCount: Record<LogLevel, number>;
  lastFlushTime: number;
  totalFlushes: number;
  totalWriteTime: number;
  maxBufferSize: number;
}

export class Logger {
  private static instance: Logger;
  private logLevel: LogLevel;
  private logToFile: boolean;
  private logFilePath: string;
  private logBuffer: LogEntry[] = [];
  private flushInterval: NodeJS.Timeout | null = null;
  private rotationInterval: NodeJS.Timeout | null = null;
  private metrics: PerformanceMetrics;
  private serviceName: string;
  private hostname: string;
  private workerId: string;
  private maxBufferSize: number;
  private flushIntervalMs: number;
  private rotateLogsIntervalHours: number;
  private maxLogFileSizeMb: number;
  private maxLogFiles: number;

  private constructor() {
    // Initialize configuration
    this.logLevel = this.getLogLevelFromEnv();
    this.logToFile = process.env.LOG_TO_FILE === 'true';
    this.logFilePath = process.env.LOG_FILE_PATH || path.join(process.cwd(), 'logs', 'app.log');
    this.serviceName = process.env.SERVICE_NAME || 'easybot';
    this.hostname = os.hostname();
    this.workerId = cluster.worker?.id?.toString() || 'main';
    this.maxBufferSize = parseInt(process.env.LOG_BUFFER_SIZE || '100', 10);
    this.flushIntervalMs = parseInt(process.env.LOG_FLUSH_INTERVAL_MS || '5000', 10);
    this.rotateLogsIntervalHours = parseInt(process.env.LOG_ROTATION_HOURS || '24', 10);
    this.maxLogFileSizeMb = parseInt(process.env.MAX_LOG_FILE_SIZE_MB || '10', 10);
    this.maxLogFiles = parseInt(process.env.MAX_LOG_FILES || '5', 10);

    // Initialize metrics
    this.metrics = {
      startTime: Date.now(),
      logCount: {
        [LogLevel.DEBUG]: 0,
        [LogLevel.INFO]: 0,
        [LogLevel.WARN]: 0,
        [LogLevel.ERROR]: 0,
        [LogLevel.FATAL]: 0
      },
      lastFlushTime: 0,
      totalFlushes: 0,
      totalWriteTime: 0,
      maxBufferSize: 0
    };

    // Create logs directory if it doesn't exist
    if (this.logToFile) {
      const logDir = path.dirname(this.logFilePath);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
    }

    // Set up buffer flushing
    this.flushInterval = setInterval(() => this.flushLogs(), this.flushIntervalMs);

    // Set up log rotation
    if (this.logToFile) {
      this.rotationInterval = setInterval(
        () => this.rotateLogFiles(),
        this.rotateLogsIntervalHours * 60 * 60 * 1000
      );
    }

    this.info('Logger initialized', {
      level: LogLevel[this.logLevel],
      logToFile: this.logToFile,
      logFilePath: this.logFilePath,
      flushInterval: this.flushIntervalMs,
      maxBufferSize: this.maxBufferSize
    });
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private getLogLevelFromEnv(): LogLevel {
    const envLevel = process.env.LOG_LEVEL?.toUpperCase();
    switch (envLevel) {
      case 'DEBUG': return LogLevel.DEBUG;
      case 'INFO': return LogLevel.INFO;
      case 'WARN': return LogLevel.WARN;
      case 'ERROR': return LogLevel.ERROR;
      case 'FATAL': return LogLevel.FATAL;
      default: return LogLevel.INFO; // Default to INFO
    }
  }

  private getLevelColor(level: LogLevel): string {
    switch (level) {
      case LogLevel.DEBUG: return colors.cyan;
      case LogLevel.INFO: return colors.green;
      case LogLevel.WARN: return colors.yellow;
      case LogLevel.ERROR: return colors.red;
      case LogLevel.FATAL: return colors.bgRed + colors.white;
      default: return colors.white;
    }
  }

  private formatLogEntryForConsole(entry: LogEntry): string {
    const levelString = LogLevel[entry.level];
    const levelColor = this.getLevelColor(entry.level);
    const timestamp = entry.timestamp.split('T')[1].replace('Z', '');

    let logString = `${colors.dim}${timestamp}${colors.reset} ${levelColor}[${levelString}]${colors.reset} ${entry.message}`;

    if (entry.service || entry.workerId) {
      logString += ` ${colors.dim}(${entry.service || this.serviceName}:${entry.workerId || this.workerId})${colors.reset}`;
    }

    if (entry.context) {
      try {
        const contextStr = JSON.stringify(entry.context);
        if (contextStr !== '{}') {
          logString += ` ${colors.dim}${contextStr}${colors.reset}`;
        }
      } catch (error) {
        logString += ` ${colors.red}[Context serialization failed]${colors.reset}`;
      }
    }

    return logString;
  }

  private formatLogEntryForFile(entry: LogEntry): string {
    const levelString = LogLevel[entry.level];
    let logString = `[${entry.timestamp}] [${levelString}] [${entry.service || this.serviceName}:${entry.workerId || this.workerId}] ${entry.message}`;

    if (entry.context) {
      try {
        const contextStr = JSON.stringify(entry.context);
        if (contextStr !== '{}') {
          logString += ` ${contextStr}`;
        }
      } catch (error) {
        logString += ` [Context serialization failed]`;
      }
    }

    return logString;
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);

    // Update metrics
    this.metrics.logCount[entry.level]++;
    this.metrics.maxBufferSize = Math.max(this.metrics.maxBufferSize, this.logBuffer.length);

    // If buffer gets too large, flush immediately
    if (this.logBuffer.length >= this.maxBufferSize) {
      this.flushLogs();
    }
  }

  private flushLogs(): void {
    if (this.logBuffer.length === 0) return;

    const startTime = Date.now();

    if (this.logToFile) {
      try {
        const logStrings = this.logBuffer.map(entry => this.formatLogEntryForFile(entry));
        fs.appendFileSync(this.logFilePath, logStrings.join('\n') + '\n');

        // Check if log file needs rotation based on size
        this.checkLogFileSize();
      } catch (error) {
        console.error(`${colors.red}Failed to write to log file:${colors.reset}`, error);
      }
    }

    // Update metrics
    this.metrics.lastFlushTime = Date.now();
    this.metrics.totalFlushes++;
    this.metrics.totalWriteTime += (Date.now() - startTime);

    this.logBuffer = [];
  }

  private checkLogFileSize(): void {
    try {
      const stats = fs.statSync(this.logFilePath);
      const fileSizeMb = stats.size / (1024 * 1024);

      if (fileSizeMb >= this.maxLogFileSizeMb) {
        this.rotateLogFiles();
      }
    } catch (error) {
      // File might not exist yet, that's fine
    }
  }

  private rotateLogFiles(): void {
    if (!this.logToFile) return;

    try {
      // Check if the log file exists
      if (!fs.existsSync(this.logFilePath)) return;

      // Get the directory and base filename
      const dir = path.dirname(this.logFilePath);
      const ext = path.extname(this.logFilePath);
      const base = path.basename(this.logFilePath, ext);
      const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');

      // Rotate existing log files
      for (let i = this.maxLogFiles - 1; i >= 0; i--) {
        const oldFile = path.join(dir, `${base}.${i}${ext}`);
        if (fs.existsSync(oldFile)) {
          if (i === this.maxLogFiles - 1) {
            // Delete the oldest log file
            fs.unlinkSync(oldFile);
          } else {
            // Rename the file to the next number
            const newFile = path.join(dir, `${base}.${i + 1}${ext}`);
            fs.renameSync(oldFile, newFile);
          }
        }
      }

      // Rename the current log file
      const newFile = path.join(dir, `${base}.0${ext}`);
      fs.renameSync(this.logFilePath, newFile);

      // Create a new empty log file
      fs.writeFileSync(this.logFilePath, `Log file created at ${timestamp}\n`);

      this.info('Log file rotated', { oldFile: newFile, newFile: this.logFilePath });
    } catch (error) {
      console.error(`${colors.red}Failed to rotate log files:${colors.reset}`, error);
    }
  }

  public log(level: LogLevel, message: string, context?: Record<string, any>): void {
    if (level < this.logLevel) return;

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      service: this.serviceName,
      workerId: this.workerId,
      hostname: this.hostname
    };

    // Always log to console with colors
    console.log(this.formatLogEntryForConsole(entry));

    // Add to buffer for file logging
    if (this.logToFile) {
      this.addToBuffer(entry);
    }
  }

  public debug(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  public info(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context);
  }

  public warn(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context);
  }

  public error(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.ERROR, message, context);
  }

  public fatal(message: string, context?: Record<string, any>): void {
    this.log(LogLevel.FATAL, message, context);
  }

  /**
   * Log performance metrics for a function or operation
   * @param operation Name of the operation
   * @param startTime Start time in milliseconds (from Date.now())
   * @param context Additional context
   */
  public logPerformance(operation: string, startTime: number, context?: Record<string, any>): void {
    const duration = Date.now() - startTime;
    this.debug(`Performance: ${operation} completed in ${duration}ms`, {
      ...context,
      operation,
      durationMs: duration,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get current logger metrics
   */
  public getMetrics(): Record<string, any> {
    const uptime = Date.now() - this.metrics.startTime;
    const avgFlushTime = this.metrics.totalFlushes > 0
      ? this.metrics.totalWriteTime / this.metrics.totalFlushes
      : 0;

    return {
      uptime,
      uptimeHuman: this.formatUptime(uptime),
      logCounts: this.metrics.logCount,
      totalLogs: Object.values(this.metrics.logCount).reduce((a, b) => a + b, 0),
      flushes: this.metrics.totalFlushes,
      avgFlushTimeMs: avgFlushTime,
      maxBufferSize: this.metrics.maxBufferSize,
      currentBufferSize: this.logBuffer.length
    };
  }

  private formatUptime(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`;
  }

  public shutdown(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }

    if (this.rotationInterval) {
      clearInterval(this.rotationInterval);
      this.rotationInterval = null;
    }

    this.flushLogs();
    this.info('Logger shutdown', this.getMetrics());
  }
}

// Export a singleton instance
export const logger = Logger.getInstance();
