/**
 * BSC Sniper Board
 *
 * This module provides the UI and functionality for sniping tokens on the Binance Smart Chain.
 * It uses the new worker system for efficient processing.
 */

import TelegramBot, { SendMessageOptions } from 'node-telegram-bot-api';
import Snipe, { ISnipe } from '../../models/snipe.model';
import { getTokenInfoFromDexscreener } from '../../services/dexscreener.service';
import { BlockchainType, TraderFactory } from '../../services/trading/TradingInterface';
import { logger } from '../../services/logger/LoggerService';
import { startPeriodicSnipeUpdates } from '../../cronjob/snipemonitor';

const createBscKeyboard = (): { inline_keyboard: TelegramBot.InlineKeyboardButton[][] } => ({
  inline_keyboard: [
    [
      { text: '📊 Scan Another Contract', callback_data: JSON.stringify({ command: 'scan_snipe_bsc' }) },
      { text: 'Execute Snipe', callback_data: JSON.stringify({ command: 'bsc_snipe_execute' }) },
    ],
    [
      { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'bsc_snipe_config' }) },
    ],
    [
      {
        text: "* Dismiss message",
        callback_data: JSON.stringify({
          command: "dismiss_message",
        }),
      },
    ]
  ]
});

const createConfigKeyboard = (): { inline_keyboard: TelegramBot.InlineKeyboardButton[][] } => ({
  inline_keyboard: [
    [
      { text: 'Slippage', callback_data: JSON.stringify({ command: 'set_slippage_bsc' }) },
      { text: 'Spend', callback_data: JSON.stringify({ command: 'set_spend_bsc' }) },
    ],
    [
      { text: 'Time Delay', callback_data: JSON.stringify({ command: 'set_time_delay_bsc' }) },
      { text: 'Block Delay', callback_data: JSON.stringify({ command: 'set_block_delay_bsc' }) },
    ],
    [
      { text: 'Retries on Fail', callback_data: JSON.stringify({ command: 'set_retries_bsc' }) },
    ],
    [
      { text: '⬅️ Back', callback_data: JSON.stringify({ command: 'back_to_main_snipe_bsc' }) },
    ]
  ]
});

const promptMessageIdStore: { [chatId: number]: { promptId?: number, replyId?: number } } = {};

const askForbscSniperInput = async (bot: TelegramBot, chatId: number, promptMessage: string, field: string): Promise<string> => {
  try {
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    const sentMessage = await bot.sendMessage(chatId, promptMessage, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log(`Received ${field}:`, msg.text);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error(`Invalid ${field}.`));
        }
      });
    });
  } catch (error) {
    console.error(`Failed to ask for ${field}:`, error);
    throw error;
  }
};

/**
 * User configuration context for BSC sniper
 * This stores user configuration state by chat ID, blockchain, and contract address
 */
const userConfigContext: {
  [chatId: number]: {
    [blockchain: string]: {
      [contractAddress: string]: any
    }
  }
} = {};

/**
 * Display the BSC sniper screen
 * @param bot Telegram bot instance
 * @param chatId Chat ID
 * @param contractAddress Contract address
 */
export const bscSniperScreen = async (bot: TelegramBot, chatId: number, contractAddress: string) => {
  try {
    // Get token information
    const tokenInfo = await getTokenInfoFromDexscreener(contractAddress, BlockchainType.BSC);

    // Check if the token is a honeypot
    const trader = TraderFactory.getTrader(BlockchainType.BSC);
    const honeypotCheck = await trader.checkHoneypot(contractAddress);

    // Extract token details
    const {
      symbol = 'Unknown',
      name = 'Unknown',
      priceUsd = 'Unknown',
      liquidity = 'Unknown',
      pairAddress = 'Unknown',
    } = tokenInfo || {};

    // Create token details message
    let tokenDetails = `
🏷 **Token Information:**
📍 **Symbol:** ${symbol}
📛 **Name:** ${name}
💵 **Price:** $${priceUsd}
💧 **Liquidity:** $${typeof liquidity === 'object' && liquidity !== null ? liquidity.usd : 'Unknown'}
🔗 **Pair Address:** ${pairAddress}
    `;

    // Add honeypot warning if applicable
    if (honeypotCheck.isHoneypot) {
      tokenDetails += `
⚠️ **HONEYPOT WARNING** ⚠️
This token appears to be a honeypot. Sniping is not recommended.
Reason: ${honeypotCheck.honeypotReason || 'Unknown'}
      `;
    }

    const options: SendMessageOptions = {
      parse_mode: 'Markdown',
      reply_markup: createBscKeyboard()
    };

    // Check if token has liquidity
    const hasLiquidity = tokenInfo?.liquidity?.usd > 0;

    if (!hasLiquidity) {
      // No liquidity found, set up for sniping
      await bot.sendMessage(chatId, `⚠️ No liquidity found for the token ${contractAddress}. Ready to snipe when liquidity is added.`, options);
      await saveSnipeConfig(chatId, contractAddress, false);

      logger.info('BSC snipe configuration saved', {
        chatId,
        contractAddress,
        hasLiquidity: false
      });
    } else {
      // Liquidity found, display token details
      const initialMessage = await bot.sendMessage(chatId, tokenDetails, options);

      // Initialize user context for this chat, blockchain, and contract
      userConfigContext[chatId] = userConfigContext[chatId] || {};
      userConfigContext[chatId]['bsc'] = userConfigContext[chatId]['bsc'] || {};
      userConfigContext[chatId]['bsc'][contractAddress] = {
        isConfigured: true,
        initialMessageId: initialMessage.message_id
      };

      logger.info('BSC token with liquidity displayed', {
        chatId,
        contractAddress,
        hasLiquidity: true,
        messageId: initialMessage.message_id
      });
    }

    // Make sure the user context is initialized
    userConfigContext[chatId] = userConfigContext[chatId] || {};
    userConfigContext[chatId]['bsc'] = userConfigContext[chatId]['bsc'] || {};
    userConfigContext[chatId]['bsc'][contractAddress] = userConfigContext[chatId]['bsc'][contractAddress] || { isConfigured: true };

    // Set up callback handler for this specific message
    const callbackHandler = async (callbackQuery: TelegramBot.CallbackQuery) => {
      try {
        // Extract message details
        const messageId = callbackQuery.message?.message_id;
        if (!messageId) return;

        // Only process callbacks for this specific chat
        if (callbackQuery.message?.chat.id !== chatId) {
          return;
        }

        // Check if this is a message we should handle
        const isOurMessage = userConfigContext[chatId]?.['bsc']?.[contractAddress]?.initialMessageId === messageId ||
                            callbackQuery.message?.text?.includes(contractAddress);

        if (!isOurMessage) {
          return;
        }

        const currentReplyMarkup = callbackQuery.message?.reply_markup || {};
        const data = JSON.parse(callbackQuery.data || '{}');
        const command = data.command;

        // Acknowledge the callback query
        await bot.answerCallbackQuery(callbackQuery.id);

        switch (command) {
          case 'bsc_snipe_config':
            // Switch to config keyboard
            userConfigContext[chatId] = userConfigContext[chatId] || {};
            userConfigContext[chatId]['bsc'] = userConfigContext[chatId]['bsc'] || {};
            userConfigContext[chatId]['bsc'][contractAddress] = userConfigContext[chatId]['bsc'][contractAddress] || {};
            userConfigContext[chatId]['bsc'][contractAddress].configMode = true;

            const configKeyboard = createConfigKeyboard();

            if (JSON.stringify(configKeyboard) !== JSON.stringify(currentReplyMarkup)) {
              await bot.editMessageReplyMarkup(
                configKeyboard,
                { chat_id: chatId, message_id: messageId }
              );
            }
            break;

          case 'back_to_main_snipe_bsc':
            // Switch back to main keyboard
            const mainKeyboard = createBscKeyboard();

            if (JSON.stringify(mainKeyboard) !== JSON.stringify(currentReplyMarkup)) {
              await bot.editMessageReplyMarkup(
                mainKeyboard,
                { chat_id: chatId, message_id: messageId }
              );
            }
            break;

          case 'bsc_snipe_execute':
            // Execute the snipe
            if (hasLiquidity) {
              await bot.answerCallbackQuery(callbackQuery.id, {
                text: '🚫 Token has already been launched.',
                show_alert: true
              });
            } else {
              const snipe = await Snipe.findOne({ chatId, contractAddress, blockchain: 'bsc' });

              if (snipe) {
                // Start the snipe monitor
                startPeriodicSnipeUpdates(bot);

                await bot.sendMessage(
                  chatId,
                  '🔫 Snipe configured and ready! Monitoring for liquidity...\n\n' +
                  'The bot will automatically execute the snipe when liquidity is detected.'
                );

                logger.info('BSC snipe execution started', {
                  chatId,
                  contractAddress,
                  snipeId: snipe._id
                });
              } else {
                await bot.sendMessage(chatId, '❌ Snipe configuration not found. Please configure the snipe first.');
              }
            }
            break;

          case 'review_config_bsc':
            // Review the current configuration
            const configMessageId = userConfigContext[chatId]?.['bsc']?.[contractAddress]?.initialMessageId;
            if (configMessageId) {
              await reviewMessage(bot, chatId, configMessageId, contractAddress);
            }
            break;

          case 'set_slippage_bsc':
            // Set slippage
            const slippage = await askForbscSniperInput(bot, chatId, 'Enter new slippage percentage:', 'slippage');
            await updateSnipeConfig(chatId, contractAddress, { slippage_bsc: parseInt(slippage, 10) });
            await bot.sendMessage(chatId, `✅ Slippage set to ${slippage}%`);
            break;

          case 'set_spend_bsc':
            // Set spend amount
            const spend = await askForbscSniperInput(bot, chatId, 'Enter new spend amount:', 'spend');
            await updateSnipeConfig(chatId, contractAddress, { spend_bsc: parseFloat(spend) });
            await bot.sendMessage(chatId, `✅ Spend amount set to ${spend} BSC`);
            break;

          case 'set_time_delay_bsc':
            // Set time delay
            const timeDelay = await askForbscSniperInput(bot, chatId, 'Enter new time delay (in milliseconds):', 'time delay');
            await updateSnipeConfig(chatId, contractAddress, { timeDelay_bsc: parseInt(timeDelay, 10) });
            await bot.sendMessage(chatId, `✅ Time delay set to ${timeDelay} ms`);
            break;

          case 'set_block_delay_bsc':
            // Set block delay
            const blockDelay = await askForbscSniperInput(bot, chatId, 'Enter new block delay:', 'block delay');
            await updateSnipeConfig(chatId, contractAddress, { blockDelay_bsc: parseInt(blockDelay, 10) });
            await bot.sendMessage(chatId, `✅ Block delay set to ${blockDelay}`);
            break;

          case 'set_retries_bsc':
            // Set retry count
            const retries = await askForbscSniperInput(bot, chatId, 'Enter new retry count:', 'retries');
            await updateSnipeConfig(chatId, contractAddress, { retries_bsc: parseInt(retries, 10) });
            await bot.sendMessage(chatId, `✅ Retries set to ${retries}`);
            break;

          case 'dismiss_message':
            // Delete the message
            try {
              await bot.deleteMessage(chatId, messageId);
              // Remove the callback handler when the message is dismissed
              bot.removeListener('callback_query', callbackHandler);
              logger.debug('BSC sniper message dismissed', { chatId, messageId });
            } catch (error) {
              logger.error('Failed to delete BSC sniper message', {
                chatId,
                messageId,
                error: (error as Error).message
              });
            }
            break;
        }
      } catch (error) {
        logger.error('Error handling BSC sniper callback', {
          chatId,
          contractAddress,
          error: (error as Error).message
        });
        await bot.sendMessage(chatId, `⚠️ An error occurred: ${(error as Error).message}`);
      }
    };

    // Add the callback handler
    bot.on('callback_query', callbackHandler);

  } catch (error) {
    logger.error('Error in bscSniperScreen', {
      chatId,
      contractAddress,
      error: (error as Error).message
    });
    await bot.sendMessage(chatId, `❌ An error occurred: ${(error as Error).message}`);
  }
};

/**
 * Update snipe configuration
 * @param chatId Chat ID
 * @param contractAddress Contract address
 * @param updates Configuration updates
 */
const updateSnipeConfig = async (chatId: number, contractAddress: string, updates: Partial<ISnipe['config']>): Promise<void> => {
  try {
    // Find the snipe configuration
    let snipe = await Snipe.findOne({ chatId, contractAddress, blockchain: 'bsc' });

    if (snipe) {
      // Update the configuration
      updates.isConfigured = true;
      snipe.config = { ...snipe.config, ...updates };
      await snipe.save();

      logger.info('Updated BSC snipe config', {
        chatId,
        contractAddress,
        updates: Object.keys(updates),
        snipeId: snipe._id
      });
    } else {
      logger.warn('Snipe configuration not found for update', {
        chatId,
        contractAddress,
        blockchain: 'bsc'
      });
    }
  } catch (error) {
    logger.error('Error updating snipe config', {
      chatId,
      contractAddress,
      error: (error as Error).message
    });
  }
};

/**
 * Save a new snipe configuration
 * @param chatId Chat ID
 * @param contractAddress Contract address
 * @param isBought Whether the token has been bought
 */
const saveSnipeConfig = async (chatId: number, contractAddress: string, isBought: boolean): Promise<void> => {
  try {
    // Check if a snipe with the given contractAddress already exists for this blockchain and chat
    const existingSnipe = await Snipe.findOne({
      chatId,
      contractAddress,
      blockchain: 'bsc'
    });

    if (existingSnipe) {
      logger.info('BSC snipe config already exists', {
        chatId,
        contractAddress,
        snipeId: existingSnipe._id
      });
      return;
    }

    // Create a new snipe with default configuration
    const snipe = new Snipe({
      chatId,
      contractAddress,
      blockchain: 'bsc',
      hasLiquidity: isBought,
      config: {
        isConfigured: true,
        isBought: false,
        isExecuted_bsc: false,
        slippage_bsc: 5, // Default slippage
        spend_bsc: 0.1, // Default spend amount
        timeDelay_bsc: 0, // Default time delay
        blockDelay_bsc: 0, // Default block delay
        retries_bsc: 3 // Default retries
      }
    });

    await snipe.save();

    logger.info('New BSC snipe config saved', {
      chatId,
      contractAddress,
      snipeId: snipe._id
    });
  } catch (error) {
    logger.error('Error saving BSC snipe config', {
      chatId,
      contractAddress,
      error: (error as Error).message
    });
  }
};


/**
 * Review and display snipe configuration
 * @param bot Telegram bot instance
 * @param chatId Chat ID
 * @param messageId Message ID to edit
 * @param contractAddress Contract address
 */
const reviewMessage = async (bot: TelegramBot, chatId: number, messageId: number, contractAddress: string): Promise<void> => {
  try {
    // Find the snipe configuration
    const snipe = await Snipe.findOne({ chatId, contractAddress, blockchain: 'bsc' });

    if (snipe) {
      // Get token information
      const tokenInfo = await getTokenInfoFromDexscreener(contractAddress, BlockchainType.BSC);

      // Get configuration
      const config = snipe.config;

      // Create review message
      const reviewText = `
📊 **BSC Snipe Configuration:**
📝 **Contract:** ${contractAddress}
🚀 **Token:** ${tokenInfo?.name || 'Unknown'} (${tokenInfo?.symbol || 'Unknown'})
💵 **Current Price:** $${tokenInfo?.priceUsd || 'Unknown'}
💧 **Liquidity:** $${tokenInfo?.liquidity?.usd || 'Unknown'}

⚙️ **Snipe Settings:**
📈 **Slippage:** ${config.slippage_bsc || '5'}%
💰 **Spend Amount:** ${config.spend_bsc || '0.1'} BSC
⏱️ **Time Delay:** ${config.timeDelay_bsc || '0'} ms
🧱 **Block Delay:** ${config.blockDelay_bsc || '0'} blocks
🔄 **Retries:** ${config.retries_bsc || '3'}
🚀 **Ready to Snipe:** ${snipe.hasLiquidity ? 'No (Liquidity already added)' : 'Yes'}
      `;

      // Edit the message with the review text
      await bot.editMessageText(reviewText, {
        chat_id: chatId,
        message_id: messageId,
        parse_mode: 'Markdown',
        reply_markup: createConfigKeyboard()
      });

      logger.debug('BSC snipe configuration reviewed', {
        chatId,
        contractAddress,
        snipeId: snipe._id
      });
    } else {
      await bot.sendMessage(chatId, '❌ Snipe configuration not found. Please set up a snipe first.');
    }
  } catch (error) {
    logger.error('Error in reviewMessage', {
      chatId,
      contractAddress,
      messageId,
      error: (error as Error).message
    });
    await bot.sendMessage(chatId, `❌ An error occurred: ${(error as Error).message}`);
  }
};
