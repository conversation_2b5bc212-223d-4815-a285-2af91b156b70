import axios from 'axios';

// Function to get token info from Dexscreener
export const getTokenInfoFromDexscreener = async (contractAddress: string) => {
  const url = `https://api.dexscreener.com/latest/dex/tokens/${contractAddress}`;

  try {
    const response = await axios.get(url);
    const latestPair = response.data.pairs[0];

    if (!latestPair) {
      throw new Error('No pair information found.');
    }

    const {
      baseToken,
      priceChange
    } = latestPair;

    const symbol = baseToken?.symbol || 'Unknown';
    const name = baseToken?.name || 'Unknown Token';

    // Extract priceChange for m5
    const priceChangeM5 = priceChange?.m5 ?? '0'; // Default to '0' if not available

    return {
      symbol,
      name,
      priceChange: priceChangeM5, // Return only m5 priceChange as a string
      // Add other fields as needed
    };
  } catch (error) {
    console.error('Error fetching token info from Dexscreener:', error);
    throw new Error('Error fetching token info from Dexscreener. Please try again later.');
  }
};
