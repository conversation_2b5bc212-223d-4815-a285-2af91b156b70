import fs from 'fs';
import path from 'path';
import TelegramBot from 'node-telegram-bot-api';
import { Connection } from '@solana/web3.js';
import User from '../models/user.models';
import TradeModel from '../models/trade.model'; // Adjust the path as needed
import Snipe from '../models/snipe.model';
import { handleBscDashboard } from '../screens/dashboards/bsc.dashboard';
import { handleBSCScanContract, handleBscSellAll, sellBscTokenPercentage } from './bscscan.service';
import {
  getSellOptionsKeyboard,
  handleGenerateNewBscWallet,
  askForContractAddress,
  handleSpendBSC,
  handleChart,
  handleRefresh,
  updateBSCMessageWithTradeOptions,
  updateBSCMessageWithSellOptions,
  mainBscMenu,
  fetchAndDisplayActiveBscTrades,
  displayBscCurrentTrade,
  askForBSCToken
} from '../services/bscscan.service';

import { bscSniperScreen } from '../screens/sniperboards/bscboard';
import { ethSniperScreen } from '../screens/sniperboards/ethboard';
import { solSniperScreen } from '../screens/sniperboards/solboard';
import { baseSniperScreen } from '../screens/sniperboards/baseboard';
import { tradeBscScreen } from '../screens/trade/bsctrade';

import { getBscBalance } from '../screens/dashboards/bsc.dashboard';
import { BlockchainType, TraderFactory, TradeOptions, TradeResult } from './trading/TradingInterface';

// Wrapper function to buy BSC tokens using the trading service
async function buyBscTokens(chatId: number, contractAddress: string): Promise<any> {
  try {
    const user = await User.findOne({ chat_id: chatId });
    if (!user) {
      throw new Error('User not found');
    }

    const trader = TraderFactory.getTrader(BlockchainType.BSC);

    // Get the spend amount from the snipe configuration
    const snipe = await Snipe.findOne({
      chatId: chatId,
      contractAddress: contractAddress,
      blockchain: 'bsc'
    });

    if (!snipe) {
      throw new Error('Snipe configuration not found');
    }

    const spendAmount = snipe.config.spend_bsc?.toString() || '0.1';
    const slippage = snipe.config.slippage_bsc || 5;

    const options: TradeOptions = {
      slippage: slippage,
      gasLimit: 1000000
    };

    const result = await trader.buyTokens(contractAddress, spendAmount, user.bsc_wallet.address, options);

    if (!result.success) {
      throw new Error(result.error || 'Unknown error');
    }

    return {
      transactionHash: result.transactionHash,
      blockNumber: result.blockNumber,
      status: true
    };
  } catch (error) {
    throw error;
  }
}

// Wrapper function to buy BASE tokens using the trading service
async function buyBaseTokens(chatId: number, contractAddress: string): Promise<any> {
  try {
    const user = await User.findOne({ chat_id: chatId });
    if (!user) {
      throw new Error('User not found');
    }

    const trader = TraderFactory.getTrader(BlockchainType.BASE);

    // Get the spend amount from the snipe configuration
    const snipe = await Snipe.findOne({
      chatId: chatId,
      contractAddress: contractAddress,
      blockchain: 'base'
    });

    if (!snipe) {
      throw new Error('Snipe configuration not found');
    }

    const spendAmount = snipe.config.spend_base?.toString() || '0.1';
    const slippage = snipe.config.slippage_base || 5;

    const options: TradeOptions = {
      slippage: slippage,
      gasLimit: 1000000
    };

    const result = await trader.buyTokens(contractAddress, spendAmount, user.base_wallet.address, options);

    if (!result.success) {
      throw new Error(result.error || 'Unknown error');
    }

    return {
      transactionHash: result.transactionHash,
      blockNumber: result.blockNumber,
      status: true
    };
  } catch (error) {
    throw error;
  }
}


import { handleBaseDashboard } from '../screens/dashboards/base.dashboard';

import { handleGenerateNewBaseWallet,
  askForBaseContractAddress, handleBASEScanContract,
  handleBaseRefresh,
  handleBaseChart, handleSpendBase, handleBaseSellAll,
  sellBaseTokenPercentage,
  updatebaseMessageWithSellOptions,
  mainbaseMenu,
  displayCurrentBaseTrade, fetchAndDisplayActiveBaseTrades,
  askForBaseToken

 } from './basescan.service';

 import { updateBaseTradeConfig,
  askForBaseTradeInput, reviewBaseTrade, executeBaseTrade
  } from '../screens/trade/basetrade';

import { tradeBaseScreen } from '../screens/trade/basetrade';

 import { handleGenerateNewSolWallet,
  askForSolContractAddress, handleSOLScanContract,
  handleSolRefresh,
  handleSolChart, handleSpendsol, handleSolSellAll,
  sellSolTokenPercentage,
  updateSolMessageWithSellOptions,
  updateSolMessageWithTradeOptions, mainSolMenu,
  displayCurrentSolTrade, fetchAndDisplayActiveSolTrades,
  askForSolToken

 } from './solscan.service';


 import { handleSolDashboard } from '../screens/dashboards/sol.dashboard';
import  { getSolBalance } from '../services/solscan.service'

// Wrapper function to buy SOL tokens using the trading service
async function buySolToken(chatId: number, contractAddress: string): Promise<string> {
  try {
    const user = await User.findOne({ chat_id: chatId });
    if (!user) {
      throw new Error('User not found');
    }

    const trader = TraderFactory.getTrader(BlockchainType.SOL);

    // Get the spend amount from the snipe configuration
    const snipe = await Snipe.findOne({
      chatId: chatId,
      contractAddress: contractAddress,
      blockchain: 'sol'
    });

    if (!snipe) {
      throw new Error('Snipe configuration not found');
    }

    const spendAmount = snipe.config.spend_sol?.toString() || '0.1';
    const slippage = snipe.config.slippage_sol || 5;

    const options: TradeOptions = {
      slippage: slippage
    };

    const result = await trader.buyTokens(contractAddress, spendAmount, user.sol_wallet.address, options);

    if (!result.success) {
      throw new Error(result.error || 'Unknown error');
    }

    return result.transactionHash || '';
  } catch (error) {
    throw error;
  }
}


 import { handleGenerateNewethWallet,
  askForEthContractAddress, handleETHScanContract,
  handleEthRefresh,
  handleEthChart, handleSpendeth, handleEthSellAll,
  sellethTokenPercentage,
  updateEthMessageWithSellOptions,
  updateEthMessageWithTradeOptions, mainEthMenu,
  displayCurrentEthTrade, fetchAndDisplayActiveEthTrades,
  askForEthToken
 } from './ethscan.service';

 import { handleEthDashboard } from '../screens/dashboards/eth.dashboard';
 import { getEthBalance } from '../screens/dashboards/eth.dashboard';

// Wrapper function to buy ETH tokens using the trading service
async function buyEthTokens(chatId: number, contractAddress: string): Promise<any> {
  try {
    const user = await User.findOne({ chat_id: chatId });
    if (!user) {
      throw new Error('User not found');
    }

    const trader = TraderFactory.getTrader(BlockchainType.ETH);

    // Get the spend amount from the snipe configuration
    const snipe = await Snipe.findOne({
      chatId: chatId,
      contractAddress: contractAddress,
      blockchain: 'eth'
    });

    if (!snipe) {
      throw new Error('Snipe configuration not found');
    }

    const spendAmount = snipe.config.spend_eth?.toString() || '0.1';
    const slippage = snipe.config.slippage_eth || 5;

    const options: TradeOptions = {
      slippage: slippage,
      gasLimit: 1000000
    };

    const result = await trader.buyTokens(contractAddress, spendAmount, user.eth_wallet.address, options);

    if (!result.success) {
      throw new Error(result.error || 'Unknown error');
    }

    return {
      transactionHash: result.transactionHash,
      blockNumber: result.blockNumber,
      status: true
    };
  } catch (error) {
    throw error;
  }
}
import { createSession } from './sessions.service';
import mongoose from 'mongoose';
import { Trade } from '../models/trade.model';


import { getCurrentContractAddress } from '../globalState';
import { tradeEthScreen } from '../screens/trade/ethtrade';
import { tradeSolScreen } from '../screens/trade/soltrade';
import { showConfigKeyboard } from '../screens/config';

const previousValuesPath = path.resolve(__dirname, '../previousValues.json');

 // Helper function to read previous values from JSON
 function getPreviousValue(chatId: number, blockchain: string) {
  const data = JSON.parse(fs.readFileSync(previousValuesPath, 'utf-8'));

  for (const [address, value] of Object.entries(data)) {
      const blockchainData = value as { [key: string]: { chat_id: number, balance: number } };
      if (blockchainData[blockchain]?.chat_id === chatId) {
          return {
              address,
              balance: blockchainData[blockchain].balance,
          };
      }
  }
  return null;
}


const connection: Connection = new Connection(process.env.SOL_PROVIDER_URL!);

interface Session {
  session: mongoose.Types.ObjectId[];
  currentTradeIndex: number;
  telegramId: number;
  userId: any;
  trades: any[];
}

let userSessions: { [chatId: number]: Session } = {};

let tradesState: any[] = [];
let currentIndex: number = 0;


const promptMessageIdStore: { [key: number]: { promptId?: number, replyId?: number, successId?: number } } = {};

export const handleCallbackQuery = async (bot: TelegramBot, query: TelegramBot.CallbackQuery) => {
  const chatId = query.message?.chat.id;
  const messageId = query.message?.message_id;

  if (!chatId || !messageId) return;

  const data = JSON.parse(query.data || '{}');

  switch (data.command) {
    case 'dismiss_message':
      await handleDismissMessage(bot, chatId, messageId);
      break;

      case 'show_config':
        await showConfigKeyboard(bot, chatId);
        break;

    case 'view_bsc':
    case 'view_base':
    case 'view_sol':
    case 'view_eth':
      await handleBlockchainView(bot, chatId, data.command);
      break;


    case 'generate_new_bsc_wallet':
      await handleGenerateNewBscWallet(bot, chatId);
      break;

    case 'scan_contract_bsc':
      const userbscAddress = await askForContractAddress(bot, chatId, messageId);
      if (userbscAddress) {
        await handleBSCScanContract(bot, chatId, userbscAddress);
      } else {
        await bot.sendMessage(chatId, 'Invalid contract address.');
      }
      break;

      case 'scan_snipe_bsc':
        try {
          const bscAddressToSnipe = await askForBSCToken(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'bsc' } });

          if (bscAddressToSnipe) {
            await bscSniperScreen(bot, chatId, bscAddressToSnipe);
          } else {
            await bot.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          console.error('Error during BSC token snipe:', error);
          await bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
        }

      break;


      case 'snipetrade_dashboard_bsc':
        const contractAddress = getCurrentContractAddress(BlockchainType.BSC) || '';
        await tradeBscScreen(bot, chatId, contractAddress);
        break;


    case 'sell_bsc':
      await updateBSCMessageWithSellOptions(bot, chatId, messageId);
      break;

      case 'active_trades_bsc':
        await fetchAndDisplayActiveBscTrades(bot, chatId, 'bsc');
        break;

        case 'previous_bsc':
          if (currentIndex > 0) {
            currentIndex--;
            const tradesState = await TradeModel.find({ isSold: false, blockchain: 'bsc' }).exec();
            await displayBscCurrentTrade(bot, chatId, tradesState, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
          }
          break;

        case 'next_bsc':
          const tradesState = await TradeModel.find({ isSold: false, blockchain: 'bsc' }).exec();
          if (currentIndex < tradesState.length - 1) {
            currentIndex++;
            await displayBscCurrentTrade(bot, chatId, tradesState, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
          }
          break;


    case 'refresh_bsc':
      await handleRefresh(bot, chatId);
      break;

    case 'chart_bsc':
      await handleChart(bot, chatId);
      break;

      case 'spend_bsc':
        try {
            const userBuy = await handleSpendBSC(bot, chatId);
            if (userBuy) {
                const receipt = await buyBscTokens(chatId, userBuy);

                if (receipt && receipt.transactionHash && receipt.blockNumber) {
                    const { transactionHash, blockNumber } = receipt;
                    await bot.sendMessage(chatId, `✅ Tokens successfully purchased:\n\n` +
                        `🔗 Transaction Hash: ${transactionHash}\n` +
                        `🧱 Block Number: ${blockNumber}\n\n` +
                        `👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${transactionHash}\nTo see your transaction`);
                } else {
                    await bot.sendMessage(chatId, `Error: Unable to get transaction details.`);
                }
            } else {
                await bot.sendMessage(chatId, 'Invalid contract address.');
            }
        } catch (error) {
            console.error('Error purchasing tokens:', error);

            if (error instanceof Error) {
                if (error.message.includes('insufficient funds')) {
                    await bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again.`);
                } else if (error.message.includes('SERVER_ERROR') || error.message.includes('ECONNRESET')) {
                    await bot.sendMessage(chatId, `Error purchasing tokens: There was a problem processing your request. Please try again later.`);
                } else if (error.message.includes('body')) {
                    try {
                        const errorBody = JSON.parse(error.message);
                        if (errorBody.error && errorBody.error.message.includes('insufficient funds')) {
                            await bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost. Please fund your wallet and try again.`);
                        } else {
                            await bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.`);
                        }
                    } catch (parseError) {
                        await bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.`);
                    }
                } else {
                    await bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than the required amount and unstable network.`);
                }
            } else {
                await bot.sendMessage(chatId, 'An unknown error occurred while purchasing tokens. Please try again later.');
            }
        }
        break;


        case 'sell_all_bsc':
          await handleBscSellAll(bot, chatId);
          break;

        case 'sell_25_bsc':
            await sellBscTokenPercentage(bot, chatId, 25);
            break;
        case 'sell_50_bsc':
            await sellBscTokenPercentage(bot, chatId, 50);
            break;
        case 'sell_75_bsc':
            await sellBscTokenPercentage(bot, chatId, 75);
            break;


            case 'refresh_wallet_bsc':
              try {
                  const user = await User.findOne({ chat_id: chatId });

                  if (user) {
                      const previousValue = getPreviousValue(chatId, 'bsc');
                      // console.log(previousValue)

                      if (previousValue) {
                          const { address } = previousValue;

                          // Fetch the stored message ID, content, and markup
                          const storedMessageId = user.bsc_dashboard_message_id;
                          const storedContent = user.bsc_dashboard_content;
                          const storedMarkup = user.bsc_dashboard_markup;

                          const balance = Number(previousValue.balance);
const formattedBalance = balance.toFixed(6);

                          // Construct new content and markup
                          const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your BSC Dashboard!
                          \n📍 **Address:** \`${address}\`
                          \n🔗 **Blockchain:** BSC
                          \n💰 **Balance:** \`${formattedBalance} BSC\`
                         `;

                          const newMarkup = {
                              inline_keyboard: [
                                  [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_bsc' }) }],
                                  [
                                      { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_bsc' }) },
                                      { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_bsc' }) }
                                  ],
                                  [
                                      { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_bsc' }) },
                                      { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_bsc_wallet' }) }
                                  ],
                                  [
                                    {
                                      text: "* Dismiss message",
                                      callback_data: JSON.stringify({
                                        command: "dismiss_message",
                                      }),
                                    },
                                  ]
                              ]
                          };

                          // Compare current content and markup with new content and markup
                          if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                              await bot.editMessageText(newContent, {
                                  chat_id: chatId,
                                  message_id: storedMessageId,
                                  parse_mode: 'Markdown',
                                  reply_markup: newMarkup,
                              });

                              // Update stored content and markup
                              await User.updateOne({ chat_id: chatId }, {
                                  $set: {
                                      'base_dashboard_content': newContent,
                                      'base_dashboard_markup': newMarkup
                                  }
                              });
                          }
                      } else {
                          await bot.sendMessage(chatId, 'No wallet address found.');
                      }
                  } else {
                      await bot.sendMessage(chatId, 'User not found.');
                  }
              } catch (error) {
                  console.error('Failed to refresh wallet base:', error);
                  await bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
              }
              break;



        // HANDLE BASE CALLBACKS



    case 'generate_new_base_wallet':
      await handleGenerateNewBaseWallet(bot, chatId);
      break;

      case 'snipetrade_dashboard_base':
        await tradeBaseScreen(bot, chatId,  getCurrentContractAddress(BlockchainType.BASE) || '');
        break;


    case 'scan_contract_base':
      const userAddress = await askForBaseContractAddress(bot, chatId, messageId);
      if (userAddress) {
        await handleBASEScanContract(bot, chatId, userAddress);
      } else {
        await bot.sendMessage(chatId, 'Invalid contract address.');
      }
      break;

      case 'scan_snipe_base':
        try {
          const baseAddressToSnipe = await askForBaseToken(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'base' } });

          if (baseAddressToSnipe) {
            await baseSniperScreen(bot, chatId, baseAddressToSnipe);
          } else {
            await bot.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          console.error('Error during Base token snipe:', error);
          await bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
        }

      break;

    case 'sell_base':
      await updatebaseMessageWithSellOptions(bot, chatId, messageId);
      break;

    // case 'trade_base':
    //   await updateBaseMessageWithTradeOptions(bot, chatId, messageId);
    //   break;

    case 'back_base':
      await mainbaseMenu(bot, chatId, messageId);
      break;

      case 'active_trades_base':
        await fetchAndDisplayActiveBaseTrades(bot, chatId, 'base');
        break;

        case 'previous_base':
          if (currentIndex > 0) {
            currentIndex--;
            const tradesStateBase = await TradeModel.find({ isSold: false, blockchain: 'base' }).exec();
            await displayCurrentBaseTrade(bot, chatId, tradesStateBase, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
          }
          break;

        case 'next_base':
          const tradesStateBase = await TradeModel.find({ isSold: false, blockchain: 'base' }).exec();
          if (currentIndex < tradesStateBase.length - 1) {
            currentIndex++;
            await displayCurrentBaseTrade(bot, chatId, tradesStateBase, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
          }
          break;


    case 'refresh_base':
      await handleBaseRefresh(bot, chatId);
      break;

    case 'chart_base':
      await handleBaseChart(bot, chatId);
      break;

    case 'spend_base':
        try {
            const userBuy = await handleSpendBase(bot, chatId);
            if (userBuy) {
                const receipt = await buyBaseTokens(chatId, userBuy);

                if (receipt && receipt.transactionHash && receipt.blockNumber) {
                    const { transactionHash, blockNumber } = receipt;
                    await bot.sendMessage(chatId, `✅ Tokens successfully purchased:\n\n` +
                        `🔗 Transaction Hash: ${transactionHash}\n` +
                        `🧱 Block Number: ${blockNumber}\n\n` +
                        `👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${transactionHash}\nTo see your transaction`);
                } else {
                    await bot.sendMessage(chatId, `Error: Unable to get transaction details.`);
                }
            } else {
                await bot.sendMessage(chatId, 'Invalid contract address.');
            }
        } catch (error) {
            console.error('Error purchasing tokens:', error);

            if (error instanceof Error) {
                if (error.message.includes('Insufficient BNB balance')) {
                    await bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost.`);
                } else if (error.message.includes('SERVER_ERROR') || error.message.includes('ECONNRESET')) {
                    await bot.sendMessage(chatId, `Error purchasing tokens: There was a problem processing your request. Please try again later.`);
                } else {
                  await bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network.`);
                }
            } else {
                await bot.sendMessage(chatId, 'An unknown error occurred while purchasing tokens. Please try again later.');
            }
        }
        break;

        case 'sell_all_base':
          await handleBaseSellAll(bot, chatId);
          break;

        case 'sell_25_base':
            await sellBaseTokenPercentage(bot, chatId, 25);
            break;
        case 'sell_50_base':
            await sellBaseTokenPercentage(bot, chatId, 50);
            break;
        case 'sell_75_base':
            await sellBaseTokenPercentage(bot, chatId, 75);
            break;






            case 'refresh_wallet_base':
                try {
                    const user = await User.findOne({ chat_id: chatId });

                    if (user) {
                        const previousValue = getPreviousValue(chatId, 'base');
                        // console.log(previousValue)

                        if (previousValue) {
                            const { address } = previousValue;

                            // Fetch the stored message ID, content, and markup
                            const storedMessageId = user.base_dashboard_message_id;
                            const storedContent = user.base_dashboard_content;
                            const storedMarkup = user.base_dashboard_markup;

                            const balance = Number(previousValue.balance);
const formattedBalance = balance.toFixed(6);

                            // Construct new content and markup
                            const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your BASE Dashboard!
                            \n📍 **Address:** \`${address}\`
                            \n🔗 **Blockchain:** BASE
                            \n💰 **Balance:** \`${formattedBalance} Base ETH\`
                           `;

                            const newMarkup = {
                                inline_keyboard: [
                                    [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_base' }) }],
                                    [
                                        { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_base' }) },
                                        { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_base' }) }
                                    ],
                                    [
                                        { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_base' }) },
                                        { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_base_wallet' }) }
                                    ],
                                    [
                                      {
                                        text: "* Dismiss message",
                                        callback_data: JSON.stringify({
                                          command: "dismiss_message",
                                        }),
                                      },
                                    ]
                                ]
                            };

                            // Compare current content and markup with new content and markup
                            if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                                await bot.editMessageText(newContent, {
                                    chat_id: chatId,
                                    message_id: storedMessageId,
                                    parse_mode: 'Markdown',
                                    reply_markup: newMarkup,
                                });

                                // Update stored content and markup
                                await User.updateOne({ chat_id: chatId }, {
                                    $set: {
                                        'base_dashboard_content': newContent,
                                        'base_dashboard_markup': newMarkup
                                    }
                                });
                            }
                        } else {
                            await bot.sendMessage(chatId, 'No wallet address found.');
                        }
                    } else {
                        await bot.sendMessage(chatId, 'User not found.');
                    }
                } catch (error) {
                    console.error('Failed to refresh wallet base:', error);
                    await bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
                }
                break;


        // HANDLE ETHEREUM CALLBACKS



    case 'generate_new_eth_wallet':
      await handleGenerateNewethWallet(bot, chatId);
      break;


      case 'snipetrade_dashboard_eth':
        await tradeEthScreen(bot, chatId,  getCurrentContractAddress(BlockchainType.ETH) || '');
        break;


    case 'scan_contract_eth':
      const userEthAddress = await askForEthContractAddress(bot, chatId, messageId);
      if (userEthAddress) {
        await handleETHScanContract(bot, chatId, userEthAddress);
      } else {
        await bot.sendMessage(chatId, 'Invalid contract address.');
      }
      break;


      case 'scan_snipe_eth':
        try {
          const ethAddressToSnipe = await askForEthToken(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'eth' } });

          if (ethAddressToSnipe) {
            await ethSniperScreen(bot, chatId, ethAddressToSnipe);
          } else {
            await bot.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          console.error('Error during Eth token snipe:', error);
          await bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
        }

      break;

    case 'sell_eth':
      await updateEthMessageWithSellOptions(bot, chatId, messageId);
      break;

    case 'trade_eth':
      await updateEthMessageWithTradeOptions(bot, chatId, messageId);
      break;

    case 'back_eth':
      await mainEthMenu(bot, chatId, messageId);
      break;

      case 'active_trades_eth':
        await fetchAndDisplayActiveEthTrades(bot, chatId, 'eth');
        break;

        case 'previous_eth':
          if (currentIndex > 0) {
            currentIndex--;
            const tradesStateEth = await TradeModel.find({ isSold: false, blockchain: 'eth' }).exec();
            await displayCurrentEthTrade(bot, chatId, tradesStateEth, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
          }
          break;

        case 'next_eth':
          const tradesStateEth = await TradeModel.find({ isSold: false, blockchain: 'eth' }).exec();
          if (currentIndex < tradesStateEth.length - 1) {
            currentIndex++;
            await displayCurrentEthTrade(bot, chatId, tradesStateEth, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
          }
          break;



    case 'refresh_eth':
      await handleEthRefresh(bot, chatId);
      break;

    case 'chart_eth':
      await handleEthChart(bot, chatId);
      break;

    case 'spend_eth':
        try {
            const userBuy = await handleSpendeth(bot, chatId);
            if (userBuy) {
                const receipt = await buyEthTokens(chatId, userBuy);

                if (receipt && receipt.transactionHash && receipt.blockNumber) {
                    const { transactionHash, blockNumber } = receipt;
                    await bot.sendMessage(chatId, `✅ Tokens successfully purchased:\n\n` +
                        `🔗 Transaction Hash: ${transactionHash}\n` +
                        `🧱 Block Number: ${blockNumber}\n\n` +
                        `👁‍🗨 Navigate to:\nhttps://etherscan.com/tx/${transactionHash}\nTo see your transaction`);
                } else {
                    await bot.sendMessage(chatId, `Error: Unable to get transaction details.`);
                }
            } else {
                await bot.sendMessage(chatId, 'Invalid contract address.');
            }
        } catch (error) {
            console.error('Error purchasing tokens:', error);

            if (error instanceof Error) {
                if (error.message.includes('Insufficient Eth balance')) {
                    await bot.sendMessage(chatId, `Error purchasing tokens: Insufficient funds for the intrinsic transaction cost.`);
                } else if (error.message.includes('SERVER_ERROR') || error.message.includes('ECONNRESET')) {
                    await bot.sendMessage(chatId, `Error purchasing tokens: There was a problem processing your request. Please try again later.`);
                } else {
                  await bot.sendMessage(chatId, `An error occurred while purchasing tokens: Possible causes are buying less than required amount and unstable network.`);
                }
            } else {
                await bot.sendMessage(chatId, 'An unknown error occurred while purchasing tokens. Please try again later.');
            }
        }
        break;

        case 'sell_all_eth':
          await handleEthSellAll(bot, chatId);
          break;

        case 'sell_25_eth':
            await sellethTokenPercentage(bot, chatId, 25);
            break;
        case 'sell_50_eth':
            await sellethTokenPercentage(bot, chatId, 50);
            break;
        case 'sell_75_eth':
            await sellethTokenPercentage(bot, chatId, 75);
            break;


            case 'refresh_wallet_eth':
              try {
                  const user = await User.findOne({ chat_id: chatId });

                  if (user) {
                      const previousValue = getPreviousValue(chatId, 'eth');
                      // console.log(previousValue)

                      if (previousValue) {
                          const { address } = previousValue;

                          // Fetch the stored message ID, content, and markup
                          const storedMessageId = user.eth_dashboard_message_id;
                          const storedContent = user.eth_dashboard_content;
                          const storedMarkup = user.eth_dashboard_markup;

                          const balance = Number(previousValue.balance);
const formattedBalance = balance.toFixed(6);

                          // Construct new content and markup
                          const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your ETH Dashboard!
                          \n📍 **Address:** \`${address}\`
                          \n🔗 **Blockchain:** ETH
                          \n💰 **Balance:** \`${formattedBalance}  ETH\`
                         `;

                          const newMarkup = {
                              inline_keyboard: [
                                  [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_eth' }) }],
                                  [
                                      { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_eth' }) },
                                      { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_eth' }) }
                                  ],
                                  [
                                      { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_eth' }) },
                                      { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_eth_wallet' }) }
                                  ],
                                  [
                                    {
                                      text: "* Dismiss message",
                                      callback_data: JSON.stringify({
                                        command: "dismiss_message",
                                      }),
                                    },
                                  ]
                              ]
                          };

                          // Compare current content and markup with new content and markup
                          if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                              await bot.editMessageText(newContent, {
                                  chat_id: chatId,
                                  message_id: storedMessageId,
                                  parse_mode: 'Markdown',
                                  reply_markup: newMarkup,
                              });

                              // Update stored content and markup
                              await User.updateOne({ chat_id: chatId }, {
                                  $set: {
                                      'eth_dashboard_content': newContent,
                                      'eth_dashboard_markup': newMarkup
                                  }
                              });
                          }
                      } else {
                          await bot.sendMessage(chatId, 'No wallet address found.');
                      }
                  } else {
                      await bot.sendMessage(chatId, 'User not found.');
                  }
              } catch (error) {
                  console.error('Failed to refresh wallet et:', error);
                  await bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
              }
              break;



        // HANDLE SOL CALLBACKS


        case 'generate_new_sol_wallet':
          await handleGenerateNewSolWallet(bot, chatId);
          break;

          case 'snipetrade_dashboard_sol':
            await tradeSolScreen(bot, chatId,  getCurrentContractAddress(BlockchainType.SOL) || '');
            break;

        case 'scan_contract_sol':
          const userSolAddress = await askForSolContractAddress(bot, chatId, messageId);
          if (userSolAddress) {
            await handleSOLScanContract(bot, chatId, userSolAddress);
          } else {
            await bot.sendMessage(chatId, 'Invalid contract address.');
          }
          break;



      case 'scan_snipe_sol':
        try {
          const solAddressToSnipe = await askForSolToken(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'sol' } });

          if (solAddressToSnipe) {
            await solSniperScreen(bot, chatId, solAddressToSnipe);
          } else {
            await bot.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          console.error('Error during Sol token snipe:', error);
          await bot.sendMessage(chatId, `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`);
        }

      break;

        case 'sell_sol':
          await updateSolMessageWithSellOptions(bot, chatId, messageId);
          break;

        case 'trade_sol':
          await updateSolMessageWithTradeOptions(bot, chatId, messageId);
          break;

        case 'back_sol':
          await mainSolMenu(bot, chatId, messageId);
          break;

          case 'active_trades_sol':
        await fetchAndDisplayActiveSolTrades(bot, chatId, 'sol');
        break;

        case 'previous_sol':
          if (currentIndex > 0) {
            currentIndex--;
            const tradesStateSol = await TradeModel.find({ isSold: false, blockchain: 'sol' }).exec();
            await displayCurrentSolTrade(bot, chatId, tradesStateSol, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the first trade.' });
          }
          break;

        case 'next_sol':
          const tradesStateSol = await TradeModel.find({ isSold: false, blockchain: 'sol' }).exec();
          if (currentIndex < tradesStateSol.length - 1) {
            currentIndex++;
            await displayCurrentSolTrade(bot, chatId, tradesStateSol, currentIndex, messageId);
          } else {
            await bot.answerCallbackQuery(query.id, { text: 'This is the last trade.' });
          }
          break;



        case 'refresh_sol':
          await handleSolRefresh(bot, chatId);
          break;

        case 'chart_sol':
          await handleSolChart(bot, chatId);
          break;

          case 'spend_sol':
            try {
              const userBuy = await handleSpendsol(bot, chatId);
              if (!userBuy || isNaN(Number(userBuy))) {
                throw new Error('Invalid amount.');
              }

              const amount = userBuy;
              const receipt = await buySolToken(chatId, amount);

              if (receipt) {
                await bot.sendMessage(chatId, `✅ Successfully purchased tokens:\n\n` +
                  `🔗 Transaction Hash: ${receipt}\n\n` +
                  `👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${receipt}\nTo see your transaction`);
              } else {
                await bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
              }
            } catch (error) {
              console.error('Error purchasing tokens:', error);
              const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
              await bot.sendMessage(chatId, `An error occurred while purchasing tokens}`);
            }
            break;



            case 'sell_all_sol':
              await handleSolSellAll(bot, chatId);
              break;

            case 'sell_25_sol':
                await sellSolTokenPercentage(bot, chatId, 25);
                break;
            case 'sell_50_sol':
                await sellSolTokenPercentage(bot, chatId, 50);
                break;
            case 'sell_75_sol':
                await sellSolTokenPercentage(bot, chatId, 75);
                break;



            case 'refresh_wallet_sol':
              try {
                  const user = await User.findOne({ chat_id: chatId });

                  if (user) {
                      const previousValue = getPreviousValue(chatId, 'sol');
                      // console.log(previousValue)

                      if (previousValue) {
                          const { address } = previousValue;

                          // Fetch the stored message ID, content, and markup
                          const storedMessageId = user.sol_dashboard_message_id;
                          const storedContent = user.sol_dashboard_content;
                          const storedMarkup = user.sol_dashboard_markup;

                          const balance = Number(previousValue.balance);
const formattedBalance = balance.toFixed(6);

                          // Construct new content and markup
                          const newContent = `👋 Welcome, ${user.first_name || 'User'}, to your SOL Dashboard!
                          \n📍 **Address:** \`${address}\`
                          \n🔗 **Blockchain:** SOL
                          \n💰 **Balance:** \`${formattedBalance}  SOL\`
                         `;

                          const newMarkup = {
                              inline_keyboard: [
                                  [{ text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_sol' }) }],
                                  [
                                      { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'config_sol' }) },
                                      { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_sol' }) }
                                  ],
                                  [
                                      { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_sol' }) },
                                      { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_sol_wallet' }) }
                                  ],
                                  [
                                    {
                                      text: "* Dismiss message",
                                      callback_data: JSON.stringify({
                                        command: "dismiss_message",
                                      }),
                                    },
                                  ]
                              ]
                          };

                          // Compare current content and markup with new content and markup
                          if (storedContent !== newContent || JSON.stringify(storedMarkup) !== JSON.stringify(newMarkup)) {
                              await bot.editMessageText(newContent, {
                                  chat_id: chatId,
                                  message_id: storedMessageId,
                                  parse_mode: 'Markdown',
                                  reply_markup: newMarkup,
                              });

                              // Update stored content and markup
                              await User.updateOne({ chat_id: chatId }, {
                                  $set: {
                                      'sol_dashboard_content': newContent,
                                      'sol_dashboard_markup': newMarkup
                                  }
                              });
                          }
                      } else {
                          await bot.sendMessage(chatId, 'No wallet address found.');
                      }
                  } else {
                      await bot.sendMessage(chatId, 'User not found.');
                  }
              } catch (error) {
                  console.error('Failed to refresh wallet et:', error);
                  await bot.sendMessage(chatId, 'An error occurred while refreshing your wallet.');
              }
              break;





    default:
      // console.warn('Unknown command:', data.command);
  }
};



// Handle dismissing messages
const handleDismissMessage = async (bot: TelegramBot, chatId: number, messageId: number) => {
  try {
    await bot.deleteMessage(chatId, messageId);
  } catch (error) {
    console.error('Failed to delete message:', error);
  }
};

// Handle blockchain view
const handleBlockchainView = async (bot: TelegramBot, chatId: number, command: string) => {
  const selectedBlockchain = command.split('_')[1];

  await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: selectedBlockchain } });

  switch (selectedBlockchain) {
    case 'bsc':
      await handleBscDashboard(bot, chatId);
      break;
    case 'base':
        await handleBaseDashboard(bot, chatId);
        break;
     case 'eth':
    await handleEthDashboard(bot, chatId);
        break;
    case 'sol':
      await handleSolDashboard(bot, chatId);
       break;

    // Add cases for other blockchains as needed
  }
};



function handleActiveTradesBsc(bot: TelegramBot, chatId: number) {
  throw new Error('Function not implemented.');
}

