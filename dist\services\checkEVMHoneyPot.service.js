"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkIfHoneypot = void 0;
const axios_1 = __importDefault(require("axios"));
// Function to check if a contract is a honeypot
const checkIfHoneypot = (address) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b, _c;
    const url = 'https://api.honeypot.is/v2/IsHoneypot';
    try {
        const response = yield axios_1.default.get(url, {
            params: { address }
        });
        const { summary, simulationResult } = response.data;
        let flagDescriptions = summary.flags.map(flag => flag.description);
        if (summary.risk === 'honeypot') {
            flagDescriptions.unshift('RUN THE FUCK AWAY (HONEYPOT DETECTED)');
        }
        else if (summary.risk === 'unknown') {
            flagDescriptions.unshift('Could not determine if this is a HoneyPot or can Ruggpull. Strongly advised to DYOR and tread with caution');
        }
        let closedSourceWarning = '';
        if (flagDescriptions.includes('The source code is not available, allowing for hidden functionality.')) {
            closedSourceWarning = "Contract's dependencies are closed source, allowing for hidden functionalities. Proceed with caution.";
            flagDescriptions = flagDescriptions.filter(flag => flag !== 'The source code is not available, allowing for hidden functionality.');
        }
        flagDescriptions = flagDescriptions.filter(flag => flag !== 'All snipers are marked as honeypots (blacklisted). Sniper detection still needs some improvements.');
        const flagDescriptionsText = (closedSourceWarning ? closedSourceWarning + '\n' : '') + (flagDescriptions.length > 0 ? flagDescriptions.join('\n') : '');
        const buyTax = (_a = simulationResult === null || simulationResult === void 0 ? void 0 : simulationResult.buyTax) !== null && _a !== void 0 ? _a : '0';
        const sellTax = (_b = simulationResult === null || simulationResult === void 0 ? void 0 : simulationResult.sellTax) !== null && _b !== void 0 ? _b : '0';
        const transferTax = (_c = simulationResult === null || simulationResult === void 0 ? void 0 : simulationResult.transferTax) !== null && _c !== void 0 ? _c : '0';
        return {
            risk: summary.risk,
            buyTax,
            sellTax,
            transferTax,
            flagDescriptions: flagDescriptionsText || 'Low risk of HoneyPot/Rugpull. This can always change, do your due diligence',
        };
    }
    catch (error) {
        console.error('Error fetching honeypot data:', error);
        throw new Error('Error fetching honeypot data. Please try again later.');
    }
});
exports.checkIfHoneypot = checkIfHoneypot;
