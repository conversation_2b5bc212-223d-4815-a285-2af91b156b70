import { UserSchema } from "../models/index";

export const UserService = {
  createUser: async (props: any) => {
    try {
      return await UserSchema.create(props);
    } catch (err: any) {
      console.log(err);
      throw new Error(err.message);
    }
  },

  findById: async (id: string) => {
    try {
      return await UserSchema.findById(id);
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  findOne: async (filter: any) => {
    try {
      return await UserSchema.findOne({ ...filter, retired: false });
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  findLastOne: async (filter: any) => {
    try {
      return await UserSchema.findOne(filter).sort({ updatedAt: -1 });
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  find: async (filter: any) => {
    try {
      return await UserSchema.find(filter);
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  findAndSort: async (filter: any) => {
    try {
      return await UserSchema.find(filter).sort({ retired: 1, nonce: 1 }).exec();
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  updateOne: async (id: string, updateData: any) => {
    try {
      return await UserSchema.findByIdAndUpdate(id, updateData, { new: true });
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  findAndUpdateOne: async (filter: any, updateData: any) => {
    try {
      return await UserSchema.findOneAndUpdate(filter, updateData, { new: true });
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  updateMany: async (filter: any, updateData: any) => {
    try {
      return await UserSchema.updateMany(filter, { $set: updateData });
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  deleteOne: async (filter: any) => {
    try {
      return await UserSchema.findOneAndDelete(filter);
    } catch (err: any) {
      throw new Error(err.message);
    }
  },

  extractUniqueCode: (text: string): string | null => {
    const words = text.split(' ');
    return words.length > 1 ? words[1] : null;
  },

  extractPNLdata: (text: string): string | null => {
    const words = text.split(' ');
    if (words.length > 1 && words[1].endsWith('png')) {
      return words[1].replace('png', '.png');
    }
    return null;
  },
};
