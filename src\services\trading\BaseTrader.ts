/**
 * Base Network Trader Implementation
 *
 * This module implements the BlockchainTrader interface for the Base network.
 */

import TelegramBot from 'node-telegram-bot-api';
import { ethers } from 'ethers';
import {
  BaseTrader as BaseTraderInterface,
  BlockchainType,
  TokenInfo,
  TradeOptions,
  TradeResult,
  WalletInfo
} from './TradingInterface';
import { logger } from '../logger/LoggerService';
import { generateBASEWallet } from '../wallet.service';
import { checkEVMHoneypot } from '../checkEVMHoneyPot.service';
import { getTokenInfoFromDexscreener } from '../dexscreener.service';
import tokenAbi from '../../abis/ERC20ABI.json';
import routerAbi from '../../abis/BaseswapRouterABI.json';
import axios from 'axios';

/**
 * Base-specific configuration
 */
export const BASE_CONFIG = {
  rpcUrl: process.env.BASE_PROVIDER_URL || 'https://mainnet.base.org',
  routerAddress: process.env.BASE_ROUTER_ADDRESS || '******************************************', // Baseswap Router
  wethAddress: process.env.BASE_WETH_ADDRESS || '******************************************', // Wrapped ETH on Base
  explorerUrl: 'https://basescan.org',
  gasLimit: 300000,
  defaultSlippage: 10, // 10%
  apiUrl: process.env.BASENODE_API_URL || 'https://base.api.0x.org/swap/v1/quote',
};

/**
 * Base Network Trader implementation
 */
export class BaseNetworkTrader extends BaseTraderInterface {
  private provider: ethers.providers.JsonRpcProvider;
  private apiKey: string;

  constructor(bot: TelegramBot) {
    super(bot);
    this.provider = new ethers.providers.JsonRpcProvider(BASE_CONFIG.rpcUrl);
    this.apiKey = process.env.MASTER_KEY || '';
    logger.info('Base Network Trader initialized', { rpcUrl: BASE_CONFIG.rpcUrl });
  }

  /**
   * Get the blockchain type
   */
  getBlockchainType(): BlockchainType {
    return BlockchainType.BASE;
  }

  /**
   * Buy tokens on Base
   * @param tokenAddress The address of the token to buy
   * @param amount The amount of ETH to spend
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async buyTokens(
    tokenAddress: string,
    amount: string,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Buying tokens on Base', {
      tokenAddress,
      amount,
      walletAddress,
      options
    });

    try {
      // Get the wallet's private key from the database
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a wallet instance
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // Calculate the amount in wei
      const amountInWei = ethers.utils.parseEther(amount);

      // Set up the 0x API parameters
      const sellToken = '******************************************'; // ETH
      const buyToken = tokenAddress;
      const slippagePercentage = (options?.slippage || BASE_CONFIG.defaultSlippage) / 100;
      const feeRecipient = process.env.EVM_ADMIN_ADDRESS || '';
      const buyTokenPercentageFee = 0.08; // 0.8% fee

      // Build the query parameters
      const queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${amountInWei.toString()}&slippagePercentage=${slippagePercentage}&takerAddress=${walletAddress}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;

      // Get the swap quote from 0x API
      const url = `${BASE_CONFIG.apiUrl}?${queryParams}`;
      const response = await axios.get(url, {
        headers: { '0x-api-key': this.apiKey }
      });

      const quote = response.data;

      // Build the transaction
      const buyTx = {
        to: quote.to,
        data: quote.data,
        value: ethers.BigNumber.from(quote.value),
        gasLimit: options?.gasLimit ? ethers.BigNumber.from(options.gasLimit) : ethers.BigNumber.from(BASE_CONFIG.gasLimit),
        gasPrice: options?.gasPrice
          ? ethers.utils.parseUnits(options.gasPrice, 'gwei')
          : ethers.utils.parseUnits('0.05', 'gwei'), // Base has very low gas prices
      };

      // Execute the transaction
      const txResponse = await wallet.sendTransaction(buyTx);

      // Wait for the transaction to be mined
      const receipt = await txResponse.wait();

      logger.info('Base buy transaction successful', {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        tokenAddress
      });

      // Log performance
      logger.logPerformance('Base buyTokens', startTime, {
        tokenAddress,
        transactionHash: receipt.transactionHash
      });

      return {
        success: true,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toNumber(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        amountIn: amount,
        amountOut: 'Unknown', // 0x API doesn't provide this directly
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('Base buy transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Sell tokens on Base
   * @param tokenAddress The address of the token to sell
   * @param percentage The percentage of tokens to sell (0-100)
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async sellTokens(
    tokenAddress: string,
    percentage: number,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Selling tokens on Base', {
      tokenAddress,
      percentage,
      walletAddress,
      options
    });

    try {
      // Validate percentage
      if (percentage <= 0 || percentage > 100) {
        throw new Error('Percentage must be between 1 and 100');
      }

      // Get the wallet's private key
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a wallet instance
      const wallet = new ethers.Wallet(privateKey, this.provider);

      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        wallet
      );

      // Get token balance and decimals
      const balance = await tokenContract.balanceOf(walletAddress);
      const decimals = await tokenContract.decimals();

      // Calculate amount to sell based on percentage
      const amountToSell = balance.mul(percentage).div(100);

      if (amountToSell.isZero()) {
        throw new Error('No tokens to sell');
      }

      // Check if we need to approve the 0x Exchange Proxy
      const spenderAddress = process.env.EVM_SPENDER_ADDRESS || '';
      const allowance = await tokenContract.allowance(walletAddress, spenderAddress);

      if (allowance.lt(amountToSell)) {
        // Approve the spender to spend tokens
        const approveTx = await tokenContract.approve(
          spenderAddress,
          ethers.constants.MaxUint256
        );
        await approveTx.wait();
        logger.info('Approved spender to spend tokens', {
          tokenAddress,
          walletAddress,
          transactionHash: approveTx.hash
        });
      }

      // Set up the 0x API parameters
      const sellToken = tokenAddress;
      const buyToken = '******************************************'; // ETH
      const slippagePercentage = (options?.slippage || BASE_CONFIG.defaultSlippage) / 100;
      const feeRecipient = process.env.EVM_ADMIN_ADDRESS || '';
      const buyTokenPercentageFee = 0.08; // 0.8% fee

      // Build the query parameters
      const queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${amountToSell.toString()}&slippagePercentage=${slippagePercentage}&takerAddress=${walletAddress}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;

      // Get the swap quote from 0x API
      const url = `${BASE_CONFIG.apiUrl}?${queryParams}`;
      const response = await axios.get(url, {
        headers: { '0x-api-key': this.apiKey }
      });

      const quote = response.data;

      // Build the transaction
      const sellTx = {
        to: quote.to,
        data: quote.data,
        value: ethers.BigNumber.from(quote.value || 0),
        gasLimit: options?.gasLimit ? ethers.BigNumber.from(options.gasLimit) : ethers.BigNumber.from(BASE_CONFIG.gasLimit),
        gasPrice: options?.gasPrice
          ? ethers.utils.parseUnits(options.gasPrice, 'gwei')
          : ethers.utils.parseUnits('0.05', 'gwei'), // Base has very low gas prices
      };

      // Execute the transaction
      const txResponse = await wallet.sendTransaction(sellTx);

      // Wait for the transaction to be mined
      const receipt = await txResponse.wait();

      logger.info('Base sell transaction successful', {
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toString(),
        tokenAddress,
        percentage
      });

      // Log performance
      logger.logPerformance('Base sellTokens', startTime, {
        tokenAddress,
        transactionHash: receipt.transactionHash,
        percentage
      });

      return {
        success: true,
        transactionHash: receipt.transactionHash,
        blockNumber: receipt.blockNumber,
        gasUsed: receipt.gasUsed.toNumber(),
        effectiveGasPrice: receipt.effectiveGasPrice.toString(),
        amountIn: ethers.utils.formatUnits(amountToSell, decimals),
        amountOut: 'Unknown', // 0x API doesn't provide this directly
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('Base sell transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress,
        percentage
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get token information
   * @param tokenAddress The address of the token
   */
  async getTokenInfo(tokenAddress: string): Promise<TokenInfo> {
    try {
      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        this.provider
      );

      // Get basic token information
      const [name, symbol, decimals, totalSupply] = await Promise.all([
        tokenContract.name(),
        tokenContract.symbol(),
        tokenContract.decimals(),
        tokenContract.totalSupply()
      ]);

      // Get additional information from Dexscreener
      const dexInfo = await getTokenInfoFromDexscreener(tokenAddress, BlockchainType.BASE);

      // Check if token is a honeypot
      const honeypotInfo = await this.checkHoneypot(tokenAddress);

      return {
        name,
        symbol,
        decimals,
        totalSupply: ethers.utils.formatUnits(totalSupply, decimals),
        address: tokenAddress,
        blockchain: BlockchainType.BASE,
        price: typeof dexInfo?.priceUsd === 'string' ? parseFloat(dexInfo.priceUsd) : (dexInfo?.priceUsd || 0),
        marketCap: typeof dexInfo?.marketCap === 'string' ? parseFloat(dexInfo.marketCap) : (dexInfo?.marketCap || 0),
        isHoneypot: honeypotInfo?.isHoneypot || false,
        honeypotInfo
      };
    } catch (error: any) {
      logger.error('Failed to get Base token info', {
        error: error.message,
        tokenAddress
      });

      // Return minimal information
      return {
        name: 'Unknown',
        symbol: 'UNKNOWN',
        decimals: 18,
        address: tokenAddress,
        blockchain: BlockchainType.BASE
      };
    }
  }

  /**
   * Get wallet balance
   * @param walletAddress The wallet address
   */
  async getWalletBalance(walletAddress: string): Promise<string> {
    try {
      const balance = await this.provider.getBalance(walletAddress);
      return ethers.utils.formatEther(balance);
    } catch (error: any) {
      logger.error('Failed to get Base wallet balance', {
        error: error.message,
        walletAddress
      });
      return '0';
    }
  }

  /**
   * Get token balance for a wallet
   * @param tokenAddress The address of the token
   * @param walletAddress The wallet address
   */
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string> {
    try {
      // Create token contract instance
      const tokenContract = new ethers.Contract(
        tokenAddress,
        tokenAbi,
        this.provider
      );

      // Get token balance and decimals
      const balance = await tokenContract.balanceOf(walletAddress);
      const decimals = await tokenContract.decimals();

      return ethers.utils.formatUnits(balance, decimals);
    } catch (error: any) {
      logger.error('Failed to get Base token balance', {
        error: error.message,
        tokenAddress,
        walletAddress
      });
      return '0';
    }
  }

  /**
   * Check if a token is a honeypot
   * @param tokenAddress The address of the token
   */
  async checkHoneypot(tokenAddress: string): Promise<any> {
    try {
      return await checkEVMHoneypot(tokenAddress, BlockchainType.BASE);
    } catch (error: any) {
      logger.error('Failed to check if Base token is honeypot', {
        error: error.message,
        tokenAddress
      });
      return { isHoneypot: false, message: 'Failed to check' };
    }
  }

  /**
   * Generate a new Base wallet
   */
  async generateWallet(): Promise<WalletInfo> {
    try {
      const wallet = generateBASEWallet();
      const balance = await this.getWalletBalance(wallet.address);

      return {
        ...wallet,
        balance,
        blockchain: BlockchainType.BASE
      };
    } catch (error: any) {
      logger.error('Failed to generate Base wallet', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Send a message to a Telegram chat
   * @param chatId The chat ID
   * @param message The message to send
   */
  protected async sendMessage(chatId: number, message: string): Promise<void> {
    try {
      await this.bot.sendMessage(chatId, message, { parse_mode: 'Markdown' });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }

  /**
   * Get a wallet's private key (this would be implemented securely in production)
   * @param walletAddress The wallet address
   */
  private async getPrivateKey(walletAddress: string): Promise<string | null> {
    // In a real implementation, you would retrieve this securely from a database
    // For now, we'll return null and assume it's handled elsewhere
    return null;
  }
}
