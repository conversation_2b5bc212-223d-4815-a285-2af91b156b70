import TelegramBot, { SendMessageOptions, InlineKeyboardButton } from 'node-telegram-bot-api';
import User from '../models/user.models';
import TradeModel  from '../models/trade.model';
import { checkIfHoneypot } from '../services/honeypot/UnifiedHoneypotChecker';
import { getTokenInfoFromDexscreener } from '../services/dexscreener.service';
import tokenAbi from '../abis/ERC20ABI.json';
import { handleSolDashboard } from '../screens/dashboards/sol.dashboard';
import { generateSOLWallet } from './wallet.service';
import { INPUT_SOL_CONTRACT_ADDRESS, INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE } from '../botConvo';
import { SET_SOL_SPEND } from '../botConvo';
import { setCurrentContractAddress, getCurrentContractAddress } from '../globalState';
import { BlockchainType, TraderFactory } from './trading/TradingInterface';
import { Connection, PublicKey, LAMPORTS_PER_SOL, clusterApiUrl } from '@solana/web3.js';

interface TokenDetails {
  totalSupply?: string | null;
  marketCap?: number;
  checkHoneypot?: any;
  tokenInfo?: any;
  resultMessage?: string;
  tokenAddress?: string;
}

let localState: Record<string, TokenDetails> = {};
let messageIdStore: Record<number, number> = {};

const providerUrl = process.env.SOL_PROVIDER_URL;
if (!providerUrl) {
    throw new Error('PROVIDER_URL is not defined in the environment variables.');
}

const connection = new Connection(providerUrl);

interface MintInfo {
    supply: number;
    decimals: number;
}

const getTokenTotalSupply = async (connection: Connection, tokenMintAddress: string): Promise<number> => {
    try {
        const mintPublicKey = new PublicKey(tokenMintAddress);
        const mintAccountInfo = await connection.getParsedAccountInfo(mintPublicKey);

        if (!mintAccountInfo || !mintAccountInfo.value || mintAccountInfo.value.data instanceof Buffer) {
            throw new Error("Could not find mint account");
        }

        const mintInfo: MintInfo = mintAccountInfo.value.data.parsed.info;
        const totalSupply = mintInfo.supply;
        const decimals = mintInfo.decimals;
        const adjustedTotalSupply = totalSupply / Math.pow(10, decimals);

        return adjustedTotalSupply;

    } catch (error) {
        console.error(`Error fetching token total supply: ${error}`);
        throw error;
    }
};

export const handleSOLScanContract = async (bot: TelegramBot, chat_id: number, userAddress: string) => {
  try {
      if (!userAddress) {
          throw new Error('Invalid contract address.');
      }

      const previousContractAddress = getCurrentContractAddress(BlockchainType.SOL);
      if (previousContractAddress && localState[previousContractAddress]) {
          delete localState[previousContractAddress];
      }

      localState[userAddress] = localState[userAddress] || {};
      setCurrentContractAddress(BlockchainType.SOL, userAddress);

      // Use the trader's honeypot checker
      const trader = TraderFactory.getTrader(BlockchainType.SOL);
      const honeypotResult = await trader.checkHoneypot(userAddress);
      const tokenInfo = await getTokenInfoFromDexscreener(userAddress, BlockchainType.SOL);

      const connection = new Connection(process.env.SOL_PROVIDER_URL!);
      const totalSupply = await getTokenTotalSupply(connection, userAddress);
      if (totalSupply === null) {
          throw new Error('Failed to fetch total supply.');
      }

      const totalSupplyNumber = totalSupply;
      const marketCap = tokenInfo.priceUsd * totalSupplyNumber;

      // Utility function to format numbers for display
      const formatNumber = (num: number): string => {
          if (num >= 1_000_000_000_000) {
              return (num / 1_000_000_000_000).toFixed(2) + 'T'; // Trillions
          } else if (num >= 1_000_000_000) {
              return (num / 1_000_000_000).toFixed(2) + 'B'; // Billions
          } else if (num >= 1_000_000) {
              return (num / 1_000_000).toFixed(2) + 'M'; // Millions
          } else if (num >= 1_000) {
              return (num / 1_000).toFixed(2) + 'K'; // Thousands
          } else {
              return num.toFixed(2); // Less than a thousand
          }
      };

      const formattedTotalSupply = formatNumber(totalSupplyNumber);
      const formattedMarketCap = formatNumber(marketCap);
      const formattedLiquidity = formatNumber(tokenInfo.liquidity.usd);

      // Use h1 (1 hour) price change instead of m5
      const priceChange = tokenInfo.priceChange.h1 || 0;
      const priceChangeEmoji = priceChange > 0 ? '🟢🟢🟢🟢🟢🟢' : '🔴🔴🔴🔴🔴🔴';

      const roundedBuyTax = Math.ceil(parseFloat(honeypotResult.buyTax));
      const roundedSellTax = Math.ceil(parseFloat(honeypotResult.sellTax));

      const resultMessage = `
🪙 ${tokenInfo.symbol} (${tokenInfo.name}) || 📈 ${priceChange > 0 ? '+' : ''}${priceChange}%  || 🏦 ${tokenInfo.dexId} || ${tokenInfo.chainId}

${priceChangeEmoji}  Price Change: ${priceChange > 0 ? '+' : ''}${priceChange}%

CA: ${tokenInfo.address}
LP: ${tokenInfo.pairAddress}

💼 TOTAL SUPPLY: ${formattedTotalSupply} ${tokenInfo.symbol}
🏷️ MC: $${formattedMarketCap}
💧 LIQUIDITY: $${formattedLiquidity}
💵 PRICE: $${tokenInfo.priceUsd}

🔍 Honeypot/Rugpull Risk: ${honeypotResult.risk}
📉 Buy Tax: ${roundedBuyTax}%
📈 Sell Tax: ${roundedSellTax}%

⚠️⚠️⚠️⚠️ ${honeypotResult.riskDescriptions}
      `;

      const user = await User.findOne({ chat_id }).exec();
      if (user) {
          user.solCurrentTokenName = tokenInfo.name;
          user.solCurrentContractAddress = userAddress;
          await user.save();
      }

      localState[userAddress] = {
          totalSupply: formattedTotalSupply, // Keep as number
          marketCap: marketCap,          // Keep as number
          checkHoneypot: honeypotResult,
          tokenInfo,
          resultMessage
      };

      const previousMessageId = messageIdStore[chat_id];
      if (previousMessageId) {
          await bot.deleteMessage(chat_id, previousMessageId);
      }

      const sentMessage = await bot.sendMessage(chat_id, resultMessage, {
          parse_mode: 'Markdown',
          reply_markup: createSolKeyboard()
      });

      messageIdStore[chat_id] = sentMessage.message_id;
      return sentMessage.message_id;

  } catch (error) {
      console.error('Error scanning contract:', error);
      await bot.sendMessage(chat_id, 'There was an error scanning the contract. Please try again later.');
  }
};


export async function getSolTokenBalance(connection: Connection, walletAddress: string, tokenMintAddress: string): Promise<number> {
    try {
        const accounts = await connection.getParsedTokenAccountsByOwner(
            new PublicKey(walletAddress),
            { mint: new PublicKey(tokenMintAddress) }
        );

        if (accounts.value.length === 0) {
            throw new Error('Token account not found');
        }

        const tokenAccount = accounts.value[0];
        const tokenAmount = tokenAccount.account.data.parsed.info.tokenAmount.uiAmount;
        console.log('Balance (using Solana-Web3.js): ', tokenAmount);
        return tokenAmount;
    } catch (error) {
        console.error('Error fetching token balance:', error);
        throw error;
    }
}


// Function to get the sol balance
export const getSolBalance = async (address: string): Promise<string> => {
  const providerUrl = process.env.SOL_PROVIDER_URL;

  if (!providerUrl) {
    throw new Error('Provider is not defined in the .env file');
  }

  const connection = new Connection(providerUrl); // Use the providerUrl from the environment variable

  try {
    // Fetch the balance
    const publicKey = new PublicKey(address);
    const balance = await connection.getBalance(publicKey); // Directly get balance in lamports

    // Convert lamports to SOL
    const balanceSOL = balance / LAMPORTS_PER_SOL;
    return balanceSOL.toFixed(6);
  } catch (error) {
    console.error('Error fetching balance:', error);
    throw new Error('Unable to fetch balance for Solana wallet.');
  }
};


// Create sell options keyboard
export const getSolSellOptionsKeyboard = async (): Promise<InlineKeyboardButton[][]> => {
  return [
    [
      { text: 'Sell All', callback_data: JSON.stringify({ command: 'sell_all_sol' }) },
      { text: 'Sell 25%', callback_data: JSON.stringify({ command: 'sell_25_sol' }) }
    ],
    [
      { text: 'Sell 50%', callback_data: JSON.stringify({ command: 'sell_50_sol' }) },
      { text: 'Sell 75%', callback_data: JSON.stringify({ command: 'sell_75_sol' }) }
    ],
    [
      { text: 'Back', callback_data: JSON.stringify({ command: 'back_sol' }) }
    ]
  ];
};


// Create trade options keyboard
export const getsolTradeOptionsKeyboard = async (): Promise<InlineKeyboardButton[][]> => {
  return [
    [
      { text: 'Trade Option 1', callback_data: JSON.stringify({ command: 'trade_option_1' }) },
      { text: 'Trade Option 2', callback_data: JSON.stringify({ command: 'trade_option_2' }) }
    ]
  ];
};


export const createSolKeyboard = (): { inline_keyboard: InlineKeyboardButton[][] } => {
  return {
    inline_keyboard: [
      [
        { text: '◀️ Previous', callback_data: JSON.stringify({ command: 'previous_sol' }) },
        { text: '▶️ Next', callback_data: JSON.stringify({ command: 'next_sol' }) }
      ],
      [
        { text: '🔄 Refresh', callback_data: JSON.stringify({ command: 'refresh_sol' }) },
        { text: '💸 Sell', callback_data: JSON.stringify({ command: 'sell_sol' }) }
      ],
      [
        { text: '📈 Chart', callback_data: JSON.stringify({ command: 'chart_sol' }) },
        { text: '💸 Spend X sol', callback_data: JSON.stringify({ command: 'spend_sol' }) }
      ],
      [
        {
          text: "* Dismiss message",
          callback_data: JSON.stringify({
            command: "dismiss_message",
          }),
        },
      ]
      // [
      //   { text: 'Advanced Trade', callback_data: JSON.stringify({ command: 'snipetrade_dashboard_sol' }) }
      // ]
    ]
  };
};

// Update message with sell options
export const updateSolMessageWithSellOptions = async (bot: TelegramBot, chatId: number, messageId: number) => {
  try {
    const sellOptions = await getSolSellOptionsKeyboard();

    const contractAddress = getCurrentContractAddress(BlockchainType.SOL);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    const tokenDetails = getTokenDetails(contractAddress);

    if (!tokenDetails || !tokenDetails.resultMessage) {
      console.error('No result message found for the token address.');
      return;
    }
    await bot.editMessageText(tokenDetails.resultMessage, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: sellOptions
      }
    });
  } catch (error) {
    console.error('Failed to update sell options:', error);
  }
};


export const updateSolMessageWithTradeOptions = async (bot: TelegramBot, chatId: number, messageId: number, ) => {
  try {
    const tradeOptions = await getsolTradeOptionsKeyboard();

    const contractAddress = getCurrentContractAddress(BlockchainType.SOL);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    const tokenDetails = getTokenDetails(contractAddress);

    if (!tokenDetails || !tokenDetails.resultMessage) {
      console.error('No result message found for the token address.');
      return;
    }
    // Update the message with the new trade options
    await bot.editMessageText(tokenDetails.resultMessage, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: tradeOptions
      }
    });
  } catch (error) {
    console.error('Failed to update trade options:', error);
  }
};

export const mainSolMenu = async (bot: TelegramBot, chatId: number, messageId: number) => {
  try {
    const solOptions = createSolKeyboard();

    const contractAddress = getCurrentContractAddress(BlockchainType.SOL);
    if (!contractAddress) {
      console.error('No contract address found in global state.');
      return;
    }

    const tokenDetails = getTokenDetails(contractAddress);

    if (!tokenDetails || !tokenDetails.resultMessage) {
      console.error('No result message found for the token address.');
      return;
    }

    await bot.editMessageText(tokenDetails.resultMessage, {
      chat_id: chatId,
      message_id: messageId,
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: solOptions.inline_keyboard
      }
    });
  } catch (error) {
    console.error('Failed to update sol menu:', error);
    await bot.sendMessage(chatId, 'Failed to update the menu. Please try again later.');
  }
};


export const handleGenerateNewSolWallet = async (bot: TelegramBot, chatId: number) => {
  try {
    const user = await User.findOne({ chat_id: chatId });

    if (!user) {
      await bot.sendMessage(chatId, 'User not found.');
      return;
    }

    const newWallet = generateSOLWallet();

    await User.updateOne(
      { chat_id: chatId },
      { $set: { 'sol_wallet.address': newWallet.address, 'sol_wallet.private_key': newWallet.privateKey } }
    );

    // Send confirmation message
    await bot.sendMessage(
      chatId,
      `✅ You clicked "Generate New Wallet" in the sol blockchain.\nNew wallet address: \`\`\`${newWallet.address}\`\`\`\nNew wallet Private Key: \`\`\`${newWallet.privateKey}\`\`\``
    );
    await handleSolDashboard(bot, chatId);
  } catch (error) {
    console.error('Failed to generate new wallet:', error);
    await bot.sendMessage(chatId, 'There was an error generating a new wallet. Please try again later.');
  }
};

// Message ID storage
let promptMessageIdStore: Record<number, { promptId?: number, replyId?: number }> = {};

export const askForSolContractAddress = async (bot: TelegramBot, chatId: number, messageId: number): Promise<string> => {
  try {
    // Save previous message IDs if any
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    // Send message asking for contract address
    const sentMessage = await bot.sendMessage(chatId, INPUT_SOL_CONTRACT_ADDRESS, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    // Update the message ID store
    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received contract address:', msg.text); // Log the received text
          // Save the reply message ID
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid contract address.'));
        }
      });
    });
  } catch (error) {
    console.error('Failed to ask for contract address:', error);
    throw error;
  }
};

export const askForSolToken = async (bot: TelegramBot, chatId: number, messageId: number): Promise<string> => {
  try {
    // Save previous message IDs if any
    if (promptMessageIdStore[chatId]) {
      const ids = promptMessageIdStore[chatId];
      if (ids.promptId) {
        try {
          await bot.deleteMessage(chatId, ids.promptId);
        } catch (error) {
          console.error('Failed to delete previous prompt message:', error);
        }
      }
      if (ids.replyId) {
        try {
          await bot.deleteMessage(chatId, ids.replyId);
        } catch (error) {
          console.error('Failed to delete previous reply message:', error);
        }
      }
    }

    // Send message asking for BSC token
    const sentMessage = await bot.sendMessage(chatId, INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    // Update the message ID store
    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received Sol token:', msg.text); // Log the received text
          // Save the reply message ID
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid BSC token.'));
        }
      });
    });
  } catch (error) {
    console.error('Failed to ask for BSC token:', error);
    throw error;
  }
};


// Wrapper function to sell SOL tokens using the trading service
async function sellSolToken(chatId: number, percentage: number = 100): Promise<string> {
  try {
    const user = await User.findOne({ chat_id: chatId });
    if (!user) {
      throw new Error('User not found');
    }

    // Get the current contract address
    const contractAddress = getCurrentContractAddress(BlockchainType.SOL);
    if (!contractAddress) {
      throw new Error('No contract address found');
    }

    const trader = TraderFactory.getTrader(BlockchainType.SOL);

    const options = {
      slippage: 5 // Default slippage
    };

    const result = await trader.sellTokens(
      contractAddress,
      percentage,
      user.sol_wallet.address,
      options
    );

    if (!result.success) {
      throw new Error(result.error || 'Unknown error');
    }

    return result.transactionHash || '';
  } catch (error) {
    throw error;
  }
}



export async function fetchAndDisplayActiveSolTrades(bot: TelegramBot, chatId: number, blockchain: string) {
  try {
    // Find active trades for the specific user based on chatId and blockchain
    const tradesState = await TradeModel.find({ chatId, isSold: false, blockchain }).exec();
    let currentIndex = 0;

    if (tradesState.length === 0) {
      await bot.sendMessage(chatId, 'No active trades found!');
      return;
    }

    // Display the current trade information
    await displayCurrentSolTrade(bot, chatId, tradesState, currentIndex);
  } catch (error) {
    console.error('Error fetching trades:', error);
    await bot.sendMessage(chatId, 'An error occurred while fetching trades.');
  }
}



export async function displayCurrentSolTrade(bot: TelegramBot, chatId: number, tradesState: any[], index: number, messageId?: number) {
  const trade = tradesState[index];
  const message = `
<b>Contract:</b> ${trade.contractName} (${trade.contractAddress})
<b>Wallet:</b> ${trade.walletId}
<b>Buy Amount:</b> ${trade.buyAmount}  <b>Sell Amount:</b> ${trade.sellAmount}
<b>Sold:</b> ${trade.isSold ? 'Yes' : 'No'}
  `;

  // Update user details
  await User.findOneAndUpdate({ chat_id: chatId }, {
    solCurrentContractAddress: trade.contractAddress,
    solCurrentTokenName: trade.contractName,
  });


  // Update global state
  setCurrentContractAddress(BlockchainType.SOL, trade.contractAddress);

  const options = {
    parse_mode: "HTML" as const,
    disable_web_page_preview: true,
    reply_markup: {
      inline_keyboard: [
        [
          {
            text: "Sell 25%",
            callback_data: JSON.stringify({ command: "sell_25_sol" }),
          },
          {
            text: "Sell 50%",
            callback_data: JSON.stringify({ command: "sell_50_sol" }),
          },
        ],
        [
          {
            text: "Sell 75%",
            callback_data: JSON.stringify({ command: "sell_75_sol" }),
          },
          {
            text: "Sell All",
            callback_data: JSON.stringify({ command: "sell_all_sol" }),
          },
        ],
        [
          {
            text: "Previous",
            callback_data: JSON.stringify({ command: "previous_sol" }),
          },
          {
            text: "Next",
            callback_data: JSON.stringify({ command: "next_sol" }),
          },
        ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ],
      ],
    },
  };

  if (messageId) {
    await bot.editMessageText(message, { chat_id: chatId, message_id: messageId, ...options });
  } else {
    await bot.sendMessage(chatId, message, options);
  }
}


export const handleSolRefresh = async (bot: TelegramBot, chatId: number, callbackQuery?: TelegramBot.CallbackQuery) => {
  try {
    const currentContractAddress = getCurrentContractAddress(BlockchainType.SOL);
    if (!currentContractAddress) {
      throw new Error('No contract address is currently set.');
    }

    await handleSOLScanContract(bot, chatId, currentContractAddress);

    // If a callbackQuery is provided, show an alert
    if (callbackQuery) {
      const callbackQueryId = callbackQuery.id;
      await bot.answerCallbackQuery(callbackQueryId, {
        text: "Token Refresh Successfully!",
        show_alert: true
      });
    }

  } catch (error) {
    console.error('Failed to refresh contract details:', error);
    await bot.sendMessage(chatId, 'Failed to refresh contract details. Please try again later.');
  }
};


export const handleSolChart = async (bot: TelegramBot, chatId: number) => {
  try {
    const currentContractAddress = getCurrentContractAddress(BlockchainType.SOL);

    if (currentContractAddress) {
      const dexscreenerUrl = `https://dexscreener.com/solana/${currentContractAddress}`;
      const chartMessage = await bot.sendMessage(chatId, `<a href="${dexscreenerUrl}">Click here to view the chart on Dexscreener</a>`, { parse_mode: 'HTML' });

      promptMessageIdStore[chatId] = { promptId: chartMessage.message_id };
    } else {
      const errorMessage = await bot.sendMessage(chatId, 'Error: Current contract address is not set.');

      promptMessageIdStore[chatId] = { promptId: errorMessage.message_id };
    }
  } catch (error) {
    console.error('Failed to handle chart action:', error);
  }
};


export const handleSpendsol = async (bot: TelegramBot, chatId: number): Promise<string> => {
  try {
    const sentMessage = await bot.sendMessage(chatId, SET_SOL_SPEND, {
      parse_mode: 'HTML',
      reply_markup: {
        force_reply: true,
      },
    });

    promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };

    return new Promise<string>((resolve, reject) => {
      bot.onReplyToMessage(chatId, sentMessage.message_id, async (msg) => {
        if (msg.text) {
          console.log('Received buy amount:', msg.text);
          promptMessageIdStore[chatId] = { ...promptMessageIdStore[chatId], replyId: msg.message_id };
          resolve(msg.text);
        } else {
          reject(new Error('Invalid buy amount.'));
        }
      });
    });

  } catch (error) {
    console.error('Error handling spend sol:', error);
    throw new Error('Failed to handle spend sol action.');
  }
};


// export async function getsolTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
//   const tokenContract = new solers.Contract(
//       tokenAddress,
//       ['function balanceOf(address) view returns (uint)', 'function decimals() view returns (uint)'],
//       provider
//   );

//   const balance = await tokenContract.balanceOf(userAddress);

//   const tokenDecimals = await tokenContract.decimals();

//   const balanceWithDecimals = solers.utils.formatUnits(balance, tokenDecimals);
//   console.log(balanceWithDecimals);

//   return balanceWithDecimals;
// }



export const handleSolSellAll = async (bot: TelegramBot, chatId: number) => {
    try {
      const user = await User.findOne({ chat_id: chatId }).exec();
      if (!user) {
        throw new Error('User not found');
      }

      const { address: userAddress } = user.sol_wallet;
      const tokenAddress = user.solCurrentContractAddress || '';

      if (!tokenAddress) {
        throw new Error('No contract address found for user');
      }

      const connection = new Connection(process.env.SOL_PROVIDER_URL!);
      const tokenBalance = await getSolTokenBalance(connection, userAddress, tokenAddress);

      if (isNaN(tokenBalance) || tokenBalance <= 0) {
        throw new Error('Invalid token balance');
      }

      const receipt = await sellSolToken(chatId, 100); // Sell 100%

      if (receipt) {
        await bot.sendMessage(chatId, `✅ Successfully sold all your tokens:\n\n` +
          `🔗 Transaction Hash: ${receipt}\n\n` +
          `👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${receipt}\nTo see your transaction`);
      } else {
        await bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
      }
    } catch (error) {
      console.error('Error selling all tokens:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      await bot.sendMessage(chatId, `An error occurred while selling all tokens: ${errorMessage}`);
    }
  };

  export const sellSolTokenPercentage = async (bot: TelegramBot, chatId: number, percentage: number) => {
    try {
      const user = await User.findOne({ chat_id: chatId }).exec();
      if (!user) {
        throw new Error('User not found');
      }

      const { address: userAddress } = user.sol_wallet;
      const tokenAddress = user.solCurrentContractAddress;

      if (!tokenAddress) {
        throw new Error('No contract address found for user');
      }

      const connection = new Connection(process.env.SOL_PROVIDER_URL!);
      const tokenBalance = await getSolTokenBalance(connection, userAddress, tokenAddress);

      if (isNaN(tokenBalance) || tokenBalance <= 0) {
        throw new Error('Insufficient token balance');
      }

      const amountToSell = tokenBalance * (percentage / 100);
      if (amountToSell <= 0) {
        throw new Error('Invalid percentage value');
      }

      console.log('Percentage to sell:', percentage);
      const receipt = await sellSolToken(chatId, percentage);

      if (receipt) {
        await bot.sendMessage(chatId, `✅ Successfully sold ${percentage}% of your tokens:\n\n` +
          `🔗 Transaction Hash: ${receipt}\n\n` +
          `👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${receipt}\nTo see your transaction`);
      } else {
        await bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
      }
    } catch (error) {
      console.error('Error selling tokens:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      await bot.sendMessage(chatId, `An error occurred while selling tokens: ${errorMessage}`);
    }
  };


// Retrieve token details by contract address
export const getTokenDetails = (contractAddress: string): TokenDetails | undefined => {
  return localState[contractAddress];
};
export { handleSolDashboard };


