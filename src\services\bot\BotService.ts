/**
 * Telegram Bot Service
 *
 * This service provides a unified interface for interacting with Telegram bots.
 * It handles message sending, callback queries, and other bot-related functionality.
 */

import TelegramBot from 'node-telegram-bot-api';
import { logger } from '../logger/LoggerService';
import { BlockchainType } from '../trading/TradingInterface';

/**
 * Message options interface
 */
export interface MessageOptions {
  parse_mode?: 'Markdown' | 'HTML';
  reply_markup?: any;
  reply_to_message_id?: number;
  disable_web_page_preview?: boolean;
}

/**
 * Bot service class
 */
export class BotService {
  private bot: TelegramBot;
  private messageStore: Map<number, Map<string, number>> = new Map();

  /**
   * Create a new bot service
   * @param bot The Telegram bot instance
   */
  constructor(bot: TelegramBot) {
    this.bot = bot;
    logger.info('Bot service initialized');
  }

  /**
   * Send a message to a chat
   * @param chatId The chat ID
   * @param message The message to send
   * @param options Message options
   * @returns The sent message
   */
  async sendMessage(
    chatId: number,
    message: string,
    options?: MessageOptions
  ): Promise<TelegramBot.Message | null> {
    try {
      const sentMessage = await this.bot.sendMessage(chatId, message, options);
      logger.debug(`Message sent to ${chatId}`, { messageId: sentMessage.message_id });
      return sentMessage;
    } catch (error) {
      // Check if the error is because the user blocked the bot
      if ((error as Error).message.includes('403 Forbidden: bot was blocked by the user')) {
        logger.warn(`User ${chatId} has blocked the bot`, { chatId });
        return null;
      } else {
        logger.error(`Failed to send message to ${chatId}`, {
          error: (error as Error).message,
          chatId
        });
      }
      return null; // Return null instead of throwing to prevent unhandled rejections
    }
  }

  /**
   * Edit a message
   * @param chatId The chat ID
   * @param messageId The message ID
   * @param text The new text
   * @param options Message options
   * @returns The edited message
   */
  async editMessage(
    chatId: number,
    messageId: number,
    text: string,
    options?: MessageOptions
  ): Promise<TelegramBot.Message | boolean | null> {
    try {
      const editedMessage = await this.bot.editMessageText(text, {
        chat_id: chatId,
        message_id: messageId,
        ...options
      });
      logger.debug(`Message ${messageId} edited in chat ${chatId}`);
      return editedMessage;
    } catch (error) {
      // Check if the error is because the user blocked the bot
      if ((error as Error).message.includes('403 Forbidden: bot was blocked by the user')) {
        logger.warn(`User ${chatId} has blocked the bot`, { chatId, messageId });
        return null;
      } else if ((error as Error).message.includes('message is not modified')) {
        // This is not really an error, just log it as info
        logger.info(`Message ${messageId} in chat ${chatId} was not modified (content unchanged)`);
        return true;
      } else {
        logger.error(`Failed to edit message ${messageId} in chat ${chatId}`, {
          error: (error as Error).message,
          chatId,
          messageId
        });
      }
      return null; // Return null instead of throwing to prevent unhandled rejections
    }
  }

  /**
   * Delete a message
   * @param chatId The chat ID
   * @param messageId The message ID
   * @returns Whether the message was deleted
   */
  async deleteMessage(chatId: number, messageId: number): Promise<boolean> {
    try {
      const result = await this.bot.deleteMessage(chatId, messageId);
      logger.debug(`Message ${messageId} deleted from chat ${chatId}`);
      return result;
    } catch (error) {
      logger.error(`Failed to delete message ${messageId} from chat ${chatId}`, {
        error: (error as Error).message,
        chatId,
        messageId
      });
      return false;
    }
  }

  /**
   * Store a message ID for a specific context
   * @param chatId The chat ID
   * @param context The context (e.g., 'contract_scan', 'wallet_info')
   * @param messageId The message ID
   */
  storeMessageId(chatId: number, context: string, messageId: number): void {
    if (!this.messageStore.has(chatId)) {
      this.messageStore.set(chatId, new Map());
    }

    const chatMessages = this.messageStore.get(chatId)!;
    chatMessages.set(context, messageId);
  }

  /**
   * Get a stored message ID
   * @param chatId The chat ID
   * @param context The context
   * @returns The message ID or undefined if not found
   */
  getStoredMessageId(chatId: number, context: string): number | undefined {
    const chatMessages = this.messageStore.get(chatId);
    if (!chatMessages) return undefined;

    return chatMessages.get(context);
  }

  /**
   * Create a keyboard for blockchain selection
   * @returns The keyboard markup
   */
  createBlockchainKeyboard(): TelegramBot.InlineKeyboardMarkup {
    return {
      inline_keyboard: [
        [
          { text: '🔷 BSC', callback_data: JSON.stringify({ command: 'view_bsc' }) },
          { text: '🔶 ETH', callback_data: JSON.stringify({ command: 'view_eth' }) }
        ],
        [
          { text: '🔵 BASE', callback_data: JSON.stringify({ command: 'view_base' }) },
          { text: '⚪ SOL', callback_data: JSON.stringify({ command: 'view_sol' }) }
        ],
        [
          { text: '⚙️ Settings', callback_data: JSON.stringify({ command: 'show_config' }) }
        ]
      ]
    };
  }

  /**
   * Create a keyboard for a specific blockchain
   * @param blockchain The blockchain type
   * @returns The keyboard markup
   */
  createBlockchainSpecificKeyboard(blockchain: BlockchainType): TelegramBot.InlineKeyboardMarkup {
    const baseKeyboard = [
      [
        {
          text: '🔍 Scan Contract',
          callback_data: JSON.stringify({ command: `scan_contract_${blockchain}` })
        },
        {
          text: '👛 Generate Wallet',
          callback_data: JSON.stringify({ command: `generate_new_${blockchain}_wallet` })
        }
      ],
      [
        {
          text: '🔫 Snipe Token',
          callback_data: JSON.stringify({ command: `scan_snipe_${blockchain}` })
        },
        {
          text: '📊 Active Trades',
          callback_data: JSON.stringify({ command: `active_trades_${blockchain}` })
        }
      ],
      [
        {
          text: '🏠 Main Menu',
          callback_data: JSON.stringify({ command: 'main_menu' })
        }
      ]
    ];

    return { inline_keyboard: baseKeyboard };
  }

  /**
   * Get the raw bot instance
   * @returns The Telegram bot instance
   */
  getBotInstance(): TelegramBot {
    return this.bot;
  }
}

// Export a factory function to create bot services
export function createBotService(bot: TelegramBot): BotService {
  return new BotService(bot);
}
