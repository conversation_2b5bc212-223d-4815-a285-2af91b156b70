import mongoose, { Document, Schema, Types } from 'mongoose';

// Define the IWalletConfig interface
interface IWalletConfig {
  slippage: number;
  antirug: boolean;
  antiMev: boolean;
  maxGasPrice: number;
  maxGasLimit: number;
  autoBuy: boolean;
  minLiquidity: number;
  maxMarketCap: number;
  maxLiquidity: number;
  autoSell: boolean;
  sellHigh: number;
  sellLow: number;
  autoApprove: boolean;
}

export { IWalletConfig };

// Define the IUser interface
export interface IUser extends Document {
  chat_id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  sol_wallet: {
    address: string;
    private_key: string;
  };
  bsc_wallet: {
    address: string;
    private_key: string;
  };
  base_wallet: {
    address: string;
    private_key: string;
  };
  eth_wallet: {
    address: string;
    private_key: string;
  };
  preset_setting: number[];
  nonce: number;
  retired: boolean;
  referrer_code?: string;
  referrer_wallet?: string;
  referral_code?: string;
  referral_date?: string;
  schedule: string;
  burn_fee: boolean;
  auto_buy: boolean;
  auto_buy_amount: string;
  auto_sell_amount: string;
  trades: {
    bsc: Types.Array<Types.ObjectId>;
    sol: Types.Array<Types.ObjectId>;
    eth: Types.Array<Types.ObjectId>;
    base: Types.Array<Types.ObjectId>;
  };
  currentBlockchain: 'bsc' | 'sol' | 'eth' | 'base';
  sol_config?: IWalletConfig;
  bsc_config?: IWalletConfig;
  base_config?: IWalletConfig;
  eth_config?: IWalletConfig;
  bsc_dashboard_message_id?: number;
  bsc_dashboard_content?: string;
  bsc_dashboard_markup?: any;
  eth_dashboard_message_id?: number;
  eth_dashboard_content?: string;
  eth_dashboard_markup?: any;
  sol_dashboard_message_id?: number;
  sol_dashboard_content?: string;
  sol_dashboard_markup?: any;
  base_dashboard_message_id?: number;
  base_dashboard_content?: string;
  base_dashboard_markup?: any;
  bscCurrentContractAddress?: string;
  solCurrentContractAddress?: string;
  baseCurrentContractAddress?: string;
  ethCurrentContractAddress?: string;
  bscCurrentTokenName?: string;
  solCurrentTokenName?: string;
  baseCurrentTokenName?: string;
  ethCurrentTokenName?: string;
  snipes: Types.Array<Types.ObjectId>;
}

// Define the user schema
const userSchema = new Schema<IUser>({
  chat_id: { type: Number, required: true },
  first_name: { type: String, required: true },
  last_name: { type: String },
  username: { type: String },
  sol_wallet: {
    address: { type: String, required: true },
    private_key: { type: String, required: true },
  },
  bsc_wallet: {
    address: { type: String, required: true },
    private_key: { type: String, required: true },
  },
  base_wallet: {
    address: { type: String, required: true },
    private_key: { type: String, required: true },
  },
  eth_wallet: {
    address: { type: String, required: true },
    private_key: { type: String, required: true },
  },
  preset_setting: { type: [Number], default: [0.01, 1, 5, 10] },
  nonce: { type: Number, default: 0 },
  retired: { type: Boolean, default: false },
  referrer_code: { type: String, default: null },
  referrer_wallet: { type: String, default: null },
  referral_code: { type: String, default: null },
  referral_date: { type: String, default: null },
  schedule: { type: String, default: '60' },
  burn_fee: { type: Boolean, default: false },
  auto_buy: { type: Boolean, default: false },
  auto_buy_amount: { type: String, default: '0' },
  auto_sell_amount: { type: String, default: '0' },
  trades: {
    bsc: [{ type: Schema.Types.ObjectId, ref: 'Trade', default: [] }],
    sol: [{ type: Schema.Types.ObjectId, ref: 'Trade', default: [] }],
    eth: [{ type: Schema.Types.ObjectId, ref: 'Trade', default: [] }],
    base: [{ type: Schema.Types.ObjectId, ref: 'Trade', default: [] }],
  },
  currentBlockchain: { 
    type: String, 
    enum: ['bsc', 'sol', 'eth', 'base'], 
    default: 'bsc',
  },
  sol_config: {
    slippage: { type: Schema.Types.Decimal128, default: 0.6 },
    antirug: { type: Boolean, default: false },
    antiMev: { type: Boolean, default: false },
    maxGasPrice: { type: Schema.Types.Decimal128, default: 10 },
    maxGasLimit: { type: Number, default: 1000000 },
    autoBuy: { type: Boolean, default: false },
    minLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    maxMarketCap: { type: Schema.Types.Decimal128, default: 0 },
    maxLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    autoSell: { type: Boolean, default: false },
    sellHigh: { type: Schema.Types.Decimal128, default: 0 },
    sellLow: { type: Schema.Types.Decimal128, default: 0 },
    autoApprove: { type: Boolean, default: false },
  },
  bsc_config: {
    slippage: { type: Schema.Types.Decimal128, default: 0.1 },
    antirug: { type: Boolean, default: false },
    antiMev: { type: Boolean, default: false },
    maxGasPrice: { type: Schema.Types.Decimal128, default: 10 },
    maxGasLimit: { type: Number, default: 1000000 },
    autoBuy: { type: Boolean, default: false },
    minLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    maxMarketCap: { type: Schema.Types.Decimal128, default: 0 },
    maxLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    autoSell: { type: Boolean, default: false },
    sellHigh: { type: Schema.Types.Decimal128, default: 0 },
    sellLow: { type: Schema.Types.Decimal128, default: 0 },
    autoApprove: { type: Boolean, default: false },
  },
  base_config: {
    slippage: { type: Schema.Types.Decimal128, default: 0.1 },
    antirug: { type: Boolean, default: false },
    antiMev: { type: Boolean, default: false },
    maxGasPrice: { type: Schema.Types.Decimal128, default: 10 },
    maxGasLimit: { type: Number, default: 1000000 },
    autoBuy: { type: Boolean, default: false },
    minLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    maxMarketCap: { type: Schema.Types.Decimal128, default: 0 },
    maxLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    autoSell: { type: Boolean, default: false },
    sellHigh: { type: Schema.Types.Decimal128, default: 0 },
    sellLow: { type: Schema.Types.Decimal128, default: 0 },
    autoApprove: { type: Boolean, default: false },
  },
  eth_config: {
    slippage: { type: Schema.Types.Decimal128, default: 0.2 },
    antirug: { type: Boolean, default: false },
    antiMev: { type: Boolean, default: false },
    maxGasPrice: { type: Schema.Types.Decimal128, default: 10 },
    maxGasLimit: { type: Number, default: 1000000 },
    autoBuy: { type: Boolean, default: false },
    minLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    maxMarketCap: { type: Schema.Types.Decimal128, default: 0 },
    maxLiquidity: { type: Schema.Types.Decimal128, default: 0 },
    autoSell: { type: Boolean, default: false },
    sellHigh: { type: Schema.Types.Decimal128, default: 0 },
    sellLow: { type: Schema.Types.Decimal128, default: 0 },
    autoApprove: { type: Boolean, default: false },
  },
  bsc_dashboard_message_id: { type: Number },
  bsc_dashboard_content: { type: String },
  bsc_dashboard_markup: { type: Schema.Types.Mixed },
  eth_dashboard_message_id: { type: Number },
  eth_dashboard_content: { type: String },
  eth_dashboard_markup: { type: Schema.Types.Mixed },
  sol_dashboard_message_id: { type: Number },
  sol_dashboard_content: { type: String },
  sol_dashboard_markup: { type: Schema.Types.Mixed },
  base_dashboard_message_id: { type: Number },
  base_dashboard_content: { type: String },
  base_dashboard_markup: { type: Schema.Types.Mixed },
  bscCurrentContractAddress: { type: String },
  solCurrentContractAddress: { type: String },
  baseCurrentContractAddress: { type: String },
  ethCurrentContractAddress: { type: String },
  bscCurrentTokenName: { type: String },
  solCurrentTokenName: { type: String },
  baseCurrentTokenName: { type: String },
  ethCurrentTokenName: { type: String },
  snipes: [{ type: Schema.Types.ObjectId, ref: 'Snipe', default: [] }],
});

const UserModel = mongoose.model<IUser>('User', userSchema);

export default UserModel;
