/**
 * Unified Honeypot Checker Service
 *
 * This service provides a unified interface for checking if tokens are honeypots
 * across all supported blockchains (ETH, BSC, BASE, SOL).
 */

import axios from 'axios';
import { BlockchainType } from '../trading/TradingInterface';
import { logger } from '../logger/LoggerService';
import { cacheService } from '../cache/RedisService';

// Cache TTL in seconds (1 hour)
const CACHE_TTL = 3600;

/**
 * Risk level definition
 */
export type RiskLevel = 'low' | 'medium' | 'high' | 'danger';

/**
 * Risk information interface
 */
export interface Risk {
  name: string;
  description: string;
  level: RiskLevel;
}

/**
 * Honeypot flag interface (for EVM chains)
 */
export interface HoneypotFlag {
  description: string;
}

/**
 * Unified honeypot check result interface
 */
export interface HoneypotCheckResult {
  isHoneypot: boolean;
  risk: string;
  buyTax: string;
  sellTax: string;
  transferTax?: string;
  buyGas?: string | number;
  sellGas?: string | number;
  warnings: string[];
  details: string;
  timestamp: number;
}

/**
 * Get the appropriate honeypot API URL for EVM blockchains
 * @param blockchain The blockchain type
 * @returns The API URL
 */
function getEvmHoneypotApiUrl(blockchain: BlockchainType): string {
  switch (blockchain) {
    case BlockchainType.ETH:
      return 'https://api.honeypot.is/v2/IsHoneypot';
    case BlockchainType.BSC:
      return 'https://bsc.honeypot.is/v2/IsHoneypot';
    case BlockchainType.BASE:
      return 'https://base.honeypot.is/v2/IsHoneypot';
    default:
      throw new Error(`Unsupported blockchain for honeypot check: ${blockchain}`);
  }
}

/**
 * Check if an EVM token is a honeypot
 * @param tokenAddress The token address
 * @param blockchain The blockchain type
 * @returns Honeypot check result
 */
async function checkEvmHoneypot(
  tokenAddress: string,
  blockchain: BlockchainType
): Promise<HoneypotCheckResult> {
  const startTime = Date.now();
  const cacheKey = `honeypot:${blockchain}:${tokenAddress}`;

  try {
    // Check cache first
    const cachedResult = await cacheService.get<HoneypotCheckResult>(cacheKey);
    if (cachedResult) {
      logger.debug(`Using cached honeypot check for ${tokenAddress} on ${blockchain}`, {
        tokenAddress,
        blockchain,
        isHoneypot: cachedResult.isHoneypot
      });
      return cachedResult;
    }

    // Determine API endpoint based on blockchain
    const apiUrl = getEvmHoneypotApiUrl(blockchain);

    // Make API request
    const response = await axios.get(apiUrl, {
      params: { address: tokenAddress }
    });

    const { summary, simulationResult } = response.data;

    // Process flag descriptions
    let flagDescriptions = summary.flags.map((flag: HoneypotFlag) => flag.description);
    const warnings: string[] = [];

    // Add warning messages based on risk level
    if (summary.risk === 'honeypot') {
      warnings.push('CRITICAL: Honeypot detected - High risk token');
    } else if (summary.risk === 'unknown') {
      warnings.push('WARNING: Could not determine if this is a honeypot. Proceed with caution.');
    }

    // Handle closed source warning
    if (flagDescriptions.includes('The source code is not available, allowing for hidden functionality.')) {
      warnings.push("WARNING: Contract's dependencies are closed source, allowing for hidden functionalities.");
      flagDescriptions = flagDescriptions.filter((flag: string) => flag !== 'The source code is not available, allowing for hidden functionality.');
    }

    // Filter out known false positives
    flagDescriptions = flagDescriptions.filter((flag: string) =>
      flag !== 'All snipers are marked as honeypots (blacklisted). Sniper detection still needs some improvements.'
    );

    // Get tax information
    const buyTax = simulationResult?.buyTax ?? '0';
    const sellTax = simulationResult?.sellTax ?? '0';
    const transferTax = simulationResult?.transferTax ?? '0';

    // Create the result object
    const result: HoneypotCheckResult = {
      isHoneypot: summary.risk === 'honeypot',
      risk: summary.risk,
      buyTax,
      sellTax,
      transferTax,
      buyGas: simulationResult?.buyGas,
      sellGas: simulationResult?.sellGas,
      warnings: warnings,
      details: flagDescriptions.join('\n') || 'No specific risks identified',
      timestamp: Date.now()
    };

    // Cache the result
    await cacheService.set(cacheKey, result, CACHE_TTL);

    // Log performance and result
    logger.logPerformance(`${blockchain} honeypot check`, startTime, {
      tokenAddress,
      isHoneypot: result.isHoneypot,
      risk: result.risk
    });

    return result;
  } catch (error: any) {
    logger.error(`Failed to check if ${blockchain} token is honeypot`, {
      error: error.message,
      tokenAddress,
      blockchain
    });

    // Create a fallback result
    const fallbackResult: HoneypotCheckResult = {
      isHoneypot: false,
      risk: 'unknown',
      buyTax: '0',
      sellTax: '0',
      warnings: ['Failed to check honeypot status'],
      details: `Error: ${error.message}`,
      timestamp: Date.now()
    };

    // Cache the error result for a shorter time (5 minutes)
    await cacheService.set(cacheKey, fallbackResult, 300);

    return fallbackResult;
  }
}

/**
 * Check if a Solana token is a honeypot
 * @param tokenAddress The token address
 * @returns Honeypot check result
 */
async function checkSolanaHoneypot(tokenAddress: string): Promise<HoneypotCheckResult> {
  const startTime = Date.now();
  const cacheKey = `honeypot:sol:${tokenAddress}`;

  try {
    // Check cache first
    const cachedResult = await cacheService.get<HoneypotCheckResult>(cacheKey);
    if (cachedResult) {
      logger.debug(`Using cached honeypot check for ${tokenAddress} on SOL`, {
        tokenAddress,
        isHoneypot: cachedResult.isHoneypot
      });
      return cachedResult;
    }

    // Make API request to RugCheck
    const url = `https://api.rugcheck.xyz/v1/tokens/${tokenAddress}/report/summary`;
    const response = await axios.get(url);
    const data = response.data;

    // Extract risk descriptions and check for "danger" level
    let riskDescriptions: string[] = [];
    const warnings: string[] = [];
    let dangerDetected = false;

    if (data.risks && data.risks.length > 0) {
      riskDescriptions = data.risks.map((risk: Risk) => {
        if (risk.level === 'danger') {
          dangerDetected = true;
          warnings.push(`DANGER: ${risk.name} - ${risk.description}`);
        } else if (risk.level === 'high') {
          warnings.push(`HIGH RISK: ${risk.name} - ${risk.description}`);
        }
        return `${risk.name}: ${risk.description} (${risk.level})`;
      });
    }

    // Add special warning if danger level is found
    if (dangerDetected) {
      warnings.unshift('CRITICAL: Honeypot detected - High risk token');
    }

    // Check if simulationResult and its properties are defined
    const buyTax = data.simulationResult?.buyTax ?? '0';
    const sellTax = data.simulationResult?.sellTax ?? '0';
    const transferTax = data.simulationResult?.transferTax ?? '0';

    // Create the result object
    const result: HoneypotCheckResult = {
      isHoneypot: dangerDetected,
      risk: data.score,
      buyTax,
      sellTax,
      transferTax,
      warnings,
      details: riskDescriptions.join('\n') || 'No specific risks identified',
      timestamp: Date.now()
    };

    // Cache the result
    await cacheService.set(cacheKey, result, CACHE_TTL);

    // Log performance and result
    logger.logPerformance('SOL honeypot check', startTime, {
      tokenAddress,
      isHoneypot: result.isHoneypot,
      risk: result.risk
    });

    return result;
  } catch (error: any) {
    logger.error('Failed to check if SOL token is honeypot', {
      error: error.message,
      tokenAddress
    });

    // Create a fallback result
    const fallbackResult: HoneypotCheckResult = {
      isHoneypot: false,
      risk: 'unknown',
      buyTax: '0',
      sellTax: '0',
      warnings: ['Failed to check honeypot status'],
      details: `Error: ${error.message}`,
      timestamp: Date.now()
    };

    // Cache the error result for a shorter time (5 minutes)
    await cacheService.set(cacheKey, fallbackResult, 300);

    return fallbackResult;
  }
}

/**
 * Check if a token is a honeypot on any supported blockchain
 * @param tokenAddress The token address
 * @param blockchain The blockchain type
 * @returns Honeypot check result
 */
export async function checkIfHoneypot(
  tokenAddress: string,
  blockchain: BlockchainType
): Promise<HoneypotCheckResult> {
  try {
    if (blockchain === BlockchainType.SOL) {
      return await checkSolanaHoneypot(tokenAddress);
    } else {
      return await checkEvmHoneypot(tokenAddress, blockchain);
    }
  } catch (error: any) {
    logger.error(`Failed to check if token is honeypot`, {
      error: error.message,
      tokenAddress,
      blockchain
    });

    // Create a fallback result
    return {
      isHoneypot: false,
      risk: 'unknown',
      buyTax: '0',
      sellTax: '0',
      warnings: ['Failed to check honeypot status'],
      details: `Error: ${error.message}`,
      timestamp: Date.now()
    };
  }
}

/**
 * Format honeypot check result as a human-readable message
 * @param result The honeypot check result
 * @returns A formatted message
 */
export function formatHoneypotResult(result: HoneypotCheckResult): string {
  let message = '';

  if (result.isHoneypot) {
    message += '🚨 **HONEYPOT DETECTED** 🚨\n\n';
  } else if (result.risk === 'unknown') {
    message += '⚠️ **UNKNOWN RISK** ⚠️\n\n';
  } else {
    message += '✅ **Not detected as a honeypot**\n\n';
  }

  message += `**Risk Level:** ${result.risk}\n`;
  message += `**Buy Tax:** ${result.buyTax}%\n`;
  message += `**Sell Tax:** ${result.sellTax}%\n`;

  if (result.transferTax) {
    message += `**Transfer Tax:** ${result.transferTax}%\n`;
  }

  if (result.warnings.length > 0) {
    message += '\n**Warnings:**\n';
    result.warnings.forEach(warning => {
      message += `- ${warning}\n`;
    });
  }

  if (result.details && result.details !== 'No specific risks identified') {
    message += '\n**Details:**\n';
    message += result.details;
  }

  return message;
}
