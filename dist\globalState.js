"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setCurrentContractAddress = exports.getCurrentContractAddress = void 0;
const globalState = {
    currentContractAddress: null,
};
const getCurrentContractAddress = () => {
    return globalState.currentContractAddress;
};
exports.getCurrentContractAddress = getCurrentContractAddress;
const setCurrentContractAddress = (address) => {
    globalState.currentContractAddress = address;
};
exports.setCurrentContractAddress = setCurrentContractAddress;
exports.default = globalState;
