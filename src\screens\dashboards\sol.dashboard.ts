import TelegramBot, { SendMessageOptions } from 'node-telegram-bot-api';
import User from '../../models/user.models';
import { Connection, clusterApiUrl, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { getSolBalance } from '../../services/solscan.service';

export const handleSolDashboard = async (bot: TelegramBot, chat_id: number) => {
  try {
    const user = await User.findOne({ chat_id });

    if (!user) {
      await bot.sendMessage(chat_id, 'User not found.');
      return;
    }

    const firstName = user.first_name || 'User';
    const solWallet = user.sol_wallet?.address || 'Not set';
    const balance = solWallet !== 'Not set' ? await getSolBalance(solWallet) : '0.0 sol';

    const welcomeMessage = `👋 Welcome, ${firstName}, to your sol Dashboard!
\n📍 **Address:** \`${solWallet}\`
\n🔗 **Blockchain:** Solana
\n💰 **Balance:** \`${balance} SOL\`
\n⚙️ **Settings:** [Antirug, etc.]`;

    const options: SendMessageOptions = {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_sol' }) }
          ],
          [
            { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
            { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_sol' }) }
          ],
          [
            { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_sol' }) },
            { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_sol_wallet' }) }
          ],
          [
            {
              text: "* Dismiss message",
              callback_data: JSON.stringify({
                command: "dismiss_message",
              }),
            },
          ]
        ]
      }
    };

    const sentMessage = await bot.sendMessage(chat_id, welcomeMessage, options);

    await User.updateOne({ chat_id: chat_id }, {
      $set: {
        'sol_dashboard_message_id': sentMessage.message_id,
        'sol_dashboard_content': welcomeMessage,
        'sol_dashboard_markup': options.reply_markup
      }
    });
  } catch (error) {
    console.error('Error handling Sol dashboard:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    await bot.sendMessage(chat_id, `An error occurred while loading the Sol dashboard: ${errorMessage}`);
  }
};
