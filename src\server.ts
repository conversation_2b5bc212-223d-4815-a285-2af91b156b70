import express from 'express';
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import TelegramBot from 'node-telegram-bot-api';
import { initializeSocketServer } from './cronjob/snipemonitor';
import { initializeWalletMonitor } from './services/wallet/WalletMonitorService';
import { handleCallbackQuery } from './services/bot/CallbackQueryHandler';
import { privatekeyHandler } from './screens/privatekeyScreen';
import newUserHandler from './screens/welcomeScreen';
import walletHandler from './screens/walletScreen';
import snipeWelcomeHandler from './screens/sniperScreen';
import { fetchBalancesForAllChains } from './admin';
import { logger } from './services/logger/LoggerService';
import { cacheService } from './services/cache/RedisService';
import { createBotService } from './services/bot/BotService';
import { initializeServices } from './services';

// Load environment variables
dotenv.config();

// Get environment variables
const token = process.env.TELEGRAM_BOT_TOKEN || '';
const adminBot = process.env.ADMIN_BOT_TOKEN || '';
const MONGODB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/mydatabase';
const socketPort = process.env.SOCKET_PORT || 3000;

if (!token || !adminBot) {
  throw new Error('TELEGRAM_BOT_TOKEN or ADMIN_BOT_TOKEN is not defined in the .env file');
}

// MongoDB connection options
const connectOptions: mongoose.ConnectOptions = {
  autoCreate: true,
  retryReads: true,
};

// Connect to MongoDB
const connectMongodb = () => mongoose.connect(MONGODB_URL, connectOptions);

// Initialize Telegram bot
const initializeBot = () => {
  // Set max listeners for the process to avoid memory leak warnings
  process.setMaxListeners(20);

  const bot = new TelegramBot(token, { polling: true });
  const botService = createBotService(bot);

  // Set max listeners for the bot to avoid memory leak warnings
  (bot as any).setMaxListeners?.(20);

  // Error handling for blocked users
  bot.on('error', (error) => {
    if (error.code === 'ETELEGRAM' && error.response?.statusCode === 403) {
      logger.warn('Bot was blocked by a user', { error: error.message });
      return;
    }
    logger.error('Telegram bot error', { error });
  });

  // Register event handlers
  bot.on('callback_query', async (query: TelegramBot.CallbackQuery) => {
    await handleCallbackQuery(bot, query);
  });

  bot.onText(/\/start/, async (msg: TelegramBot.Message) => {
    await newUserHandler(bot, msg);
  });

  bot.onText(/\/sniper/, async (msg: TelegramBot.Message) => {
    await snipeWelcomeHandler(bot, msg);
  });

  bot.onText(/\/wallets/, async (msg: TelegramBot.Message) => {
    await walletHandler(bot, msg);
  });

  bot.onText(/\/privatekeys/, async (msg: TelegramBot.Message) => {
    await privatekeyHandler(bot, msg);
  });

  logger.info('Bot initialized with all handlers');
  return { bot, botService };
};

const initializeAdminBot = () => {
  const superBot = new TelegramBot(adminBot, { polling: true });
  const botService = createBotService(superBot);

  // Set max listeners for the admin bot to avoid memory leak warnings
  (superBot as any).setMaxListeners?.(20);

  // Register event handlers
  superBot.on('callback_query', async (query: TelegramBot.CallbackQuery) => {
    await handleCallbackQuery(superBot, query);
  });

  superBot.onText(/\/start/, async (msg: TelegramBot.Message) => {
    await newUserHandler(superBot, msg);
  });

  superBot.onText(/\/sniper/, async (msg: TelegramBot.Message) => {
    await snipeWelcomeHandler(superBot, msg);
  });

  superBot.onText(/\/wallets/, async (msg: TelegramBot.Message) => {
    await walletHandler(superBot, msg);
  });

  superBot.onText(/\/privatekeys/, async (msg: TelegramBot.Message) => {
    await privatekeyHandler(superBot, msg);
  });

  logger.info('Admin bot initialized with all handlers');
  return { bot: superBot, botService };
};

// Initialize the application
// Initialize Express app
const app = express();
const port = process.env.PORT || 8000;

// Connect to MongoDB and initialize bot
connectMongodb()
  .then(async () => {
    logger.info('Connected to MongoDB successfully');

    // Initialize bots
    const { bot } = initializeBot();
    // Initialize admin bot
    initializeAdminBot();

    // Initialize services
    initializeServices(bot);

    // Fetch balances for all chains
    fetchBalancesForAllChains();

    // Initialize cache
    await cacheService.set('server_start_time', new Date().toISOString());
    logger.info('Cache service initialized');

    // Start the Socket.IO server
    initializeSocketServer(Number(socketPort), bot);

    // Initialize wallet monitoring service
    initializeWalletMonitor(bot);

    // Define routes
    app.get('/', (_req, res) => {
      res.send('Easy Sniper Bot is running');
    });

    // Health check endpoint
    app.get('/health', async (_req, res) => {
      try {
        const startTime = await cacheService.get('server_start_time');
        const monitoringActive = await cacheService.get('wallet_monitor:active') || false;

        // Get wallet monitoring stats
        const walletBalanceCount = await mongoose.model('WalletBalance').countDocuments();

        res.json({
          status: 'ok',
          uptime: process.uptime(),
          startTime,
          version: process.env.npm_package_version || '1.0.0',
          services: {
            walletMonitor: {
              active: monitoringActive,
              walletCount: walletBalanceCount
            }
          }
        });
      } catch (error: any) {
        res.status(500).json({ status: 'error', message: error.message || 'Unknown error' });
      }
    });

    // Start the Express server
    app.listen(port, () => {
      logger.info(`Server is running on port ${port}`);
    });

    // Handle graceful shutdown
    const gracefulShutdown = async () => {
      logger.info('Graceful shutdown initiated');

      // Import the wallet monitor service here to avoid circular dependencies
      const { walletMonitorService } = await import('./services/wallet/WalletMonitorService');

      // Stop the wallet monitor service
      await walletMonitorService.stop();
      logger.info('Wallet monitor service stopped');

      // Close the HTTP server
      await new Promise<void>((resolve) => {
        const server = app.listen();
        if (server) {
          server.close(() => {
            logger.info('HTTP server closed');
            resolve();
          });
        } else {
          logger.info('No HTTP server to close');
          resolve();
        }
      });

      // Close MongoDB connection
      await mongoose.connection.close();
      logger.info('MongoDB connection closed');

      // Exit gracefully
      process.exit(0);
    };

    // Register shutdown handlers
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);
  })
  .catch(err => {
    logger.error('Error connecting to MongoDB:', { error: err.message });
  });

