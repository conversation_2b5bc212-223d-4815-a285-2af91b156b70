"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SET_SOL_SPEND = exports.SET_ETH_SPEND = exports.SET_BASE_SPEND = exports.SET_BSC_SPEND = exports.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE = exports.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE = exports.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE = exports.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE = exports.INPUT_SOL_CONTRACT_ADDRESS = exports.INPUT_ETH_CONTRACT_ADDRESS = exports.INPUT_BASE_CONTRACT_ADDRESS = exports.INPUT_CONTRACT_ADDRESS = void 0;
exports.INPUT_CONTRACT_ADDRESS = 'Please submit the BSC contract address you want to scan.';
exports.INPUT_BASE_CONTRACT_ADDRESS = 'Please submit the BASE contract address you want to scan.';
exports.INPUT_ETH_CONTRACT_ADDRESS = 'Please submit the ETH contract address you want to scan.';
exports.INPUT_SOL_CONTRACT_ADDRESS = 'Please submit the SOL contract address you want to scan.';
exports.INPUT_BSC_CONTRACT_ADDRESS_TOSNIPE = 'Please enter the BSC token you want to snipe';
exports.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE = 'Please enter the BASE token you want to snipe';
exports.INPUT_ETH_CONTRACT_ADDRESS_TOSNIPE = 'Please enter the ETH token you want to snipe';
exports.INPUT_SOL_CONTRACT_ADDRESS_TOSNIPE = 'Please enter the SOL token you want to snipe';
exports.SET_BSC_SPEND = 'Input how much BSC to spend';
exports.SET_BASE_SPEND = 'Input how much BASE to spend';
exports.SET_ETH_SPEND = 'Input how much ETH to spend';
exports.SET_SOL_SPEND = 'Input how much SOL to spend';
