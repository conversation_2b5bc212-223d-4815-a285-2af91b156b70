/**
 * Callback Query Handler
 *
 * This module handles callback queries from Telegram bot inline keyboards.
 * It routes the queries to the appropriate handlers based on the command.
 */

import TelegramBot from 'node-telegram-bot-api';
import { logger } from '../logger/LoggerService';
import { BotService } from './BotService';
import { BlockchainType, TraderFactory } from '../trading/TradingInterface';
import User from '../../models/user.models';

// Import blockchain-specific handlers
import {
  handleBscDashboard,
  handleBSCScanContract,
  handleGenerateNewBscWallet,
  askForContractAddress as askForBscContractAddress,
  handleSpendBSC,
  sellBscTokenPercentage
} from '../bscscan.service';

import {
  handleEthDashboard,
  handleETHScanContract,
  handleGenerateNewethWallet,
  askForEthContractAddress,
  handleSpendeth,
  sellethTokenPercentage
} from '../ethscan.service';

import {
  handleBaseDashboard,
  handleBASEScanContract,
  handleGenerateNewBaseWallet,
  askForBaseContractAddress,
  handleSpendBase,
  sellBaseTokenPercentage
} from '../basescan.service';

import {
  handleSolDashboard,
  handleSOLScanContract,
  handleGenerateNewSolWallet,
  askForSolContractAddress,
  handleSpendsol,
  sellSolTokenPercentage
} from '../solscan.service';

// Import screens
import { showConfigKeyboard } from '../../screens/config';
import { bscSniperScreen } from '../../screens/sniperboards/bscboard';
import { ethSniperScreen } from '../../screens/sniperboards/ethboard';
import { baseSniperScreen } from '../../screens/sniperboards/baseboard';
import { solSniperScreen } from '../../screens/sniperboards/solboard';

// Use trading factory for buying tokens

// Import global state
import { getCurrentContractAddress } from '../../globalState';

/**
 * Handle a callback query
 * @param bot The Telegram bot instance
 * @param query The callback query
 */
export async function handleCallbackQuery(
  bot: TelegramBot,
  query: TelegramBot.CallbackQuery
): Promise<void> {
  try {
    const chatId = query.message?.chat.id;
    const messageId = query.message?.message_id;

    if (!chatId || !messageId) {
      logger.warn('Invalid callback query: missing chat ID or message ID');
      return;
    }

    // Create a bot service instance
    const botService = new BotService(bot);

    // Parse the callback data
    const data = JSON.parse(query.data || '{}');
    const command = data.command;

    logger.debug(`Processing callback query: ${command}`, { chatId, messageId });

    // Handle the command
    switch (command) {
      case 'dismiss_message':
        await handleDismissMessage(botService, chatId, messageId);
        break;

      case 'show_config':
        await showConfigKeyboard(bot, chatId);
        break;

      case 'view_bsc':
      case 'view_base':
      case 'view_sol':
      case 'view_eth':
        await handleBlockchainView(bot, chatId, command);
        break;

      case 'generate_new_bsc_wallet':
        await handleGenerateNewBscWallet(bot, chatId);
        break;

      case 'generate_new_eth_wallet':
        await handleGenerateNewethWallet(bot, chatId);
        break;

      case 'generate_new_base_wallet':
        await handleGenerateNewBaseWallet(bot, chatId);
        break;

      case 'generate_new_sol_wallet':
        await handleGenerateNewSolWallet(bot, chatId);
        break;

      case 'scan_contract_bsc':
        const bscAddress = await askForBscContractAddress(bot, chatId, messageId);
        if (bscAddress) {
          await handleBSCScanContract(bot, chatId, bscAddress);
        } else {
          await botService.sendMessage(chatId, 'Invalid contract address.');
        }
        break;

      case 'scan_contract_eth':
        const ethAddress = await askForEthContractAddress(bot, chatId, messageId);
        if (ethAddress) {
          await handleETHScanContract(bot, chatId, ethAddress);
        } else {
          await botService.sendMessage(chatId, 'Invalid contract address.');
        }
        break;

      case 'scan_contract_base':
        const baseAddress = await askForBaseContractAddress(bot, chatId, messageId);
        if (baseAddress) {
          await handleBASEScanContract(bot, chatId, baseAddress);
        } else {
          await botService.sendMessage(chatId, 'Invalid contract address.');
        }
        break;

      case 'scan_contract_sol':
        const solAddress = await askForSolContractAddress(bot, chatId, messageId);
        if (solAddress) {
          await handleSOLScanContract(bot, chatId, solAddress);
        } else {
          await botService.sendMessage(chatId, 'Invalid contract address.');
        }
        break;

      case 'scan_snipe_bsc':
        try {
          const bscAddressToSnipe = await askForBscContractAddress(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'bsc' } });

          if (bscAddressToSnipe) {
            await bscSniperScreen(bot, chatId, bscAddressToSnipe);
          } else {
            await botService.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          logger.error('Error during BSC token snipe:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'scan_snipe_eth':
        try {
          const ethAddressToSnipe = await askForEthContractAddress(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'eth' } });

          if (ethAddressToSnipe) {
            await ethSniperScreen(bot, chatId, ethAddressToSnipe);
          } else {
            await botService.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          logger.error('Error during ETH token snipe:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'scan_snipe_base':
        try {
          const baseAddressToSnipe = await askForBaseContractAddress(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'base' } });

          if (baseAddressToSnipe) {
            await baseSniperScreen(bot, chatId, baseAddressToSnipe);
          } else {
            await botService.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          logger.error('Error during BASE token snipe:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'scan_snipe_sol':
        try {
          const solAddressToSnipe = await askForSolContractAddress(bot, chatId, messageId);
          await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'sol' } });

          if (solAddressToSnipe) {
            await solSniperScreen(bot, chatId, solAddressToSnipe);
          } else {
            await botService.sendMessage(chatId, 'Invalid contract address.');
          }
        } catch (error) {
          logger.error('Error during SOL token snipe:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'spend_bsc':
        try {
          const spendAmount = await handleSpendBSC(bot, chatId);
          if (spendAmount) {
            // Get the current contract address from global state
            const contractAddress = getCurrentContractAddress(BlockchainType.BSC);
            if (!contractAddress) {
              await botService.sendMessage(chatId, 'No contract address selected. Please select a token first.');
              break;
            }

            // Get user wallet address
            const user = await User.findOne({ chat_id: chatId });
            if (!user || !user.bsc_wallet || !user.bsc_wallet.address) {
              await botService.sendMessage(chatId, 'No wallet found. Please generate a wallet first.');
              break;
            }

            const walletAddress = user.bsc_wallet.address;

            // Get the trader and execute the buy
            const trader = TraderFactory.getTrader(BlockchainType.BSC);
            const receipt = await trader.buyTokens(contractAddress, spendAmount, walletAddress);

            if (receipt && receipt.transactionHash && receipt.blockNumber) {
              const { transactionHash, blockNumber } = receipt;
              await botService.sendMessage(
                chatId,
                `✅ Tokens successfully purchased:\n\n` +
                `🔗 Transaction Hash: ${transactionHash}\n` +
                `🧱 Block Number: ${blockNumber}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://bscscan.com/tx/${transactionHash}\nTo see your transaction`
              );
            } else {
              await botService.sendMessage(chatId, `Error: Unable to get transaction details.`);
            }
          } else {
            await botService.sendMessage(chatId, 'Invalid spend amount.');
          }
        } catch (error) {
          logger.error('Error purchasing BSC tokens:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'spend_eth':
        try {
          const spendAmount = await handleSpendeth(bot, chatId);
          if (spendAmount) {
            // Get the current contract address from global state
            const contractAddress = getCurrentContractAddress(BlockchainType.ETH);
            if (!contractAddress) {
              await botService.sendMessage(chatId, 'No contract address selected. Please select a token first.');
              break;
            }

            // Get user wallet address
            const user = await User.findOne({ chat_id: chatId });
            if (!user || !user.eth_wallet || !user.eth_wallet.address) {
              await botService.sendMessage(chatId, 'No wallet found. Please generate a wallet first.');
              break;
            }

            const walletAddress = user.eth_wallet.address;

            // Get the trader and execute the buy
            const trader = TraderFactory.getTrader(BlockchainType.ETH);
            const receipt = await trader.buyTokens(contractAddress, spendAmount, walletAddress);

            if (receipt && receipt.transactionHash && receipt.blockNumber) {
              const { transactionHash, blockNumber } = receipt;
              await botService.sendMessage(
                chatId,
                `✅ Tokens successfully purchased:\n\n` +
                `🔗 Transaction Hash: ${transactionHash}\n` +
                `🧱 Block Number: ${blockNumber}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://etherscan.io/tx/${transactionHash}\nTo see your transaction`
              );
            } else {
              await botService.sendMessage(chatId, `Error: Unable to get transaction details.`);
            }
          } else {
            await botService.sendMessage(chatId, 'Invalid spend amount.');
          }
        } catch (error) {
          logger.error('Error purchasing ETH tokens:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'spend_base':
        try {
          const spendAmount = await handleSpendBase(bot, chatId);
          if (spendAmount) {
            // Get the current contract address from global state
            const contractAddress = getCurrentContractAddress(BlockchainType.BASE);
            if (!contractAddress) {
              await botService.sendMessage(chatId, 'No contract address selected. Please select a token first.');
              break;
            }

            // Get user wallet address
            const user = await User.findOne({ chat_id: chatId });
            if (!user || !user.base_wallet || !user.base_wallet.address) {
              await botService.sendMessage(chatId, 'No wallet found. Please generate a wallet first.');
              break;
            }

            const walletAddress = user.base_wallet.address;

            // Get the trader and execute the buy
            const trader = TraderFactory.getTrader(BlockchainType.BASE);
            const receipt = await trader.buyTokens(contractAddress, spendAmount, walletAddress);

            if (receipt && receipt.transactionHash && receipt.blockNumber) {
              const { transactionHash, blockNumber } = receipt;
              await botService.sendMessage(
                chatId,
                `✅ Tokens successfully purchased:\n\n` +
                `🔗 Transaction Hash: ${transactionHash}\n` +
                `🧱 Block Number: ${blockNumber}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${transactionHash}\nTo see your transaction`
              );
            } else {
              await botService.sendMessage(chatId, `Error: Unable to get transaction details.`);
            }
          } else {
            await botService.sendMessage(chatId, 'Invalid spend amount.');
          }
        } catch (error) {
          logger.error('Error purchasing BASE tokens:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'spend_sol':
        try {
          const spendAmount = await handleSpendsol(bot, chatId);
          if (spendAmount) {
            // Get the current contract address from global state
            const contractAddress = getCurrentContractAddress(BlockchainType.SOL);
            if (!contractAddress) {
              await botService.sendMessage(chatId, 'No contract address selected. Please select a token first.');
              break;
            }

            // Get user wallet address
            const user = await User.findOne({ chat_id: chatId });
            if (!user || !user.sol_wallet || !user.sol_wallet.address) {
              await botService.sendMessage(chatId, 'No wallet found. Please generate a wallet first.');
              break;
            }

            const walletAddress = user.sol_wallet.address;

            // Get the trader and execute the buy
            const trader = TraderFactory.getTrader(BlockchainType.SOL);
            const receipt = await trader.buyTokens(contractAddress, spendAmount, walletAddress);

            if (receipt && receipt.transactionHash) {
              const { transactionHash } = receipt;
              await botService.sendMessage(
                chatId,
                `✅ Tokens successfully purchased:\n\n` +
                `🔗 Transaction Hash: ${transactionHash}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://solscan.io/tx/${transactionHash}\nTo see your transaction`
              );
            } else {
              await botService.sendMessage(chatId, `Error: Unable to get transaction details.`);
            }
          } else {
            await botService.sendMessage(chatId, 'Invalid spend amount.');
          }
        } catch (error) {
          logger.error('Error purchasing SOL tokens:', {
            error: (error as Error).message,
            chatId
          });
          await botService.sendMessage(
            chatId,
            `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
          );
        }
        break;

      case 'main_menu':
        await showMainMenu(botService, chatId);
        break;

      default:
        logger.warn(`Unknown callback command: ${command}`, { chatId });
        await botService.sendMessage(chatId, 'Unknown command. Please try again.');
    }

    // Answer the callback query to remove the loading indicator
    await bot.answerCallbackQuery(query.id);

  } catch (error) {
    // Check if the error is because the query is too old
    if ((error as Error).message.includes('query is too old') ||
        (error as Error).message.includes('query ID is invalid')) {
      logger.warn('Callback query expired or invalid:', {
        query: query.data,
        queryId: query.id
      });

      // If we have chat ID, send a message to inform the user
      if (query.message?.chat.id) {
        try {
          const botService = new BotService(bot);
          await botService.sendMessage(
            query.message.chat.id,
            'This action has expired. Please try again with a new command.',
            { reply_to_message_id: query.message.message_id }
          );
        } catch (msgError) {
          // Just log and continue if we can't send a message
          logger.debug('Could not send expired query message', {
            error: (msgError as Error).message
          });
        }
      }
    } else {
      // Log other errors
      logger.error('Error handling callback query:', {
        error: (error as Error).message,
        query: query.data
      });

      try {
        // Try to answer the callback query even if there was an error
        await bot.answerCallbackQuery(query.id, {
          text: 'An error occurred. Please try again.',
          show_alert: true
        });
      } catch (answerError) {
        // Check if this is also a "query is too old" error
        if ((answerError as Error).message.includes('query is too old') ||
            (answerError as Error).message.includes('query ID is invalid')) {
          logger.debug('Could not answer expired callback query', {
            queryId: query.id
          });
        } else {
          logger.error('Error answering callback query:', {
            error: (answerError as Error).message,
            queryId: query.id
          });
        }
      }
    }
  }
}

/**
 * Handle dismissing a message
 * @param botService The bot service
 * @param chatId The chat ID
 * @param messageId The message ID
 */
async function handleDismissMessage(
  botService: BotService,
  chatId: number,
  messageId: number
): Promise<void> {
  try {
    await botService.deleteMessage(chatId, messageId);
  } catch (error) {
    logger.error('Error dismissing message:', {
      error: (error as Error).message,
      chatId,
      messageId
    });
  }
}

/**
 * Handle blockchain view
 * @param bot The Telegram bot instance
 * @param chatId The chat ID
 * @param command The command
 */
async function handleBlockchainView(
  bot: TelegramBot,
  chatId: number,
  command: string
): Promise<void> {
  const selectedBlockchain = command.split('_')[1];

  try {
    await User.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: selectedBlockchain } });

    switch (selectedBlockchain) {
      case 'bsc':
        await handleBscDashboard(bot, chatId);
        break;
      case 'base':
        await handleBaseDashboard(bot, chatId);
        break;
      case 'eth':
        await handleEthDashboard(bot, chatId);
        break;
      case 'sol':
        await handleSolDashboard(bot, chatId);
        break;
      default:
        logger.warn(`Unknown blockchain: ${selectedBlockchain}`, { chatId });
        await bot.sendMessage(chatId, 'Unknown blockchain. Please try again.');
    }
  } catch (error) {
    logger.error(`Error handling ${selectedBlockchain} view:`, {
      error: (error as Error).message,
      chatId
    });
    await bot.sendMessage(
      chatId,
      `⚠️ Error: ${error instanceof Error ? error.message : 'Unknown error occurred.'}`
    );
  }
}

/**
 * Show the main menu
 * @param botService The bot service
 * @param chatId The chat ID
 */
async function showMainMenu(botService: BotService, chatId: number): Promise<void> {
  try {
    const keyboard = botService.createBlockchainKeyboard();

    await botService.sendMessage(
      chatId,
      '🏠 *Main Menu*\n\nSelect a blockchain to continue:',
      {
        parse_mode: 'Markdown',
        reply_markup: keyboard
      }
    );
  } catch (error) {
    logger.error('Error showing main menu:', {
      error: (error as Error).message,
      chatId
    });
  }
}
