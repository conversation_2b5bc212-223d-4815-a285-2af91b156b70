"use strict";
// V0 USING LEGACY TRANSACTION
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sellSolToken = void 0;
exports.getUserSettings = getUserSettings;
const web3_js_1 = require("@solana/web3.js");
const spl_token_1 = require("@solana/spl-token");
const cross_fetch_1 = __importDefault(require("cross-fetch"));
const bs58_1 = __importDefault(require("bs58"));
const user_models_1 = __importDefault(require("../models/user.models"));
const globalState_1 = require("../globalState");
const RPC_ENDPOINT = process.env.SOL_PROVIDER_URL || '';
const ADMIN_PUBLIC_KEY_STRING = process.env.SOL_ADMIN_PUBLIC_KEY || '';
const SOLANA_ADDRESS = 'So11111111111111111111111111111111111111112';
if (!RPC_ENDPOINT || !ADMIN_PUBLIC_KEY_STRING) {
    throw new Error('Environment variables SOL_PROVIDER_URL and ADMIN_PUBLIC_KEY must be set');
}
const connection = new web3_js_1.Connection(RPC_ENDPOINT);
function getUserSettings(chatId) {
    return __awaiter(this, void 0, void 0, function* () {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }
        const solConfig = user.sol_config;
        if (!solConfig) {
            throw new Error('SOL configuration is not set for the user');
        }
        const { slippage } = solConfig;
        const { private_key, address } = user.sol_wallet;
        return { slippage, private_key, address };
    });
}
const convertToInteger = (amount, decimals) => {
    return Math.floor(amount * Math.pow(10, decimals));
};
const getQuote = (inputMint, outputMint, amount, slippageBps) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const url = `https://quote-api.jup.ag/v6/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}`;
        const response = yield (0, cross_fetch_1.default)(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch quote: ${response.statusText}`);
        }
        const quoteResponse = yield response.json();
        return quoteResponse;
    }
    catch (error) {
        console.error("Error fetching quote:", error);
        throw error;
    }
});
const getSwapTransaction = (quoteResponse, walletPublicKey) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const body = {
            quoteResponse,
            userPublicKey: walletPublicKey,
            wrapAndUnwrapSol: true,
            restrictIntermediateTokens: false,
            prioritizationFeeLamports: 2000000, // Set to 0.002 SOL to bribe the miners
            autoMultiplier: 2,
        };
        const response = yield (0, cross_fetch_1.default)("https://quote-api.jup.ag/v6/swap", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(body),
        });
        if (!response.ok) {
            throw new Error(`Failed to fetch swap transaction: ${response.statusText}`);
        }
        const swapResponse = yield response.json();
        return swapResponse.swapTransaction;
    }
    catch (error) {
        console.error("Error fetching swap transaction:", error);
        throw error;
    }
});
function executeTransaction(transaction, connection) {
    return __awaiter(this, void 0, void 0, function* () {
        let tryAgain = true;
        let txid;
        let maxTriesCounter = 0;
        const maxTries = 15;
        const baseRetryDelay = 1500; // Base delay of 1 seconds
        while (tryAgain) {
            try {
                maxTriesCounter++;
                const rawTransaction = transaction.serialize();
                txid = yield connection.sendRawTransaction(rawTransaction, {
                    skipPreflight: true,
                    maxRetries: 2,
                });
                yield new Promise(r => setTimeout(r, baseRetryDelay * maxTriesCounter)); // Exponential backoff
                const result = yield connection.getSignatureStatus(txid, {
                    searchTransactionHistory: true,
                });
                if ((result === null || result === void 0 ? void 0 : result.value) !== null) {
                    tryAgain = false;
                }
                if (maxTriesCounter >= maxTries) {
                    tryAgain = false;
                    throw new Error('Max retries exceeded');
                }
            }
            catch (error) {
                console.error('Error during transaction execution:', error);
                if (maxTriesCounter >= maxTries) {
                    tryAgain = false; // Exit retry loop on error if max tries exceeded
                }
            }
        }
        if (!txid) {
            throw new Error('Transaction ID is undefined');
        }
        yield connection.confirmTransaction(txid);
        return txid;
    });
}
const sellSolToken = (chatId, amountOfTokenToSell) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    console.log(amountOfTokenToSell);
    try {
        if (!amountOfTokenToSell || amountOfTokenToSell <= 0) {
            throw new Error("Invalid amount of tokens to sell");
        }
        const { slippage, private_key, address } = yield getUserSettings(chatId);
        const slippageBps = slippage * 100;
        const secretKey = bs58_1.default.decode(private_key);
        const wallet = web3_js_1.Keypair.fromSecretKey(secretKey);
        const ADDRESS_OF_TOKEN_OUT = (_a = (0, globalState_1.getCurrentContractAddress)()) !== null && _a !== void 0 ? _a : '';
        console.log(ADDRESS_OF_TOKEN_OUT);
        const mint = yield connection.getParsedAccountInfo(new web3_js_1.PublicKey(ADDRESS_OF_TOKEN_OUT));
        if (!mint || !mint.value || mint.value.data instanceof Buffer) {
            throw new Error("Could not find mint");
        }
        const feeAmountInTokens = amountOfTokenToSell * 0.01; // 1% fee in token units
        const remainingTokens = amountOfTokenToSell - feeAmountInTokens;
        console.log(`Remaining tokens after fee: ${remainingTokens}`);
        console.log(`Fee amount in tokens: ${feeAmountInTokens}`);
        yield sendTokens(connection, wallet, ADDRESS_OF_TOKEN_OUT, feeAmountInTokens, ADMIN_PUBLIC_KEY_STRING);
        const decimals = mint.value.data.parsed.info.decimals;
        const convertedRemainingTokens = convertToInteger(remainingTokens, decimals);
        const quoteResponse = yield getQuote(ADDRESS_OF_TOKEN_OUT, SOLANA_ADDRESS, BigInt(convertedRemainingTokens), slippageBps);
        const walletPublicKey = wallet.publicKey.toString();
        const swapTransaction = yield getSwapTransaction(quoteResponse, walletPublicKey);
        const swapTransactionBuf = Buffer.from(swapTransaction, 'base64');
        const transaction = web3_js_1.VersionedTransaction.deserialize(swapTransactionBuf);
        transaction.sign([wallet]);
        const txid = yield executeTransaction(transaction, connection);
        // Update the trade as sold
        // await updateTradeAsSold(User._id, ADDRESS_OF_TOKEN_OUT, parseFloat(amountOfTokenToSell), 'sol');
        return txid;
    }
    catch (error) {
        console.error(`Error selling token: ${error}`);
        throw error;
    }
});
exports.sellSolToken = sellSolToken;
function getNumberDecimals(MINT_ADDRESS) {
    return __awaiter(this, void 0, void 0, function* () {
        const mintInfo = yield connection.getParsedAccountInfo(new web3_js_1.PublicKey(MINT_ADDRESS));
        if (!mintInfo.value || mintInfo.value.data instanceof Buffer) {
            throw new Error("Invalid mint address");
        }
        return mintInfo.value.data.parsed.info.decimals;
    });
}
function sendTokens(SOLANA_CONNECTION, FROM_KEYPAIR, MINT_ADDRESS, TRANSFER_AMOUNT, DESTINATION_WALLET) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            let sourceAccount = yield (0, spl_token_1.getOrCreateAssociatedTokenAccount)(SOLANA_CONNECTION, FROM_KEYPAIR, new web3_js_1.PublicKey(MINT_ADDRESS), FROM_KEYPAIR.publicKey);
            let destinationAccount = yield (0, spl_token_1.getOrCreateAssociatedTokenAccount)(SOLANA_CONNECTION, FROM_KEYPAIR, new web3_js_1.PublicKey(MINT_ADDRESS), new web3_js_1.PublicKey(DESTINATION_WALLET));
            const numberDecimals = yield getNumberDecimals(MINT_ADDRESS);
            const tx = new web3_js_1.Transaction();
            tx.add((0, spl_token_1.createTransferInstruction)(sourceAccount.address, destinationAccount.address, FROM_KEYPAIR.publicKey, convertToInteger(TRANSFER_AMOUNT, numberDecimals), [], spl_token_1.TOKEN_PROGRAM_ID));
            const latestBlockHash = yield SOLANA_CONNECTION.getLatestBlockhash("confirmed");
            tx.recentBlockhash = latestBlockHash.blockhash;
            const signature = yield SOLANA_CONNECTION.sendTransaction(tx, [FROM_KEYPAIR]);
            return signature;
        }
        catch (error) {
            console.error("Error sending tokens:", error);
            throw error;
        }
    });
}
module.exports = {
    sellSolToken: exports.sellSolToken,
    sendTokens,
};
// V1 USING JITO BUNDLER
// import bs58 from 'bs58';
// import { Keypair, Connection, VersionedTransaction, PublicKey } from "@solana/web3.js";
// import fetch from "cross-fetch";
// import { TOKEN_PROGRAM_ID, getOrCreateAssociatedTokenAccount, createTransferInstruction } from "@solana/spl-token";
// import { Transaction, SystemProgram } from "@solana/web3.js";
// import User from '../models/user.models';
// import { updateTradeAsSold } from '../models/trade.model';
// import { getCurrentContractAddress } from "../globalState";
// import { bundle } from '../utils/jito';  // Import only the bundle function
// const RPC_ENDPOINT = process.env.SOL_PROVIDER_URL || '';
// const ADMIN_PUBLIC_KEY_STRING = process.env.SOL_ADMIN_PUBLIC_KEY || '';
// const SOLANA_ADDRESS = 'So11111111111111111111111111111111111111112';
// const JITO_ENDPOINT = process.env.JITO_ENDPOINT || ''; // Jito endpoint
// const JITO_API_TOKEN = process.env.JITO_API_TOKEN || ''; // Jito API token
// if (!RPC_ENDPOINT || !ADMIN_PUBLIC_KEY_STRING || !JITO_ENDPOINT || !JITO_API_TOKEN) {
//   throw new Error('Environment variables SOL_PROVIDER_URL, ADMIN_PUBLIC_KEY, JITO_ENDPOINT, and JITO_API_TOKEN must be set');
// }
// const connection = new Connection(RPC_ENDPOINT);
// // Function to get a quote from Jupiter
// const getQuote = async (inputMint: string, outputMint: string, amount: bigint, slippageBps: number) => {
//   try {
//     const url = `https://quote-api.jup.ag/v6/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}`;
//     const response = await fetch(url);
//     if (!response.ok) {
//       throw new Error(`Failed to fetch quote: ${response.statusText}`);
//     }
//     const quoteResponse = await response.json();
//     return quoteResponse;
//   } catch (error) {
//     console.error("Error fetching quote:", error);
//     throw error;
//   }
// };
// // Function to create an admin fee transaction
// const createAdminFeeTransaction = async (wallet: Keypair, tokenAddress: string, feeAmount: number) => {
//   try {
//     const tx = new Transaction();
//     const sourceAccount = await getOrCreateAssociatedTokenAccount(connection, wallet, new PublicKey(tokenAddress), wallet.publicKey);
//     const adminPublicKey = new PublicKey(ADMIN_PUBLIC_KEY_STRING);
//     const destinationAccount = await getOrCreateAssociatedTokenAccount(connection, wallet, new PublicKey(tokenAddress), adminPublicKey);
//     tx.add(
//       createTransferInstruction(
//         sourceAccount.address,
//         destinationAccount.address,
//         wallet.publicKey,
//         convertToInteger(feeAmount, 6),
//         [],
//         TOKEN_PROGRAM_ID
//       )
//     );
//     tx.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
//     tx.feePayer = wallet.publicKey;
//     return tx;
//   } catch (error) {
//     console.error("Error creating admin fee transaction:", error);
//     throw error;
//   }
// };
// // Function to create a swap transaction
// const createSwapTransaction = async (wallet: Keypair, quoteResponse: any) => {
//   try {
//     const body = {
//       quoteResponse,
//       userPublicKey: wallet.publicKey.toBase58(),
//       wrapAndUnwrapSol: true,
//       restrictIntermediateTokens: false,
//       prioritizationFeeLamports: "auto",
//       autoMultiplier: 2,
//     };
//     const response = await fetch("https://quote-api.jup.ag/v6/swap", {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//       },
//       body: JSON.stringify(body),
//     });
//     if (!response.ok) {
//       throw new Error(`Failed to fetch swap transaction: ${response.statusText}`);
//     }
//     const swapResponse = await response.json();
//     return swapResponse.swapTransaction;
//   } catch (error) {
//     console.error("Error fetching swap transaction:", error);
//     throw error;
//   }
// };
// // Main function to handle the selling of SOL tokens
// export const sellSolToken = async (chatId: number, amountOfTokenToSell: number): Promise<string> => {
//   console.log(amountOfTokenToSell);
//   const user = await User.findOne({ chat_id: chatId }).exec();
//   if (!user) {
//     throw new Error("User not found");
//   }
//   try {
//     if (!amountOfTokenToSell || amountOfTokenToSell <= 0) {
//       throw new Error("Invalid amount of tokens to sell");
//     }
//     const { slippage, private_key, address, solCurrentTokenName, solCurrentContractAddress } = await getUserSettings(chatId);
//     console.log(slippage);
//     const slippageBps = slippage * 100;
//     const secretKey = bs58.decode(private_key);
//     const wallet = Keypair.fromSecretKey(secretKey);
//     const ADDRESS_OF_TOKEN_OUT = solCurrentContractAddress ?? '';
//     console.log(ADDRESS_OF_TOKEN_OUT);
//     const mint = await connection.getParsedAccountInfo(new PublicKey(ADDRESS_OF_TOKEN_OUT));
//     if (!mint || !mint.value || mint.value.data instanceof Buffer) {
//       throw new Error("Could not find mint");
//     }
//     const feeAmountInTokens = amountOfTokenToSell * 0.08; // 8% fee in token units
//     const remainingTokens = amountOfTokenToSell - feeAmountInTokens;
//     console.log(`Remaining tokens after fee: ${remainingTokens}`);
//     console.log(`Fee amount in tokens: ${feeAmountInTokens}`);
//     // 1. Get quote from Jupiter
//     const quoteResponse = await getQuote(ADDRESS_OF_TOKEN_OUT, SOLANA_ADDRESS, BigInt(remainingTokens), slippageBps);
//     // 2. Create transactions
//     const sendAdminFeeTx = await createAdminFeeTransaction(wallet, ADDRESS_OF_TOKEN_OUT, feeAmountInTokens);
//     const swapTransaction = await createSwapTransaction(wallet, quoteResponse);
//     const txs = [sendAdminFeeTx, swapTransaction];
//     // 3. Bundle and send transactions using Jito
//     const isSuccess = await bundle(txs, wallet);
//     if (!isSuccess) {
//       throw new Error('Failed to bundle and send transactions');
//     }
//     await updateTradeAsSold(user._id, ADDRESS_OF_TOKEN_OUT, amountOfTokenToSell, 'sol');
//     return swapTransaction.signatures[0].signature.toString();
//   } catch (error) {
//     console.error(`Error selling token: ${error}`);
//     throw error;
//   }
// };
// async function getUserSettings(chatId: number) {
//   const user = await User.findOne({ chat_id: chatId }).exec();
//   if (!user) {
//     throw new Error('User not found');
//   }
//   const solConfig = user.sol_config;
//   if (!solConfig) {
//     throw new Error('SOL configuration is not set for the user');
//   }
//   const { slippage } = solConfig;
//   const { private_key, address } = user.sol_wallet;
//   const solCurrentContractAddress = user.solCurrentContractAddress;
//   const solCurrentTokenName = user.solCurrentTokenName;
//   return { slippage, private_key, address, solCurrentContractAddress, solCurrentTokenName };
// }
// const convertToInteger = (amount: number, decimals: number) => {
//   return Math.floor(amount * 10 ** decimals);
// };
