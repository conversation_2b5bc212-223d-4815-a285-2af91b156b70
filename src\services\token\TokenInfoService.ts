/**
 * Token Information Service
 *
 * This service provides functionality to fetch token information
 * from various sources like Dexscreener, CoinGecko, etc.
 */

import axios from 'axios';
import { BlockchainType, TokenInfo } from '../trading/TradingInterface';
import { logger } from '../logger/LoggerService';
import { cacheService } from '../cache/RedisService';
import { checkIfHoneypot } from '../honeypot/UnifiedHoneypotChecker';

// Cache TTL in seconds (5 minutes)
const CACHE_TTL = 300;

/**
 * Dexscreener token info interface
 */
interface DexscreenerTokenInfo {
  name?: string;
  symbol?: string;
  priceUsd?: number;
  liquidity?: {
    usd?: number;
  };
  fdv?: number;
  volume?: {
    h24?: number;
  };
  priceChange?: {
    h1?: number;
    h24?: number;
  };
  pairAddress?: string;
  dexId?: string;
  chainId?: string;
}

/**
 * Get blockchain ID for Dexscreener API
 * @param blockchain The blockchain type
 * @returns The blockchain ID for Dexscreener
 */
function getDexscreenerChainId(blockchain: BlockchainType): string {
  switch (blockchain) {
    case BlockchainType.ETH:
      return 'ethereum';
    case BlockchainType.BSC:
      return 'bsc';
    case BlockchainType.SOL:
      return 'solana';
    case BlockchainType.BASE:
      return 'base';
    default:
      throw new Error(`Unsupported blockchain: ${blockchain}`);
  }
}

/**
 * Get token information from Dexscreener
 * @param tokenAddress The token address
 * @param blockchain The blockchain type
 * @returns Token information
 */
export async function getTokenInfoFromDexscreener(
  tokenAddress: string,
  blockchain: BlockchainType
): Promise<DexscreenerTokenInfo | null> {
  const startTime = Date.now();
  const cacheKey = `dexscreener:${blockchain}:${tokenAddress}`;

  try {
    // Check cache first
    const cachedResult = await cacheService.get<DexscreenerTokenInfo>(cacheKey);
    if (cachedResult) {
      logger.debug(`Using cached token info for ${tokenAddress} on ${blockchain}`, {
        tokenAddress,
        blockchain
      });
      return cachedResult;
    }

    const chainId = getDexscreenerChainId(blockchain);
    const url = `https://api.dexscreener.com/latest/dex/tokens/${tokenAddress}`;

    const response = await axios.get(url);
    const data = response.data;

    if (!data.pairs || data.pairs.length === 0) {
      logger.warn(`No pairs found for token ${tokenAddress} on ${blockchain}`, {
        tokenAddress,
        blockchain
      });
      return null;
    }

    // Find the pair for the specified blockchain
    const pair = data.pairs.find((p: any) => p.chainId === chainId);
    if (!pair) {
      logger.warn(`No pair found for token ${tokenAddress} on ${blockchain}`, {
        tokenAddress,
        blockchain,
        availableChains: data.pairs.map((p: any) => p.chainId)
      });
      return null;
    }

    const tokenInfo: DexscreenerTokenInfo = {
      name: pair.baseToken.name,
      symbol: pair.baseToken.symbol,
      priceUsd: parseFloat(pair.priceUsd || '0'),
      liquidity: {
        usd: pair.liquidity?.usd || 0
      },
      fdv: pair.fdv || 0,
      volume: {
        h24: pair.volume?.h24 || 0
      },
      priceChange: {
        h1: pair.priceChange?.h1 || 0,
        h24: pair.priceChange?.h24 || 0
      },
      pairAddress: pair.pairAddress,
      dexId: pair.dexId,
      chainId: pair.chainId
    };

    // Cache the result
    await cacheService.set(cacheKey, tokenInfo, CACHE_TTL);

    // Log performance
    logger.logPerformance('Dexscreener token info', startTime, {
      tokenAddress,
      blockchain
    });

    return tokenInfo;
  } catch (error: any) {
    logger.error(`Failed to get token info from Dexscreener for ${tokenAddress} on ${blockchain}`, {
      error: error.message,
      tokenAddress,
      blockchain
    });
    return null;
  }
}

/**
 * Liquidity information interface
 */
export interface LiquidityInfo {
  hasLiquidity: boolean;
  liquidityUsd: number;
  pairAddress?: string;
  dexId?: string;
  priceUsd?: number;
  volume24h?: number;
  priceChange?: {
    h1?: number;
    h24?: number;
  };
}

/**
 * Get liquidity information for a token
 * @param tokenAddress The token address
 * @param blockchain The blockchain type
 * @returns Liquidity information
 */
export async function getLiquidityInfo(
  tokenAddress: string,
  blockchain: BlockchainType
): Promise<LiquidityInfo> {
  const startTime = Date.now();
  const cacheKey = `liquidity:${blockchain}:${tokenAddress}`;

  try {
    // Check cache first
    const cachedResult = await cacheService.get<LiquidityInfo>(cacheKey);
    if (cachedResult) {
      logger.debug(`Using cached liquidity info for ${tokenAddress} on ${blockchain}`, {
        tokenAddress,
        blockchain
      });
      return cachedResult;
    }

    const tokenInfo = await getTokenInfoFromDexscreener(tokenAddress, blockchain);

    if (!tokenInfo) {
      const result = { hasLiquidity: false, liquidityUsd: 0 };
      await cacheService.set(cacheKey, result, CACHE_TTL);
      return result;
    }

    const liquidityUsd = tokenInfo.liquidity?.usd || 0;
    const hasLiquidity = liquidityUsd > 0;

    const result: LiquidityInfo = {
      hasLiquidity,
      liquidityUsd,
      pairAddress: tokenInfo.pairAddress,
      dexId: tokenInfo.dexId,
      priceUsd: tokenInfo.priceUsd,
      volume24h: tokenInfo.volume?.h24,
      priceChange: tokenInfo.priceChange
    };

    // Cache the result
    await cacheService.set(cacheKey, result, CACHE_TTL);

    // Log performance
    logger.logPerformance('Liquidity info', startTime, {
      tokenAddress,
      blockchain,
      hasLiquidity,
      liquidityUsd
    });

    return result;
  } catch (error: any) {
    logger.error(`Failed to get liquidity info for ${tokenAddress} on ${blockchain}`, {
      error: error.message,
      tokenAddress,
      blockchain
    });
    return { hasLiquidity: false, liquidityUsd: 0 };
  }
}

/**
 * Get comprehensive token information
 * @param tokenAddress The token address
 * @param blockchain The blockchain type
 * @returns Comprehensive token information
 */
export async function getComprehensiveTokenInfo(
  tokenAddress: string,
  blockchain: BlockchainType
): Promise<TokenInfo> {
  const startTime = Date.now();
  const cacheKey = `token:${blockchain}:${tokenAddress}`;

  try {
    // Check cache first
    const cachedResult = await cacheService.get<TokenInfo>(cacheKey);
    if (cachedResult) {
      logger.debug(`Using cached comprehensive token info for ${tokenAddress} on ${blockchain}`, {
        tokenAddress,
        blockchain
      });
      return cachedResult;
    }

    // Get token info from Dexscreener
    const dexInfo = await getTokenInfoFromDexscreener(tokenAddress, blockchain);

    // Check if token is a honeypot
    const honeypotInfo = await checkIfHoneypot(tokenAddress, blockchain);

    // Combine the information
    const tokenInfo: TokenInfo = {
      name: dexInfo?.name || 'Unknown',
      symbol: dexInfo?.symbol || 'UNKNOWN',
      decimals: 18, // Default, would be overridden by blockchain-specific logic
      address: tokenAddress,
      blockchain,
      price: dexInfo?.priceUsd || 0,
      marketCap: dexInfo?.fdv || 0,
      isHoneypot: honeypotInfo.isHoneypot,
      honeypotInfo
    };

    // Cache the result
    await cacheService.set(cacheKey, tokenInfo, CACHE_TTL);

    // Log performance
    logger.logPerformance('Comprehensive token info', startTime, {
      tokenAddress,
      blockchain
    });

    return tokenInfo;
  } catch (error: any) {
    logger.error(`Failed to get comprehensive token info for ${tokenAddress} on ${blockchain}`, {
      error: error.message,
      tokenAddress,
      blockchain
    });

    // Return minimal information
    return {
      name: 'Unknown',
      symbol: 'UNKNOWN',
      decimals: 18,
      address: tokenAddress,
      blockchain
    };
  }
}

/**
 * Format token information as a human-readable message
 * @param tokenInfo The token information
 * @returns A formatted message
 */
export function formatTokenInfo(tokenInfo: TokenInfo): string {
  let message = '';

  message += `**Token Information**\n\n`;
  message += `**Name:** ${tokenInfo.name}\n`;
  message += `**Symbol:** ${tokenInfo.symbol}\n`;
  message += `**Address:** \`${tokenInfo.address}\`\n`;
  message += `**Blockchain:** ${tokenInfo.blockchain}\n`;

  if (tokenInfo.price) {
    message += `**Price:** $${tokenInfo.price.toFixed(8)}\n`;
  }

  if (tokenInfo.marketCap) {
    message += `**Market Cap:** $${tokenInfo.marketCap.toLocaleString()}\n`;
  }

  if (tokenInfo.isHoneypot !== undefined) {
    message += `**Honeypot:** ${tokenInfo.isHoneypot ? '🚨 YES' : '✅ NO'}\n`;
  }

  return message;
}
