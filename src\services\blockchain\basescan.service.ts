/**
 * BASE Scan Service
 * 
 * This service provides functionality for interacting with the Base blockchain
 * and fetching data from Basescan.
 */

import { ethers } from 'ethers';
import axios from 'axios';
import { logger } from '../logger/LoggerService';
import { BlockchainType, TraderFactory } from '../trading/TradingInterface';

// BASE configuration
const BASE_RPC_URL = process.env.BASE_PROVIDER_URL || 'https://mainnet.base.org';
const BASE_SCAN_API_KEY = process.env.BASE_SCAN_API_KEY || '';
const BASE_SCAN_API_URL = 'https://api.basescan.org/api';

// Create provider
const provider = new ethers.providers.JsonRpcProvider(BASE_RPC_URL);

/**
 * Get token information from Basescan
 * @param contractAddress Token contract address
 * @returns Token information
 */
export async function getTokenInfo(contractAddress: string) {
  try {
    logger.info(`Getting token info for ${contractAddress} from Basescan`, {
      contractAddress
    });

    // Get token name and symbol
    const tokenContract = new ethers.Contract(
      contractAddress,
      [
        'function name() view returns (string)',
        'function symbol() view returns (string)',
        'function decimals() view returns (uint8)',
        'function totalSupply() view returns (uint256)'
      ],
      provider
    );

    const [name, symbol, decimals, totalSupply] = await Promise.all([
      tokenContract.name(),
      tokenContract.symbol(),
      tokenContract.decimals(),
      tokenContract.totalSupply()
    ]);

    // Format total supply
    const formattedTotalSupply = ethers.utils.formatUnits(totalSupply, decimals);

    logger.info(`Token info retrieved for ${contractAddress}`, {
      contractAddress,
      name,
      symbol,
      decimals,
      totalSupply: formattedTotalSupply
    });

    return {
      name,
      symbol,
      decimals,
      totalSupply: formattedTotalSupply
    };
  } catch (error) {
    logger.error(`Error getting token info for ${contractAddress}`, {
      contractAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Get token balance for a wallet
 * @param contractAddress Token contract address
 * @param walletAddress Wallet address
 * @returns Token balance
 */
export async function getTokenBalance(contractAddress: string, walletAddress: string) {
  try {
    logger.info(`Getting token balance for ${walletAddress}`, {
      contractAddress,
      walletAddress
    });

    // Get token decimals
    const tokenContract = new ethers.Contract(
      contractAddress,
      [
        'function balanceOf(address) view returns (uint256)',
        'function decimals() view returns (uint8)'
      ],
      provider
    );

    const [balance, decimals] = await Promise.all([
      tokenContract.balanceOf(walletAddress),
      tokenContract.decimals()
    ]);

    // Format balance
    const formattedBalance = ethers.utils.formatUnits(balance, decimals);

    logger.info(`Token balance retrieved for ${walletAddress}`, {
      contractAddress,
      walletAddress,
      balance: formattedBalance
    });

    return formattedBalance;
  } catch (error) {
    logger.error(`Error getting token balance for ${walletAddress}`, {
      contractAddress,
      walletAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Get BASE balance for a wallet
 * @param walletAddress Wallet address
 * @returns BASE balance
 */
export async function getBaseBalance(walletAddress: string) {
  try {
    logger.info(`Getting BASE balance for ${walletAddress}`, {
      walletAddress
    });

    // Get BASE balance
    const balance = await provider.getBalance(walletAddress);
    
    // Format balance
    const formattedBalance = ethers.utils.formatEther(balance);

    logger.info(`BASE balance retrieved for ${walletAddress}`, {
      walletAddress,
      balance: formattedBalance
    });

    return formattedBalance;
  } catch (error) {
    logger.error(`Error getting BASE balance for ${walletAddress}`, {
      walletAddress,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Get transaction receipt from Basescan
 * @param txHash Transaction hash
 * @returns Transaction receipt
 */
export async function getTransactionReceipt(txHash: string) {
  try {
    logger.info(`Getting transaction receipt for ${txHash}`, {
      txHash
    });

    // Get transaction receipt
    const receipt = await provider.getTransactionReceipt(txHash);

    logger.info(`Transaction receipt retrieved for ${txHash}`, {
      txHash,
      status: receipt.status
    });

    return receipt;
  } catch (error) {
    logger.error(`Error getting transaction receipt for ${txHash}`, {
      txHash,
      error: (error as Error).message
    });
    
    throw error;
  }
}

/**
 * Check if a token is a honeypot
 * @param contractAddress Token contract address
 * @returns Honeypot check result
 */
export async function checkHoneypot(contractAddress: string) {
  try {
    logger.info(`Checking if ${contractAddress} is a honeypot`, {
      contractAddress
    });

    // Use the trader to check if the token is a honeypot
    const trader = TraderFactory.getTrader(BlockchainType.BASE);
    const result = await trader.checkHoneypot(contractAddress);

    logger.info(`Honeypot check completed for ${contractAddress}`, {
      contractAddress,
      isHoneypot: result.isHoneypot,
      reason: result.honeypotReason
    });

    return result;
  } catch (error) {
    logger.error(`Error checking honeypot for ${contractAddress}`, {
      contractAddress,
      error: (error as Error).message
    });
    
    return {
      isHoneypot: false,
      honeypotReason: `Error checking honeypot: ${(error as Error).message}`
    };
  }
}
