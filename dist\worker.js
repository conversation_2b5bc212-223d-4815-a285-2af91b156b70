(()=>{"use strict";var e={6418:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{d(n.next(e))}catch(e){o(e)}}function s(e){try{d(n.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}d((n=n.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.buyBaseTokens=function(e,t){return n(this,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chat_id:e}).exec();if(!r)throw new Error("User not found");const a=yield i.default.findOne({chatId:e,contractAddress:t,blockchain:"base"}).exec();if(!a)throw new Error("Snipe configuration not found");const{private_key:f,address:y}=r.base_wallet,h=r.baseCurrentTokenName,m=a.config.spend_base,_=a.config.slippage_base;if(null==m||isNaN(m))throw new Error("Invalid spend_base value");if(null==_||isNaN(_))throw new Error("Invalid slippage_base value");const b=t,v=new o.providers.JsonRpcProvider(c),g=new o.Wallet(f,v);let w=`buyToken=${b}&sellToken=******************************************&sellAmount=${o.ethers.utils.parseEther(m.toString())}&slippagePercentage=${_/100}&takerAddress=${y}&feeRecipient=${l}&buyTokenPercentageFee=0.8`;const S=`${process.env.BASENODE_API_URL}?${w}`,T=yield function(e){return n(this,void 0,void 0,(function*(){return(yield u.default.get(e,{headers:p})).data}))}(S),N={to:T.to,data:T.data,value:o.ethers.BigNumber.from(T.value),gasLimit:o.ethers.BigNumber.from(1e6),gasPrice:o.ethers.utils.parseUnits("0.05","gwei")},k=yield g.sendTransaction(N),E=yield k.wait(),x=yield d.default.findOne({userId:r._id,contractAddress:b,isSold:!1}).exec();if(x)x.buyAmount+=parseFloat(m.toString()),yield x.save();else{const t=new d.default({userId:r._id,chatId:e,walletId:r.base_wallet.address,contractAddress:b,contractName:h,buyAmount:parseFloat(m.toString()),sellAmount:0,takeProfit:0,stopLoss:0,isSold:!1,blockchain:"base"});yield t.save()}return yield s.default.updateOne({_id:r._id},{$push:{"trades.base":x?x._id:r._id}}),E}catch(e){throw console.error("Error purchasing tokens:",e),e}}))};const o=r(9321),i=a(r(8087)),s=a(r(8722)),d=a(r(6786)),u=a(r(8938));a(r(818)).default.config();const l=process.env.EVM_ADMIN_ADDRESS||"",c=process.env.BASE_PROVIDER_URL;if(!l)throw new Error("ADMIN_ADDRESS environment variable is not set");if(!c)throw new Error("BASE_PROVIDER_URL environment variable is not set");const p={"0x-api-key":process.env.MASTER_KEY||""}},159:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{d(n.next(e))}catch(e){o(e)}}function s(e){try{d(n.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}d((n=n.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.buySolTokens=function(e,t){return n(this,void 0,void 0,(function*(){try{const r=yield u.default.findOne({chat_id:e}).exec();if(!r)throw new Error("User not found");const a=yield d.default.findOne({chatId:e,contractAddress:t,blockchain:"sol"}).exec();if(!a)throw new Error("Snipe configuration not found");const{private_key:c,address:h}=r.sol_wallet,m=r.solCurrentTokenName,_=a.config.spend_sol,b=a.config.slippage_sol;if(null==_||isNaN(_))throw new Error("Invalid spend_sol value");if(null==b||isNaN(b))throw new Error("Invalid slippage_sol value");const v=f,g=t;if(!g)throw new Error("Output mint address is not defined");const w=100*b,S=s.default.decode(c),T=o.Keypair.fromSecretKey(S),N=BigInt(Math.round(1e9*_)),k=.01*_,E=BigInt(Math.round(1e9*k)),x=yield function(e,t,r,a){return n(this,void 0,void 0,(function*(){const n=yield(0,i.default)(`https://quote-api.jup.ag/v6/quote?inputMint=${e}&outputMint=${t}&amount=${r}&slippageBps=${a}`),o=yield n.json();if(!o)throw new Error("Failed to get quote data");return o}))}(v,g,N,w),A=yield function(e,t){return n(this,void 0,void 0,(function*(){const r=yield(0,i.default)("https://quote-api.jup.ag/v6/swap",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({quoteResponse:e,userPublicKey:t,wrapAndUnwrapSol:!0,dynamicComputeUnitLimit:!0,prioritizationFeeLamports:2e6,autoMultiplier:2})}),n=yield r.json();if(!n||!n.swapTransaction)throw new Error("Failed to get swap transaction data");return n.swapTransaction}))}(x,T.publicKey.toString()),D=Buffer.from(A,"base64"),O=o.VersionedTransaction.deserialize(D),P=o.SystemProgram.transfer({fromPubkey:T.publicKey,toPubkey:new o.PublicKey(p),lamports:E}),I=yield Promise.all(O.message.addressTableLookups.map((e=>n(this,void 0,void 0,(function*(){const t=yield y.getAccountInfo(e.accountKey);if(!t)throw new Error(`Account info not found for key: ${e.accountKey.toBase58()}`);return new o.AddressLookupTableAccount({key:e.accountKey,state:o.AddressLookupTableAccount.deserialize(t.data)})})))));let B=o.TransactionMessage.decompile(O.message,{addressLookupTableAccounts:I});B.instructions.push(P),O.message=B.compileToV0Message(I),O.sign([T]);const M=yield function(e,t){return n(this,void 0,void 0,(function*(){var r;let n,a=!0;let o=0;for(;a&&o<10;)try{o++;const i=e.serialize();n=yield t.sendRawTransaction(i,{skipPreflight:!0,maxRetries:2}),yield new Promise((e=>setTimeout(e,1e3)));const s=yield t.getSignatureStatus(n,{searchTransactionHistory:!0});if(null===(null===(r=null==s?void 0:s.value)||void 0===r?void 0:r.err))a=!1;else if(o>=10)throw a=!1,new Error("Max retries exceeded")}catch(e){if(console.error("Error during transaction execution:",e),o>=10)throw new Error("Max retries exceeded")}if(!n)throw new Error("Transaction ID is undefined");return yield t.confirmTransaction(n),n}))}(O,y),L=yield l.default.findOne({userId:r._id,contractAddress:g,isSold:!1}).exec();if(L)L.buyAmount+=_,yield L.save();else{const t=new l.default({userId:r._id,chatId:e,walletId:r.sol_wallet.address,contractAddress:g,contractName:m||"---",buyAmount:_,sellAmount:0,takeProfit:0,stopLoss:0,isSold:!1});yield t.save()}return yield u.default.updateOne({_id:r._id},{$push:{trades:L?L._id:r._id}}),M}catch(e){throw console.error("Error during token purchase:",e),e}}))};const o=r(8491),i=a(r(6713)),s=a(r(8574)),d=a(r(8087)),u=a(r(8722)),l=a(r(6786));a(r(818)).default.config();const c=process.env.SOL_PROVIDER_URL||"",p=process.env.SOL_ADMIN_PUBLIC_KEY||"",f="So11111111111111111111111111111111111111112";if(!c||!p)throw new Error("Environment variables SOL_PROVIDER_URL and ADMIN_PUBLIC_KEY must be set");const y=new o.Connection(c)},2313:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{d(n.next(e))}catch(e){o(e)}}function s(e){try{d(n.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}d((n=n.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.buyBscTokens=function(e,t){return n(this,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chat_id:e}).exec();if(!r)throw new Error("User not found");const a=yield i.default.findOne({chatId:e,contractAddress:t,blockchain:"bsc"}).exec();if(!a)throw new Error("Snipe configuration not found");const{private_key:f,address:y}=r.bsc_wallet,h=r.bscCurrentTokenName,m=a.config.spend_bsc,_=a.config.slippage_bsc;if(null==m||isNaN(m))throw new Error("Invalid spend_bsc value");if(null==_||isNaN(_))throw new Error("Invalid slippage_bsc value");const b=t,v=new o.providers.JsonRpcProvider(c),g=new o.Wallet(f,v);let w=`buyToken=${b}&sellToken=******************************************&sellAmount=${o.ethers.utils.parseEther(m.toString())}&slippagePercentage=${_/100}&takerAddress=${y}&feeRecipient=${l}&buyTokenPercentageFee=0.8`;const S=`${process.env.BSCNODE_API_URL}?${w}`,T=yield function(e){return n(this,void 0,void 0,(function*(){return(yield u.default.get(e,{headers:p})).data}))}(S),N={to:T.to,data:T.data,value:o.ethers.BigNumber.from(T.value),gasLimit:o.ethers.BigNumber.from(1e6),gasPrice:o.ethers.utils.parseUnits("1.5","gwei")},k=yield g.sendTransaction(N),E=yield k.wait(),x=yield d.default.findOne({userId:r._id,contractAddress:b,isSold:!1}).exec();if(x)x.buyAmount+=parseFloat(m.toString()),yield x.save();else{const t=new d.default({userId:r._id,chatId:e,walletId:r.bsc_wallet.address,contractAddress:b,contractName:h,buyAmount:parseFloat(m.toString()),sellAmount:0,takeProfit:0,stopLoss:0,isSold:!1,blockchain:"bsc"});yield t.save()}return yield s.default.updateOne({_id:r._id},{$push:{"trades.bsc":x?x._id:r._id}}),E}catch(e){throw console.error("Error purchasing tokens:",e),e}}))};const o=r(9321),i=a(r(8087)),s=a(r(8722)),d=a(r(6786)),u=a(r(8938));a(r(818)).default.config();const l=process.env.EVM_ADMIN_ADDRESS||"",c=process.env.BSC_PROVIDER_URL;if(!l)throw new Error("ADMIN_ADDRESS environment variable is not set");if(!c)throw new Error("bsc_PROVIDER_URL environment variable is not set");const p={"0x-api-key":process.env.MASTER_KEY||""}},4211:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{d(n.next(e))}catch(e){o(e)}}function s(e){try{d(n.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}d((n=n.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.buyEthTokens=function(e,t){return n(this,void 0,void 0,(function*(){try{const r=yield s.default.findOne({chat_id:e}).exec();if(!r)throw new Error("User not found");const a=yield i.default.findOne({chatId:e,contractAddress:t,blockchain:"eth"}).exec();if(!a)throw new Error("Snipe configuration not found");const{private_key:f,address:y}=r.eth_wallet,h=r.ethCurrentTokenName,m=a.config.spend_eth,_=a.config.slippage_eth;if(null==m||isNaN(m))throw new Error("Invalid spend_eth value");if(null==_||isNaN(_))throw new Error("Invalid slippage_eth value");const b=t,v=new o.providers.JsonRpcProvider(c),g=new o.Wallet(f,v);let w=`buyToken=${b}&sellToken=******************************************&sellAmount=${o.ethers.utils.parseEther(m.toString())}&slippagePercentage=${_/100}&takerAddress=${y}&feeRecipient=${l}&buyTokenPercentageFee=0.8`;const S=`${process.env.ETHNODE_API_URL}?${w}`,T=yield function(e){return n(this,void 0,void 0,(function*(){return(yield u.default.get(e,{headers:p})).data}))}(S),N={to:T.to,data:T.data,value:o.ethers.BigNumber.from(T.value),gasLimit:o.ethers.BigNumber.from(1e6),gasPrice:o.ethers.utils.parseUnits("15","gwei")},k=yield g.sendTransaction(N),E=yield k.wait(),x=yield d.default.findOne({userId:r._id,contractAddress:b,isSold:!1}).exec();if(x)x.buyAmount+=parseFloat(m.toString()),yield x.save();else{const t=new d.default({userId:r._id,chatId:e,walletId:r.eth_wallet.address,contractAddress:b,contractName:h,buyAmount:parseFloat(m.toString()),sellAmount:0,takeProfit:0,stopLoss:0,isSold:!1,blockchain:"eth"});yield t.save()}return yield s.default.updateOne({_id:r._id},{$push:{"trades.eth":x?x._id:r._id}}),E}catch(e){throw console.error("Error purchasing tokens:",e),e}}))};const o=r(9321),i=a(r(8087)),s=a(r(8722)),d=a(r(6786)),u=a(r(8938));a(r(818)).default.config();const l=process.env.EVM_ADMIN_ADDRESS||"",c=process.env.ETH_PROVIDER_URL;if(!l)throw new Error("ADMIN_ADDRESS environment variable is not set");if(!c)throw new Error("eth_PROVIDER_URL environment variable is not set");const p={"0x-api-key":process.env.MASTER_KEY||""}},8087:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var a=Object.getOwnPropertyDescriptor(t,r);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,a)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const i=o(r(6037)),s=new i.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String,required:!0},config:{retries_base:{type:Number},retries_eth:{type:Number},retries_sol:{type:Number},retries_bsc:{type:Number},slippage_bsc:{type:Number},spend_bsc:{type:Number},timeDelay_bsc:{type:Number},blockDelay_bsc:{type:Number},retriesOnFail_bsc:{type:Number},slippage_base:{type:Number},spend_base:{type:Number},timeDelay_base:{type:Number},blockDelay_base:{type:Number},retriesOnFail_base:{type:Number},slippage_sol:{type:Number},spend_sol:{type:Number},timeDelay_sol:{type:Number},blockDelay_sol:{type:Number},retriesOnFail_sol:{type:Number},slippage_eth:{type:Number},spend_eth:{type:Number},timeDelay_eth:{type:Number},blockDelay_eth:{type:Number},retriesOnFail_eth:{type:Number},isConfigured:{type:Boolean,default:!1},isBought:{type:Boolean,default:!1},isExecuted_bsc:{type:Boolean,default:!1},isExecuted_base:{type:Boolean,default:!1},isExecuted_sol:{type:Boolean,default:!1},isExecuted_eth:{type:Boolean,default:!1}},hasLiquidity:{type:Boolean,default:!1},lastPriceUpdate:{type:Date}}),d=i.default.model("Snipe",s);t.default=d},6786:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var a=Object.getOwnPropertyDescriptor(t,r);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,a)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return a(t,e),t},i=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{d(n.next(e))}catch(e){o(e)}}function s(e){try{d(n.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}d((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.updateTradeAsSold=function(e,t,r,n){return i(this,void 0,void 0,(function*(){yield u.findOneAndUpdate({contractAddress:t},{isSold:!0}).exec()}))};const s=o(r(6037)),d=new s.Schema({chatId:{type:Number,required:!0},blockchain:{type:String,required:!0},contractAddress:{type:String},contractName:{type:String,default:""},buyAmount:{type:Number,default:0},sellAmount:{type:Number,default:0},isSold:{type:Boolean,default:!1},config:{takeProfit:{type:Number,default:0},stopLoss:{type:Number,default:0},spendAmount:{type:Number,default:0}}}),u=s.default.model("Trade",d);t.default=u},8722:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var a=Object.getOwnPropertyDescriptor(t,r);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,a)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const i=o(r(6037)),s=new i.Schema({chat_id:{type:Number,required:!0},first_name:{type:String,required:!0},last_name:{type:String},username:{type:String},sol_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},bsc_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},base_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},eth_wallet:{address:{type:String,required:!0},private_key:{type:String,required:!0}},preset_setting:{type:[Number],default:[.01,1,5,10]},nonce:{type:Number,default:0},retired:{type:Boolean,default:!1},referrer_code:{type:String,default:null},referrer_wallet:{type:String,default:null},referral_code:{type:String,default:null},referral_date:{type:String,default:null},schedule:{type:String,default:"60"},burn_fee:{type:Boolean,default:!1},auto_buy:{type:Boolean,default:!1},auto_buy_amount:{type:String,default:"0"},auto_sell_amount:{type:String,default:"0"},trades:{bsc:[{type:i.Schema.Types.ObjectId,ref:"Trade",default:[]}],sol:[{type:i.Schema.Types.ObjectId,ref:"Trade",default:[]}],eth:[{type:i.Schema.Types.ObjectId,ref:"Trade",default:[]}],base:[{type:i.Schema.Types.ObjectId,ref:"Trade",default:[]}]},currentBlockchain:{type:String,enum:["bsc","sol","eth","base"],default:"bsc"},sol_config:{slippage:{type:i.Schema.Types.Decimal128,default:.6},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:i.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:i.Schema.Types.Decimal128,default:0},maxMarketCap:{type:i.Schema.Types.Decimal128,default:0},maxLiquidity:{type:i.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:i.Schema.Types.Decimal128,default:0},sellLow:{type:i.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},bsc_config:{slippage:{type:i.Schema.Types.Decimal128,default:.1},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:i.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:i.Schema.Types.Decimal128,default:0},maxMarketCap:{type:i.Schema.Types.Decimal128,default:0},maxLiquidity:{type:i.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:i.Schema.Types.Decimal128,default:0},sellLow:{type:i.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},base_config:{slippage:{type:i.Schema.Types.Decimal128,default:.1},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:i.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:i.Schema.Types.Decimal128,default:0},maxMarketCap:{type:i.Schema.Types.Decimal128,default:0},maxLiquidity:{type:i.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:i.Schema.Types.Decimal128,default:0},sellLow:{type:i.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},eth_config:{slippage:{type:i.Schema.Types.Decimal128,default:.2},antirug:{type:Boolean,default:!1},antiMev:{type:Boolean,default:!1},maxGasPrice:{type:i.Schema.Types.Decimal128,default:10},maxGasLimit:{type:Number,default:1e6},autoBuy:{type:Boolean,default:!1},minLiquidity:{type:i.Schema.Types.Decimal128,default:0},maxMarketCap:{type:i.Schema.Types.Decimal128,default:0},maxLiquidity:{type:i.Schema.Types.Decimal128,default:0},autoSell:{type:Boolean,default:!1},sellHigh:{type:i.Schema.Types.Decimal128,default:0},sellLow:{type:i.Schema.Types.Decimal128,default:0},autoApprove:{type:Boolean,default:!1}},bsc_dashboard_message_id:{type:Number},bsc_dashboard_content:{type:String},bsc_dashboard_markup:{type:i.Schema.Types.Mixed},eth_dashboard_message_id:{type:Number},eth_dashboard_content:{type:String},eth_dashboard_markup:{type:i.Schema.Types.Mixed},sol_dashboard_message_id:{type:Number},sol_dashboard_content:{type:String},sol_dashboard_markup:{type:i.Schema.Types.Mixed},base_dashboard_message_id:{type:Number},base_dashboard_content:{type:String},base_dashboard_markup:{type:i.Schema.Types.Mixed},bscCurrentContractAddress:{type:String},solCurrentContractAddress:{type:String},baseCurrentContractAddress:{type:String},ethCurrentContractAddress:{type:String},bscCurrentTokenName:{type:String},solCurrentTokenName:{type:String},baseCurrentTokenName:{type:String},ethCurrentTokenName:{type:String},snipes:[{type:i.Schema.Types.ObjectId,ref:"Snipe",default:[]}]}),d=i.default.model("User",s);t.default=d},8904:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{d(n.next(e))}catch(e){o(e)}}function s(e){try{d(n.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}d((n=n.apply(e,t||[])).next())}))},a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getLiquidityInfoFromDexscreener=void 0;const o=a(r(8938));t.getLiquidityInfoFromDexscreener=e=>n(void 0,void 0,void 0,(function*(){const t=`https://api.dexscreener.com/latest/dex/tokens/${e}`;try{const r=yield o.default.get(t);if(!r.data.pairs||0===r.data.pairs.length)return console.log(`No liquidity found for the token ${e}.`),{hasLiquidity:!1,message:`No liquidity found for the token ${e}.`};const n=r.data.pairs[0],{baseToken:a,priceNative:i,priceUsd:s,chainId:d,priceChange:u,dexId:l,totalSupply:c,liquidity:p}=n,f=a.symbol,y=a.name||"Unknown Token";return{hasLiquidity:!0,symbol:f,name:y,priceNative:i,priceUsd:s,chainId:d,priceChange:u,dexId:l,totalSupply:c,pairAddress:n.pairAddress||"Not available",marketCap:n.marketCap||"Not available",address:a.address}}catch(e){throw console.error("Error fetching token info from Dexscreener:",e),new Error("Error fetching token info from Dexscreener. Please try again later.")}}))},1156:function(e,t,r){var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))((function(a,o){function i(e){try{d(n.next(e))}catch(e){o(e)}}function s(e){try{d(n.throw(e))}catch(e){o(e)}}function d(e){var t;e.done?a(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(i,s)}d((n=n.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const a=r(8167),o=r(6418),i=r(2313),s=r(4211),d=r(159),u=r(8904);if(!a.parentPort)throw new Error("This module should be run as a worker thread");a.parentPort.on("message",(e=>n(void 0,void 0,void 0,(function*(){try{let t;switch(e.type){case"CHECK_LIQUIDITY":t=yield(0,u.getLiquidityInfoFromDexscreener)(e.data.contractAddress);break;case"BUY_BASE_TOKEN":t=yield(0,o.buyBaseTokens)(e.data.chatId,e.data.contractAddress);break;case"BUY_BSC_TOKEN":t=yield(0,i.buyBscTokens)(e.data.chatId,e.data.contractAddress);break;case"BUY_ETH_TOKEN":t=yield(0,s.buyEthTokens)(e.data.chatId,e.data.contractAddress);break;case"BUY_SOL_TOKEN":t=yield(0,d.buySolTokens)(e.data.chatId,e.data.contractAddress);break;case"MONITOR_WALLET":t=yield function(e){return n(this,void 0,void 0,(function*(){return{success:!0,data:e}}))}(e.data);break;default:throw new Error(`Unknown task type: ${e.type}`)}a.parentPort&&a.parentPort.postMessage(t)}catch(e){if(a.parentPort){const t=e instanceof Error?e.message:String(e);a.parentPort.postMessage({error:t})}}}))))},8491:e=>{e.exports=require("@solana/web3.js")},8938:e=>{e.exports=require("axios")},8574:e=>{e.exports=require("bs58")},6713:e=>{e.exports=require("cross-fetch")},818:e=>{e.exports=require("dotenv")},9321:e=>{e.exports=require("ethers")},6037:e=>{e.exports=require("mongoose")},8167:e=>{e.exports=require("worker_threads")}},t={};!function r(n){var a=t[n];if(void 0!==a)return a.exports;var o=t[n]={exports:{}};return e[n].call(o.exports,o,o.exports,r),o.exports}(1156)})();