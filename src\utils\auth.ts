import crypto from 'crypto';

/**
 * Utility functions for API authentication
 */

/**
 * Generate the Authorization header for Barbour ABI API
 * 
 * @param username - The API username
 * @param password - The API password
 * @returns The Base64 encoded Authorization header value
 */
export function generateBarbour<PERSON>biAuthHeader(username: string = 'api_user', password: string = 'MB_4810'): string {
  // Step 1: SHA256 hash the password
  const passwordHash = crypto
    .createHash('sha256')
    .update(password)
    .digest('hex');

  // Step 2: Append the hash to the username with a colon separator
  const authString = `${username}:${passwordHash}`;

  // Step 3: Base64 encode the combined string
  const base64Auth = Buffer.from(authString).toString('base64');

  return base64Auth;
}

/**
 * Make an authenticated request to the Barbour ABI API
 * 
 * @param apiKey - The API key provided by Barbour ABI
 * @returns The headers object for the request
 */
export function getBarbourAbiHeaders(apiKey: string): Record<string, string> {
  const authHeader = generateBarbourAbiAuthHeader();
  
  return {
    'Authorization': `Basic ${authHeader}`,
    'x-api-key': apiKey
  };
}

/**
 * Example usage of the authentication utility
 */
export async function loginToBarbourAbi(apiKey: string): Promise<boolean> {
  try {
    const headers = getBarbourAbiHeaders(apiKey);
    
    const response = await fetch('https://api.barbour-abi.com/v4/login', {
      method: 'GET',
      headers
    });
    
    if (response.ok) {
      console.log('Successfully authenticated with Barbour ABI API');
      return true;
    } else {
      console.error(`Authentication failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.error('Error authenticating with Barbour ABI API:', error);
    return false;
  }
}
