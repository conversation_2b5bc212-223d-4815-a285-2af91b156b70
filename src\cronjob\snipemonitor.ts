/**
 * Snipe Monitor
 *
 * This module monitors tokens for liquidity and executes snipe trades
 * when conditions are met.
 */

import http from 'http';
import { Server } from 'socket.io';
import TelegramBot from 'node-telegram-bot-api';
import Snipe, { ISnipe, ISnipeConfig } from '../models/snipe.model';
import { BlockchainType, TraderFactory } from '../services/trading/TradingInterface';
import { logger } from '../services/logger/LoggerService';
import { getLiquidityInfoFromDexscreener } from '../services/checkliquidity.service';
import { cacheService } from '../services/cache/RedisService';
import {
  initializeSocketUtils,
  registerUserSocket,
  unregisterSocket,
  sendToUser as sendSocketToUser,
  userSockets,
  socketUsers
} from '../utils/socket.utils';

// Constants
const BATCH_SIZE = 10; // Process 10 tokens at a time (reduced for better reliability)
const UPDATE_INTERVAL = 30000; // 30 seconds
const MAX_RETRIES = 3;
const CACHE_TTL = 60; // 1 minute cache for liquidity checks

// Create server and socket.io instance for the web dashboard
// NOTE: This is NOT used for Telegram bot communication, which uses HTTP.
// Socket.IO is only for browser-based real-time updates on the web interface.
const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST']
  }
});

// Set max listeners to avoid memory leak warnings
server.setMaxListeners(20);
io.setMaxListeners(20);

// Initialize socket utilities for web dashboard
initializeSocketUtils(io);

/**
 * Send a message to a Telegram user
 * @param bot Telegram bot instance
 * @param chatId Chat ID to send message to
 * @param message Message text
 * @param options Message options
 */
const sendMessageToUser = async (
  bot: TelegramBot,
  chatId: number,
  message: string,
  options?: TelegramBot.SendMessageOptions
): Promise<void> => {
  try {
    await bot.sendMessage(chatId, message, options);
    logger.debug(`Message sent to chat ID ${chatId}`, {
      chatId,
      messageLength: message.length
    });
  } catch (error) {
    logger.error(`Failed to send message to chat ID ${chatId}`, {
      error: (error as Error).message,
      chatId
    });
  }
};

/**
 * Check liquidity for a token
 * @param contractAddress The token contract address
 * @param blockchain The blockchain
 * @returns Liquidity information
 */
async function checkLiquidity(contractAddress: string, blockchain: string): Promise<any> {
  const cacheKey = `liquidity:${blockchain}:${contractAddress}`;

  try {
    // Check cache first
    const cachedResult = await cacheService.get(cacheKey);
    if (cachedResult) {
      logger.debug(`Using cached liquidity info for ${contractAddress}`, {
        contractAddress,
        blockchain
      });
      return cachedResult;
    }

    // Get liquidity info
    const result = await getLiquidityInfoFromDexscreener(contractAddress);

    // Cache the result
    await cacheService.set(cacheKey, result, CACHE_TTL);

    return result;
  } catch (error) {
    logger.error(`Error checking liquidity for ${contractAddress}`, {
      error: (error as Error).message,
      contractAddress,
      blockchain
    });
    throw error;
  }
}

/**
 * Execute a snipe
 * @param snipe The snipe configuration
 * @param bot Telegram bot instance
 * @returns Result of the snipe
 */
async function executeSnipe(snipe: ISnipe, bot: TelegramBot): Promise<any> {
  const { chatId, contractAddress, blockchain } = snipe;

  try {
    logger.info(`Executing snipe for ${contractAddress} on ${blockchain}`, {
      chatId,
      contractAddress,
      blockchain
    });

    // Get the appropriate trader
    let result;

    try {
      // Try to use the trader factory first
      if (TraderFactory.hasTrader(blockchain as BlockchainType)) {
        const trader = TraderFactory.getTrader(blockchain as BlockchainType);

        // Get snipe configuration
        const config = snipe.config;
        const spendKey = `spend_${blockchain}` as keyof typeof config;
        const slippageKey = `slippage_${blockchain}` as keyof typeof config;

        const spendAmount = config[spendKey]?.toString() || '0.1';
        const slippage = Number(config[slippageKey]) || 5;

        if (parseFloat(spendAmount) <= 0) {
          throw new Error(`Invalid spend amount: ${spendAmount}`);
        }

        // Execute the trade
        result = await trader.buyTokens(
          contractAddress,
          spendAmount,
          '', // Wallet address will be determined by the trader
          { slippage }
        );

        if (!result.success) {
          throw new Error(result.error || 'Transaction failed');
        }
      } else {
        // If no trader is registered, throw an error
        throw new Error(`No trader registered for blockchain: ${blockchain}`);
      }

      // Update the snipe status
      const executedKey = `isExecuted_${blockchain}` as keyof typeof snipe.config;
      await Snipe.updateOne(
        { _id: snipe._id },
        {
          $set: {
            [`config.${String(executedKey)}`]: true,
            [`config.isBought`]: true
          }
        }
      );

      // Notify the user
      // Get the spend amount from the config
      const config = snipe.config;
      const spendKey = `spend_${blockchain}` as keyof typeof config;
      const spendAmount = config[spendKey]?.toString() || '0.1';

      await sendMessageToUser(
        bot,
        chatId,
        `✅ Successfully sniped ${contractAddress} on ${blockchain.toUpperCase()}!\n\n` +
        `🔗 Transaction Hash: ${result.transactionHash}\n` +
        `💰 Amount Spent: ${spendAmount} ${blockchain.toUpperCase()}`
      );

      // Send user-specific socket notification
      sendSocketToUser(chatId, {
        tokenAddress: contractAddress,
        tokenInfo: {
          hasLiquidity: true,
          message: 'Liquidity detected and token purchased.',
          transactionHash: result.transactionHash
        },
      }, 'tokenUpdate');

      logger.info(`Successfully sniped token ${contractAddress} on ${blockchain}`, {
        chatId,
        contractAddress,
        blockchain,
        transactionHash: result.transactionHash
      });

      return { success: true, transactionHash: result.transactionHash };
    } catch (error) {
      logger.error(`Error executing snipe for ${contractAddress}`, {
        error: (error as Error).message,
        chatId,
        contractAddress,
        blockchain
      });

      // Increment retry counter
      const retryKey = `retries_${blockchain}` as keyof typeof snipe.config;
      const currentRetries = (snipe.config[retryKey] as number) || 0;

      await Snipe.updateOne(
        { _id: snipe._id },
        { $set: { [`config.${String(retryKey)}`]: currentRetries + 1 } }
      );

      // Notify the user of the error
      await sendMessageToUser(
        bot,
        chatId,
        `❌ Error sniping ${contractAddress} on ${blockchain.toUpperCase()}: ${(error as Error).message}`
      );

      throw error;
    }
  } catch (error) {
    logger.error(`Failed to execute snipe for ${contractAddress}`, {
      error: (error as Error).message,
      chatId,
      contractAddress,
      blockchain
    });
    return { success: false, error: (error as Error).message };
  }
}

/**
 * Update tokens (check liquidity and execute snipes)
 * @param bot Telegram bot instance
 */
export const updateTokens = async (bot: TelegramBot): Promise<void> => {
  const startTime = Date.now();
  logger.info('Started checking tokens for liquidity');

  try {
    // Find all configured snipes that haven't been bought yet
    const snipes = await Snipe.find({
      'config.isConfigured': true,
      'config.isBought': false,
      $or: [
        { 'config.isExecuted_bsc': false },
        { 'config.isExecuted_sol': false },
        { 'config.isExecuted_eth': false },
        { 'config.isExecuted_base': false },
      ],
    }).lean();

    if (snipes.length === 0) {
      logger.debug('No snipes to process');
      return;
    }

    logger.info(`Found ${snipes.length} snipes to process`);

    // Group snipes by blockchain for better processing
    const snipesByBlockchain = snipes.reduce<Record<string, ISnipe[]>>((acc, snipe) => {
      const blockchain = snipe.blockchain;
      if (!acc[blockchain]) {
        acc[blockchain] = [];
      }
      acc[blockchain].push(snipe);
      return acc;
    }, {});

    // Process each blockchain's snipes
    for (const [blockchain, blockchainSnipes] of Object.entries(snipesByBlockchain)) {
      logger.info(`Processing ${blockchainSnipes.length} snipes for ${blockchain}`);

      // Get unique token addresses for this blockchain
      const tokenAddresses = [...new Set(blockchainSnipes.map(snipe => snipe.contractAddress))];

      // Process in batches
      for (let i = 0; i < tokenAddresses.length; i += BATCH_SIZE) {
        const batch = tokenAddresses.slice(i, i + BATCH_SIZE);
        logger.debug(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(tokenAddresses.length / BATCH_SIZE)} for ${blockchain}`);

        // Check liquidity for each token in the batch
        const liquidityResults = await Promise.all(
          batch.map(address => checkLiquidity(address, blockchain))
        );

        // Process results
        for (let j = 0; j < batch.length; j++) {
          const address = batch[j];
          const data = liquidityResults[j];

          // Find all snipes for this token address
          const tokenSnipes = blockchainSnipes.filter(s => s.contractAddress === address);

          // Update all snipes with liquidity info
          for (const snipe of tokenSnipes) {
            // Update the snipe document with liquidity information
            await Snipe.updateOne(
              { _id: snipe._id },
              {
                $set: {
                  hasLiquidity: data.hasLiquidity,
                  lastPriceUpdate: new Date()
                }
              }
            );

            // Execute the snipe if it has liquidity and hasn't been executed yet
            const executedKey = `isExecuted_${blockchain}`;
            if (data.hasLiquidity && !snipe.config[executedKey as keyof ISnipeConfig]) {
              logger.info(`Liquidity found for token ${address} on ${blockchain}, executing snipe`);

              // Execute the snipe
              executeSnipe(snipe, bot).catch(error => {
                logger.error(`Failed to execute snipe for ${address}`, {
                  error: (error as Error).message,
                  blockchain,
                  address
                });
              });

              // Notify users that we found liquidity and are attempting to snipe
              await sendMessageToUser(
                bot,
                snipe.chatId,
                `🔍 Liquidity detected for ${address} on ${blockchain.toUpperCase()}! Attempting to snipe...`
              );

              // Send user-specific socket notification
              sendSocketToUser(snipe.chatId, {
                tokenAddress: address,
                blockchain,
                timestamp: new Date().toISOString()
              }, 'liquidityFound');
            } else if (!data.hasLiquidity) {
              logger.debug(`No liquidity found for token ${address} on ${blockchain}`);
            }
          }
        }

        // Add a small delay between batches to avoid rate limiting
        if (i + BATCH_SIZE < tokenAddresses.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    // Log performance metrics
    const duration = Date.now() - startTime;
    logger.info(`Completed token update in ${duration}ms`, {
      snipeCount: snipes.length,
      durationMs: duration
    });
  } catch (error) {
    logger.error('Error updating tokens', {
      error: (error as Error).message,
      stack: (error as Error).stack
    });
  }
};

/**
 * Start periodic snipe updates
 * @param bot Telegram bot instance
 * @returns The interval ID
 */
export const startPeriodicSnipeUpdates = (bot: TelegramBot): ReturnType<typeof setInterval> => {
  logger.info(`Starting periodic snipe updates (every ${UPDATE_INTERVAL / 1000} seconds)`);
  return setInterval(() => updateTokens(bot), UPDATE_INTERVAL);
};

/**
 * Initialize the Socket.IO server
 * @param port Server port
 * @param bot Telegram bot instance
 */
export const initializeSocketServer = (port: number, bot: TelegramBot): void => {
  // Set up Socket.IO event handlers
  io.on('connection', (socket: any) => {
    logger.info('New client connected', { socketId: socket.id });

    // Handle user authentication
    socket.on('authenticate', (data: { chatId: number, token: string }) => {
      // In a real application, you would validate the token here
      // For now, we'll just trust the chatId
      const { chatId } = data;

      if (!chatId) {
        socket.emit('error', { message: 'Authentication failed: Missing chatId' });
        return;
      }

      // Register this socket with the user
      registerUserSocket(socket, chatId);

      logger.info('User authenticated', { socketId: socket.id, chatId });

      // Send user-specific initial data
      sendUserSnipeData(socket, chatId);

      // Confirm authentication
      socket.emit('authenticated', { chatId });
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      // Unregister this socket
      unregisterSocket(socket.id);

      logger.info('Client disconnected', { socketId: socket.id });
    });

    // Handle manual snipe request
    socket.on('execute_snipe', async (data: { snipeId: string }) => {
      try {
        // Get the chatId from the socket
        const chatId = socketUsers.get(socket.id);
        if (!chatId) {
          socket.emit('error', { message: 'You must be authenticated to execute a snipe' });
          return;
        }

        const snipe = await Snipe.findById(data.snipeId);
        if (!snipe) {
          socket.emit('error', { message: 'Snipe not found' });
          return;
        }

        // Verify that this snipe belongs to the user
        if (snipe.chatId !== chatId) {
          socket.emit('error', { message: 'You do not have permission to execute this snipe' });
          return;
        }

        // Execute the snipe
        executeSnipe(snipe, bot).then(result => {
          // Send result to all of the user's connected sockets
          sendSocketToUser(chatId, {
            snipeId: data.snipeId,
            result
          }, 'snipe_executed');
        }).catch(error => {
          socket.emit('error', { message: (error as Error).message });
        });

        socket.emit('snipe_queued', {
          snipeId: data.snipeId,
          message: 'Snipe has been queued for execution'
        });

        logger.info(`Manual snipe queued for ${snipe.contractAddress}`, {
          snipeId: data.snipeId,
          blockchain: snipe.blockchain,
          chatId
        });
      } catch (error) {
        logger.error('Error executing manual snipe', {
          error: (error as Error).message,
          snipeId: data.snipeId,
          socketId: socket.id
        });
        socket.emit('error', { message: (error as Error).message });
      }
    });

    // Add health check endpoint
    socket.on('health_check', async () => {
      try {
        // Get the chatId from the socket
        const chatId = socketUsers.get(socket.id);

        const health = {
          status: 'healthy',
          uptime: process.uptime(),
          timestamp: new Date().toISOString(),
          user: chatId ? {
            chatId,
            authenticated: true,
            activeConnections: userSockets.get(chatId)?.size || 1
          } : {
            authenticated: false
          }
        };

        socket.emit('health_status', health);
      } catch (error) {
        logger.error('Error sending health status', {
          error: (error as Error).message,
          socketId: socket.id
        });
        socket.emit('error', { message: (error as Error).message });
      }
    });
  });

  // Start the Socket.IO server for the web dashboard
  server.listen(port, () => {
    logger.info(`Web dashboard Socket.IO server is running on port ${port}`);
  });

  // Start periodic updates
  startPeriodicSnipeUpdates(bot);

  // Register shutdown handler
  process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    await gracefulShutdown();
  });

  process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    await gracefulShutdown();
  });
};

/**
 * Graceful shutdown
 */
async function gracefulShutdown(): Promise<void> {
  logger.info('Starting graceful shutdown');

  try {
    // Remove all socket listeners
    io.removeAllListeners();

    // Close all socket connections
    const sockets = await io.fetchSockets();
    for (const socket of sockets) {
      socket.disconnect(true);
    }

    // Stop accepting new connections
    await new Promise<void>((resolve) => {
      server.close(() => {
        logger.info('HTTP server closed');
        resolve();
      });
    });

    logger.info('Graceful shutdown completed');
  } catch (error) {
    logger.error('Error during graceful shutdown', {
      error: (error as Error).message
    });
    throw error;
  }
}

/**
 * Send user-specific snipe data to a socket
 * @param socket Socket.IO socket
 * @param chatId User's chat ID
 */
const sendUserSnipeData = async (socket: any, chatId: number): Promise<void> => {
  try {
    // Find only snipes that belong to this user
    const snipes = await Snipe.find({
      chatId,
      'config.isConfigured': true,
      'config.isBought': false
    }).lean();

    socket.emit('snipe_data', { snipes });

    logger.debug(`Sent user-specific snipe data to client`, {
      socketId: socket.id,
      chatId,
      snipeCount: snipes.length
    });
  } catch (error) {
    logger.error('Error sending user-specific snipe data', {
      error: (error as Error).message,
      socketId: socket.id,
      chatId
    });
  }
};

/**
 * Send snipe data to all sockets of a specific user
 * @param chatId User's chat ID
 * @param data Data to send
 * @param event Event name
 */
const sendToUser = (chatId: number, data: any, event: string): void => {
  const socketIds = userSockets.get(chatId);
  if (!socketIds || socketIds.size === 0) {
    // User has no active socket connections
    return;
  }

  // Send to all of the user's connected sockets
  for (const socketId of socketIds) {
    const socket = io.sockets.sockets.get(socketId);
    if (socket) {
      socket.emit(event, data);
    }
  }
};
