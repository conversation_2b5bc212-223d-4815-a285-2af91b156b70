"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.tradeEthScreen = exports.reviewethTrade = exports.monitorEthPriceAndSell = exports.executeEthTrade = exports.updateEthTradeConfig = exports.createEthTradeKeyboard = exports.askForEthTradeInput = void 0;
exports.getEthTokenBalance = getEthTokenBalance;
const ethers_1 = require("ethers");
const autotrade_model_1 = __importDefault(require("../../models/autotrade.model"));
const fetchPriceChange_1 = require("../../services/fetchPriceChange");
const ethuniswap_trader_1 = require("../../executor/ethuniswap.trader");
const autotrade_model_2 = require("../../models/autotrade.model");
const trade_model_1 = require("../../models/trade.model");
const user_models_1 = __importDefault(require("../../models/user.models"));
// Define the provider URL and admin address
const adminAddress = process.env.EVM_ADMIN_ADDRESS || '';
const providerUrl = process.env.ETH_PROVIDER_URL;
if (!adminAddress) {
    throw new Error('ADMIN_ADDRESS environment variable is not set');
}
if (!providerUrl) {
    throw new Error('Provider environment variable is not set');
}
const promptMessageIdStore = {};
const askForEthTradeInput = (bot, chatId, promptMessage, field) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Clear previous messages if any
        if (promptMessageIdStore[chatId]) {
            const ids = promptMessageIdStore[chatId];
            if (ids.promptId) {
                yield bot.deleteMessage(chatId, ids.promptId).catch(console.error);
            }
            if (ids.replyId) {
                yield bot.deleteMessage(chatId, ids.replyId).catch(console.error);
            }
            if (ids.successId) {
                yield bot.deleteMessage(chatId, ids.successId).catch(console.error);
            }
        }
        // Send prompt message
        const sentPromptMessage = yield bot.sendMessage(chatId, promptMessage, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            }
        });
        promptMessageIdStore[chatId] = { promptId: sentPromptMessage.message_id };
        // Wait for user response
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentPromptMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    const value = parseFloat(msg.text);
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    // Send success message
                    const successMessage = `✅ ${field.charAt(0).toUpperCase() + field.slice(1)} successfully updated.`;
                    const sentSuccessMessage = yield bot.sendMessage(chatId, successMessage);
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { successId: sentSuccessMessage.message_id });
                    resolve(value);
                }
                else {
                    reject(new Error(`Invalid ${field}.`));
                }
            }));
        });
    }
    catch (error) {
        console.error(`Failed to ask for ${field}:`, error);
        throw error;
    }
});
exports.askForEthTradeInput = askForEthTradeInput;
const createEthTradeKeyboard = () => ({
    inline_keyboard: [
        [{ text: '🛒 Execute Trade', callback_data: JSON.stringify({ command: 'snipetrade_buy_eth' }) }],
        [
            { text: '📈 Take Profit', callback_data: JSON.stringify({ command: 'snipe_set_take_profit_eth' }) },
            { text: '📉 Stop Loss', callback_data: JSON.stringify({ command: 'snipe_set_stop_loss_eth' }) },
            { text: '📉 Amount', callback_data: JSON.stringify({ command: 'snipe_set_spend_amount_eth' }) },
        ],
        [
            { text: '🔄 Refresh Token', callback_data: JSON.stringify({ command: 'snipe_refresh_token_eth' }) },
            { text: '🔍 Review Trade', callback_data: JSON.stringify({ command: 'snipe_review_trade_eth' }) },
        ],
        [{ text: '⬅️ Back', callback_data: JSON.stringify({ command: 'back_to_main_trade' }) }]
    ]
});
exports.createEthTradeKeyboard = createEthTradeKeyboard;
const updateEthTradeConfig = (chatId, contractAddress, field, value) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const trade = yield autotrade_model_1.default.findOne({ chatId, contractAddress, blockchain: 'eth' });
        if (trade) {
            trade.config[field] = value;
            yield trade.save();
            console.log(`Updated ${String(field)} for trade:`, trade);
        }
    }
    catch (error) {
        console.error(`Error updating trade ${String(field)}:`, error);
    }
});
exports.updateEthTradeConfig = updateEthTradeConfig;
const executeEthTrade = (bot, chatId, contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const trade = yield autotrade_model_1.default.findOne({ chatId, contractAddress, blockchain: 'eth' });
        if (!trade) {
            yield bot.sendMessage(chatId, '❌ Trade configuration not found.');
            return;
        }
        // Check if all configuration parameters are set (allowing values to be <= 0)
        if (trade.config.spendAmount <= 0) {
            yield bot.sendMessage(chatId, '⚠️ Trade cannot be executed. Please make sure all configuration parameters (take profit, stop loss, and spend amount) are set.');
            return;
        }
        const spendAmount = trade.config.spendAmount.toString();
        const buyReceipt = yield (0, ethuniswap_trader_1.buyEthTokens)(chatId, spendAmount);
        if (!buyReceipt.status) {
            yield bot.sendMessage(chatId, '❌ Failed to purchase tokens.');
            return;
        }
        // Update the trade status to indicate it's no longer pending
        yield autotrade_model_1.default.findOneAndUpdate({ chatId, contractAddress, blockchain: 'eth' }, { isSold: false, isInitialized: 'ongoing' } // Set to 'ongoing'
        ).exec();
        yield bot.sendMessage(chatId, `✅ Successfully purchased tokens. Transaction Hash: ${buyReceipt.transactionHash}`);
        yield bot.sendMessage(chatId, '✅ Trade executed successfully and monitoring started.');
    }
    catch (error) {
        console.error(`Error executing trade:`, error);
        yield bot.sendMessage(chatId, '⚠️ An error occurred while executing the trade.');
    }
});
exports.executeEthTrade = executeEthTrade;
const activeCronJobs = {}; // Store active cron jobs in memory
const monitorEthPriceAndSell = (bot, chatId, trade) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Fetch token info
        const tokenInfo = yield (0, fetchPriceChange_1.getTokenInfoFromDexscreener)(trade.contractAddress);
        if (!tokenInfo || tokenInfo.priceChange === undefined) {
            console.error('Token info or price change not found.');
            return false;
        }
        // Convert priceChange to number for comparison
        const priceChange = parseFloat(tokenInfo.priceChange);
        if (isNaN(priceChange)) {
            console.error('Invalid priceChange value:', tokenInfo.priceChange);
            return false;
        }
        console.log(`Price Change: ${priceChange}`);
        // Check if priceChange meets the conditions
        if (priceChange >= trade.config.takeProfit || priceChange <= trade.config.stopLoss) {
            console.log('Conditions met for selling tokens.');
            // Define job key
            const jobKey = `${trade.chatId}_${trade.contractAddress}_${trade.blockchain}`;
            // Stop the cron job if it exists
            if (activeCronJobs[jobKey]) {
                activeCronJobs[jobKey].stop();
                delete activeCronJobs[jobKey];
            }
            // // Remove job record from the dataeth
            // await CronJob.findOneAndDelete({
            //   chatId: trade.chatId,
            //   contractAddress: trade.contractAddress,
            //   blockchain: trade.blockchain
            // });
            // Fetch user details
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user) {
                throw new Error('User not found');
            }
            const { address: userAddress } = user.eth_wallet;
            // Get token balance
            const tokenBalanceString = yield getEthTokenBalance(trade.contractAddress, userAddress);
            const tokenBalanceNumber = parseFloat(tokenBalanceString);
            if (isNaN(tokenBalanceNumber) || tokenBalanceNumber <= 0) {
                console.error('Invalid or zero token balance.');
                return false;
            }
            console.log(`Token Balance: ${tokenBalanceNumber}`);
            // Use the token balance directly as the spend amount
            const spendAmount = tokenBalanceNumber.toString();
            let sellSuccess = false;
            let attempts = 0;
            // Attempt to sell the tokens
            while (attempts < 2 && !sellSuccess) {
                try {
                    attempts++;
                    // Sell all available tokens
                    yield (0, ethuniswap_trader_1.sellEthTokens)(trade.chatId, spendAmount);
                    sellSuccess = true;
                    // Update the trade as sold in the dataeth
                    yield (0, autotrade_model_2.updateAutoTradeAsSold)(user._id, trade.contractAddress, tokenBalanceNumber, 'eth');
                    yield (0, trade_model_1.updateTradeAsSold)(user._id, trade.contractAddress, tokenBalanceNumber, 'eth');
                    if (bot) {
                        yield bot.sendMessage(trade.chatId, '✅ Trade Auto-sold successfully.');
                    }
                    return true;
                }
                catch (sellError) {
                    console.error(`Attempt ${attempts} to sell tokens failed:`, sellError);
                }
            }
            if (!sellSuccess) {
                console.error('Failed to sell tokens after 3 attempts.');
            }
            return sellSuccess;
        }
        else {
            console.log('Conditions not met, continuing monitoring on eth...');
            return false;
        }
    }
    catch (error) {
        console.error('Error in monitorEthPriceAndSell:', error);
        return false;
    }
});
exports.monitorEthPriceAndSell = monitorEthPriceAndSell;
// The getEthTokenBalance function remains the same as you provided.
function getEthTokenBalance(tokenAddress, userAddress) {
    return __awaiter(this, void 0, void 0, function* () {
        const provider = new ethers_1.ethers.providers.JsonRpcProvider(providerUrl);
        const tokenContract = new ethers_1.ethers.Contract(tokenAddress, [
            'function balanceOf(address owner) view returns (uint256)',
            'function decimals() view returns (uint8)'
        ], provider);
        // Fetch balance and decimals
        const [balance, decimals] = yield Promise.all([
            tokenContract.balanceOf(userAddress),
            tokenContract.decimals()
        ]);
        // Convert balance to a human-readable format
        const formattedBalance = ethers_1.ethers.utils.formatUnits(balance, decimals);
        return formattedBalance;
    });
}
const reviewethTrade = (bot, chatId, messageId, contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const trade = yield autotrade_model_1.default.findOne({ chatId, contractAddress, blockchain: 'eth' });
        if (!trade) {
            yield bot.sendMessage(chatId, '❌ No trade found.');
            return;
        }
        let isSoldStatus = trade.isSold ? 'Yes' : 'No';
        const tradeDetails = `💼 **Trade Details:**
📝 **Contract:** ${contractAddress}
📈 **Take Profit:** ${trade.config.takeProfit}%
📉 **Stop Loss:** ${trade.config.stopLoss}%
💰 **Spend Amount:** ${trade.config.spendAmount} ETH
🔄 **Is Sold:** ${isSoldStatus}`;
        yield bot.editMessageText(tradeDetails, {
            chat_id: chatId,
            message_id: messageId,
            parse_mode: 'Markdown',
            reply_markup: (0, exports.createEthTradeKeyboard)(),
        });
    }
    catch (error) {
        console.error('Error in reviewethTrade:', error);
        yield bot.sendMessage(chatId, '⚠️ An error occurred while reviewing the trade.');
    }
});
exports.reviewethTrade = reviewethTrade;
// Main function to display the eth trading screen
const tradeEthScreen = (bot, chatId, contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const tokenInfo = yield (0, fetchPriceChange_1.getTokenInfoFromDexscreener)(contractAddress);
        if (!tokenInfo) {
            yield bot.sendMessage(chatId, '❌ Token information not found.');
            return;
        }
        const { symbol, name } = tokenInfo;
        const newTrade = new autotrade_model_1.default({
            chatId,
            blockchain: 'eth',
            contractAddress,
            contractName: name,
            isSold: false, // Set initial status
            isInitialized: 'pending', // Set to 'pending' for new trades
            config: {
                takeProfit: 0,
                stopLoss: 0,
                spendAmount: 0,
            },
        });
        yield newTrade.save();
        const message = `💼 **eth Trading**
📝 **Contract:** ${contractAddress}
🚀 **Token:** ${name} (${symbol})
🔗 [Dexscreener Link](https://dexscreener.com/ethereum/${contractAddress})

What would you like to do next?`;
        yield bot.sendMessage(chatId, message, {
            parse_mode: 'Markdown',
            reply_markup: (0, exports.createEthTradeKeyboard)(),
        });
        // Handle callback queries for trade options
        bot.on('callback_query', (callbackQuery) => __awaiter(void 0, void 0, void 0, function* () {
            const data = JSON.parse(callbackQuery.data);
            const command = data.command;
            switch (command) {
                case 'snipe_set_take_profit_eth':
                    const takeProfit = yield (0, exports.askForEthTradeInput)(bot, chatId, '📈 Set your take profit %:', 'take profit');
                    yield (0, exports.updateEthTradeConfig)(chatId, contractAddress, 'takeProfit', takeProfit);
                    // await bot.sendMessage(chatId, '✅ Take profit updated successfully.');
                    break;
                case 'snipe_set_stop_loss_eth':
                    const stopLoss = yield (0, exports.askForEthTradeInput)(bot, chatId, '📉 Set your stop loss %:', 'stop loss');
                    yield (0, exports.updateEthTradeConfig)(chatId, contractAddress, 'stopLoss', stopLoss);
                    // await bot.sendMessage(chatId, '✅ Stop loss updated successfully.');
                    break;
                case 'snipe_set_spend_amount_eth':
                    const spendAmount = yield (0, exports.askForEthTradeInput)(bot, chatId, '💰 Set the amount to spend:', 'spend amount');
                    yield (0, exports.updateEthTradeConfig)(chatId, contractAddress, 'spendAmount', spendAmount);
                    // await bot.sendMessage(chatId, '✅ Spend Amount updated successfully.');
                    break;
                case 'snipetrade_buy_eth':
                    yield (0, exports.executeEthTrade)(bot, chatId, contractAddress);
                    break;
                case 'snipe_review_trade_eth':
                    yield (0, exports.reviewethTrade)(bot, chatId, callbackQuery.message.message_id, contractAddress);
                    break;
                case 'back_to_main_trade':
                    // Handle back to main trade logic
                    break;
            }
        }));
    }
    catch (error) {
        console.error('Error in tradeethScreen:', error);
        yield bot.sendMessage(chatId, '⚠️ An error occurred while accessing the eth trading screen.');
    }
});
exports.tradeEthScreen = tradeEthScreen;
