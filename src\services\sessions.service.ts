import TelegramBot from 'node-telegram-bot-api';
import TradeModel from '../../src/models/trade.model';
import User from '../../src/models/user.models';
import mongoose from 'mongoose';

interface Session {
  session: mongoose.Types.ObjectId[];
  currentTradeIndex: number;
  telegramId: number;
  userId: any;
  trades: any[];
}

let userSessions: { [chatId: number]: Session } = {};

export const createSession = async (bot: TelegramBot, msg: TelegramBot.Message) => {
  const chatId = msg.chat.id;

  if (!userSessions[chatId]) {
    const user = await User.findOne({ chat_id: chatId }).exec();

    if (user) {
      const currentBlockchain = user.currentBlockchain as unknown as string;

      const login = (user as any)[currentBlockchain] || [];

      userSessions[chatId] = {
        session: login,
        currentTradeIndex: 0,
        telegramId: user.chat_id,
        userId: user._id,
        trades: [],
      };

      await bot.sendMessage(chatId, `Session created. You are now logged in to your ${currentBlockchain.toUpperCase()} wallet.`);
    } else {
      await bot.sendMessage(chatId, 'User not found. Please register first.');
    }
  } else {
    await bot.sendMessage(chatId, 'You are already logged in.');
  }
};