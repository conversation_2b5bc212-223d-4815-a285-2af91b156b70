"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleBscDashboard = exports.getBscBalance = void 0;
const ethers_1 = require("ethers");
const user_models_1 = __importDefault(require("../../models/user.models"));
const getBscBalance = (address) => __awaiter(void 0, void 0, void 0, function* () {
    const providerUrl = process.env.BSC_PROVIDER_URL;
    if (!providerUrl) {
        throw new Error('BSC_PROVIDER_URL is not defined in the .env file');
    }
    const providerBSC = new ethers_1.ethers.providers.JsonRpcProvider(providerUrl);
    const balanceBSC = yield providerBSC.getBalance(address);
    return parseFloat(ethers_1.ethers.utils.formatEther(balanceBSC)).toFixed(6);
});
exports.getBscBalance = getBscBalance;
const handleBscDashboard = (bot, chat_id) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const user = yield user_models_1.default.findOne({ chat_id });
    if (!user) {
        bot.sendMessage(chat_id, 'User not found.');
        return;
    }
    const firstName = user.first_name || 'User';
    const bscWallet = ((_a = user.bsc_wallet) === null || _a === void 0 ? void 0 : _a.address) || 'Not set';
    const balance = bscWallet !== 'Not set' ? yield (0, exports.getBscBalance)(bscWallet) : '0.0 BNB';
    const welcomeMessage = `👋 Welcome, ${firstName}, to your BSC Dashboard!
\n📍 **Address:** \`${bscWallet}\`
\n🔗 **Blockchain:** BSC
\n💰 **Balance:** \`${balance} BNB\`
\n⚙️ **Settings:** [Antirug, etc.]`;
    const options = {
        parse_mode: 'Markdown',
        reply_markup: {
            inline_keyboard: [
                [
                    { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_bsc' }) }
                ],
                [
                    { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
                    { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_bsc' }) }
                ],
                [
                    { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_bsc' }) },
                    { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_bsc_wallet' }) }
                ],
                [
                    {
                        text: "* Dismiss message",
                        callback_data: JSON.stringify({
                            command: "dismiss_message",
                        }),
                    },
                ]
            ]
        }
    };
    const sentMessage = yield bot.sendMessage(chat_id, welcomeMessage, options);
    yield user_models_1.default.updateOne({ chat_id: chat_id }, {
        $set: {
            'bsc_dashboard_message_id': sentMessage.message_id,
            'bsc_dashboard_content': welcomeMessage,
            'bsc_dashboard_markup': options.reply_markup
        }
    });
});
exports.handleBscDashboard = handleBscDashboard;
