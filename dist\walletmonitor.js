"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadPreviousValues = loadPreviousValues;
exports.startMonitoring = startMonitoring;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const user_models_1 = __importDefault(require("./models/user.models"));
const ethers_1 = require("ethers");
const web3_js_1 = require("@solana/web3.js");
// Providers
const ethProvider = new ethers_1.ethers.providers.JsonRpcProvider('https://eth.llamarpc.com');
const bscProvider = new ethers_1.ethers.providers.JsonRpcProvider('https://bsc-dataseed.binance.org/');
const solConnection = new web3_js_1.Connection('https://api.mainnet-beta.solana.com', 'confirmed');
const baseProvider = new ethers_1.ethers.providers.JsonRpcProvider('https://mainnet.base.org');
const previousValuesFilePath = path_1.default.resolve(__dirname, 'previousValues.json');
let previousValues = {};
function loadPreviousValues(bot) {
    if (fs_1.default.existsSync(previousValuesFilePath)) {
        const data = fs_1.default.readFileSync(previousValuesFilePath, 'utf-8');
        previousValues = JSON.parse(data);
    }
    else {
        initializePreviousValues(bot);
    }
}
function savePreviousValues() {
    fs_1.default.writeFileSync(previousValuesFilePath, JSON.stringify(previousValues, null, 2));
}
function initializePreviousValues(bot) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const wallets = yield fetchAndCategorizeWallets();
            for (const network of ['eth', 'bsc', 'sol', 'base']) {
                for (const wallet of wallets[network]) {
                    let balance = '';
                    if (network === 'eth') {
                        balance = yield getEthBalance(wallet.address);
                    }
                    else if (network === 'bsc') {
                        balance = yield getBscBalance(wallet.address);
                    }
                    else if (network === 'sol') {
                        balance = yield getSolBalance(wallet.address);
                    }
                    else if (network === 'base') {
                        balance = yield getBaseBalance(wallet.address);
                    }
                    const user = yield user_models_1.default.findOne({ [`${network}_wallet.address`]: wallet.address }).exec();
                    if (user) {
                        if (!previousValues[wallet.address]) {
                            previousValues[wallet.address] = {};
                        }
                        previousValues[wallet.address][network] = {
                            balance: balance.toString(),
                            chat_id: user.chat_id
                        };
                    }
                }
            }
            savePreviousValues();
        }
        catch (error) {
            console.error('Error initializing previous values:', error.message);
        }
    });
}
function fetchAndCategorizeWallets() {
    return __awaiter(this, void 0, void 0, function* () {
        const wallets = { base: [], eth: [], sol: [], bsc: [] };
        try {
            const users = yield user_models_1.default.find().exec();
            for (const user of users) {
                if (user.base_wallet) {
                    wallets.base.push(user.base_wallet);
                }
                if (user.eth_wallet) {
                    wallets.eth.push(user.eth_wallet);
                }
                if (user.sol_wallet) {
                    wallets.sol.push(user.sol_wallet);
                }
                if (user.bsc_wallet) {
                    wallets.bsc.push(user.bsc_wallet);
                }
            }
            return wallets;
        }
        catch (error) {
            console.error('Error fetching and categorizing wallets:', error.message);
            throw new Error('Failed to fetch and categorize wallets.');
        }
    });
}
function getEthBalance(address) {
    return __awaiter(this, void 0, void 0, function* () {
        const balance = yield ethProvider.getBalance(address);
        return ethers_1.ethers.utils.formatEther(balance);
    });
}
function getBscBalance(address) {
    return __awaiter(this, void 0, void 0, function* () {
        const balance = yield bscProvider.getBalance(address);
        return ethers_1.ethers.utils.formatEther(balance);
    });
}
function getSolBalance(address) {
    return __awaiter(this, void 0, void 0, function* () {
        const balance = yield solConnection.getBalance(new web3_js_1.PublicKey(address));
        return balance / 1e9;
    });
}
function getBaseBalance(address) {
    return __awaiter(this, void 0, void 0, function* () {
        const balance = yield baseProvider.getBalance(address);
        return ethers_1.ethers.utils.formatEther(balance);
    });
}
function monitorWallets(bot) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        try {
            const wallets = yield fetchAndCategorizeWallets();
            for (const network of ['eth', 'bsc', 'sol', 'base']) {
                const provider = network === 'eth' ? ethProvider :
                    network === 'bsc' ? bscProvider :
                        network === 'base' ? baseProvider : solConnection;
                for (const wallet of wallets[network]) {
                    let balance = '';
                    if (network === 'eth') {
                        balance = yield getEthBalance(wallet.address);
                    }
                    else if (network === 'bsc') {
                        balance = yield getBscBalance(wallet.address);
                    }
                    else if (network === 'sol') {
                        balance = yield getSolBalance(wallet.address);
                    }
                    else if (network === 'base') {
                        balance = yield getBaseBalance(wallet.address);
                    }
                    const previousValue = (_a = previousValues[wallet.address]) === null || _a === void 0 ? void 0 : _a[network];
                    if (!previousValue || balance.toString() !== previousValue.balance) {
                        const user = yield user_models_1.default.findOne({ [`${network}_wallet.address`]: wallet.address }).exec();
                        if (user) {
                            sendAlert(bot, wallet.address, network, balance.toString(), user.chat_id);
                            if (!previousValues[wallet.address]) {
                                previousValues[wallet.address] = {};
                            }
                            previousValues[wallet.address][network] = {
                                balance: balance.toString(),
                                chat_id: user.chat_id
                            };
                        }
                    }
                }
            }
            savePreviousValues();
        }
        catch (error) {
            console.error('Error monitoring wallet values:', error.message);
        }
    });
}
function sendAlert(bot, walletAddress, network, newBalance, chat_id) {
    const formattedBalance = parseFloat(newBalance).toFixed(6);
    const message = `Hello, ${chat_id}: your ${network.toUpperCase()} Wallet ${walletAddress} has a new balance: ${formattedBalance}`;
    bot.sendMessage(chat_id, message);
}
const POLL_INTERVAL = 10 * 1000;
function startMonitoring(bot) {
    setInterval(() => monitorWallets(bot), POLL_INTERVAL);
}
