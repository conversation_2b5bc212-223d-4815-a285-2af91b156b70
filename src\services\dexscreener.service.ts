/**
 * Dexscreener Service
 *
 * This service is a wrapper around the token info service to maintain
 * backward compatibility with existing code.
 */

import { getTokenInfoFromDexscreener as getTokenInfo } from './token/TokenInfoService';
import { BlockchainType } from './trading/TradingInterface';
import { logger } from './logger/LoggerService';

/**
 * Get token information from Dexscreener
 * @param contractAddress The token address
 * @param blockchain The blockchain type (optional, defaults to BSC)
 * @returns Token information
 */
export const getTokenInfoFromDexscreener = async (contractAddress: string, blockchain: BlockchainType = BlockchainType.BSC) => {
  try {
    // Call the new implementation
    const tokenInfo = await getTokenInfo(contractAddress, blockchain);

    // Return in the old format for backward compatibility
    return {
      symbol: tokenInfo?.symbol || 'UNKNOWN',
      name: tokenInfo?.name || 'Unknown Token',
      priceNative: tokenInfo?.priceUsd || 0,
      priceUsd: tokenInfo?.priceUsd || 0,
      chainId: blockchain,
      priceChange: tokenInfo?.priceChange || { h1: 0, h24: 0 },
      dexId: tokenInfo?.dexId || 'unknown',
      totalSupply: 0,
      pairAddress: tokenInfo?.pairAddress || 'Not available',
      marketCap: tokenInfo?.fdv || 'Not available',
      liquidity: { usd: tokenInfo?.liquidity?.usd || 0 },
      address: contractAddress
    };
  } catch (error) {
    logger.error('Error fetching token info from Dexscreener:', {
      error: (error as Error).message,
      contractAddress,
      blockchain
    });

    // Return a minimal object for backward compatibility
    return {
      symbol: 'UNKNOWN',
      name: 'Unknown Token',
      priceNative: 0,
      priceUsd: 0,
      chainId: blockchain,
      priceChange: { h1: 0, h24: 0 },
      dexId: 'unknown',
      totalSupply: 0,
      pairAddress: 'Not available',
      marketCap: 'Not available',
      liquidity: { usd: 0 },
      address: contractAddress
    };
  }
};
