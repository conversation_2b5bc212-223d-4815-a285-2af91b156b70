"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTokenInfoFromDexscreener = void 0;
const axios_1 = __importDefault(require("axios"));
// Function to get token info from Dexscreener
const getTokenInfoFromDexscreener = (contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    const url = `https://api.dexscreener.com/latest/dex/tokens/${contractAddress}`;
    try {
        const response = yield axios_1.default.get(url);
        const latestPair = response.data.pairs[0];
        if (!latestPair) {
            throw new Error('No pair information found.');
        }
        const { baseToken, priceChange } = latestPair;
        const symbol = (baseToken === null || baseToken === void 0 ? void 0 : baseToken.symbol) || 'Unknown';
        const name = (baseToken === null || baseToken === void 0 ? void 0 : baseToken.name) || 'Unknown Token';
        // Extract priceChange for m5
        const priceChangeM5 = (_a = priceChange === null || priceChange === void 0 ? void 0 : priceChange.m5) !== null && _a !== void 0 ? _a : '0'; // Default to '0' if not available
        return {
            symbol,
            name,
            priceChange: priceChangeM5, // Return only m5 priceChange as a string
            // Add other fields as needed
        };
    }
    catch (error) {
        console.error('Error fetching token info from Dexscreener:', error);
        throw new Error('Error fetching token info from Dexscreener. Please try again later.');
    }
});
exports.getTokenInfoFromDexscreener = getTokenInfoFromDexscreener;
