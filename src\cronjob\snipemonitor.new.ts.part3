/**
 * Graceful shutdown
 */
async function gracefulShutdown(): Promise<void> {
  logger.info('Starting graceful shutdown');

  try {
    // Stop accepting new connections
    server.close(() => {
      logger.info('HTTP server closed');
    });

    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown', {
      error: (error as Error).message
    });
    process.exit(1);
  }
}

/**
 * Send snipe data to a socket
 * @param socket Socket.IO socket
 */
const sendSnipeData = async (socket: any): Promise<void> => {
  try {
    const snipes = await Snipe.find({
      'config.isConfigured': true,
      'config.isBought': false
    }).lean();

    socket.emit('snipe_data', { snipes });

    logger.debug(`Sent snipe data to client`, {
      socketId: socket.id,
      snipeCount: snipes.length
    });
  } catch (error) {
    logger.error('Error sending snipe data', {
      error: (error as Error).message,
      socketId: socket.id
    });
  }
};
