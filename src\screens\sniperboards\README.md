# Sniperboards

This directory contains the implementation of sniper screens for different blockchains.

## Files

- `bscboard.ts` - Binance Smart Chain sniper board
- `ethboard.ts` - Ethereum sniper board
- `baseboard.ts` - Base network sniper board
- `solboard.ts` - Solana sniper board

## Usage

Import the sniper screens directly from this directory:

```typescript
import { bscSniperScreen } from '../screens/sniperboards/bscboard';
import { ethSniperScreen } from '../screens/sniperboards/ethboard';
import { baseSniperScreen } from '../screens/sniperboards/baseboard';
import { solSniperScreen } from '../screens/sniperboards/solboard';
```

## Note

The old `src/screens/sniper` directory has been removed, and all functionality has been consolidated into this directory.
