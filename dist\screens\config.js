"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.showConfigKeyboard = void 0;
const events_1 = __importDefault(require("events"));
const user_models_1 = __importDefault(require("../models/user.models"));
const configUpdateEmitter = new events_1.default();
const createConfigKeyboard = (config) => ({
    inline_keyboard: [
        [
            { text: `Slippage: ${config.slippage}`, callback_data: JSON.stringify({ command: 'set_slippage_base' }) },
            { text: `Max Gas Price: ${config.maxGasPrice}`, callback_data: JSON.stringify({ command: 'set_max_gas_price_base' }) },
        ],
        [
            { text: `Max Gas Limit: ${config.maxGasLimit}`, callback_data: JSON.stringify({ command: 'set_max_gas_limit_base' }) },
            { text: `Min Liquidity: ${config.minLiquidity}`, callback_data: JSON.stringify({ command: 'set_min_liquidity_base' }) },
        ],
        [
            { text: `Max Market Cap: ${config.maxMarketCap}`, callback_data: JSON.stringify({ command: 'set_max_market_cap_base' }) },
            { text: `Max Liquidity: ${config.maxLiquidity}`, callback_data: JSON.stringify({ command: 'set_max_liquidity_base' }) },
        ],
        [
            { text: `Auto Buy: ${config.autoBuy ? '✅' : '❌'}`, callback_data: JSON.stringify({ command: 'toggle_auto_buy_base', state: config.autoBuy }) },
            { text: `Auto Sell: ${config.autoSell ? '✅' : '❌'}`, callback_data: JSON.stringify({ command: 'toggle_auto_sell_base', state: config.autoSell }) },
        ],
        [
            { text: `Auto Approve: ${config.autoApprove ? '✅' : '❌'}`, callback_data: JSON.stringify({ command: 'toggle_auto_approve_base', state: config.autoApprove }) },
            { text: '⬅️ Back', callback_data: JSON.stringify({ command: 'back_to_main_snipe_base' }) },
        ],
        [
            {
                text: "* Dismiss message",
                callback_data: JSON.stringify({
                    command: "dismiss_message",
                }),
            },
        ]
    ],
});
const updateConfig = (bot, chatId, updates) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user)
            throw new Error('User not found.');
        const updatedConfigField = `${user.currentBlockchain}_config`;
        const updatedConfig = Object.assign(Object.assign({}, user[updatedConfigField]), updates);
        const configUpdate = {
            [updatedConfigField]: updatedConfig,
        };
        yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: configUpdate }).exec();
        configUpdateEmitter.emit('configUpdated', chatId, updatedConfig, bot);
        // Send a success message
        const successMessage = yield bot.sendMessage(chatId, '✅ Configuration updated successfully.');
        // Delete the success message after 5 seconds
        setTimeout(() => __awaiter(void 0, void 0, void 0, function* () {
            try {
                yield bot.deleteMessage(chatId, successMessage.message_id);
            }
            catch (error) {
                console.error('Failed to delete success message:', error);
            }
        }), 5000);
    }
    catch (error) {
        console.error('Error updating configuration:', error);
        yield bot.sendMessage(chatId, '❌ An error occurred while updating your wallet configurations.');
    }
});
// Event listener for updating the keyboard
configUpdateEmitter.on('configUpdated', (chatId, updatedConfig, bot) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user)
            throw new Error('User not found.');
        const keyboard = createConfigKeyboard(updatedConfig);
        const messageId = user[`${user.currentBlockchain}_dashboard_message_id`];
        yield bot.editMessageReplyMarkup(keyboard, {
            chat_id: chatId,
            message_id: messageId,
        });
    }
    catch (error) {
        console.error('Error updating the configuration keyboard:', error);
    }
}));
const promptMessageIdStore = {};
const askForConfigInput = (bot, chatId, promptMessage, field) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (promptMessageIdStore[chatId]) {
            const ids = promptMessageIdStore[chatId];
            if (ids.promptId) {
                try {
                    yield bot.deleteMessage(chatId, ids.promptId);
                }
                catch (error) {
                    console.error('Failed to delete previous prompt message:', error);
                }
            }
            if (ids.replyId) {
                try {
                    yield bot.deleteMessage(chatId, ids.replyId);
                }
                catch (error) {
                    console.error('Failed to delete previous reply message:', error);
                }
            }
        }
        const sentMessage = yield bot.sendMessage(chatId, promptMessage, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log(`Received ${field}:`, msg.text);
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error(`Invalid ${field}.`));
                }
            }));
        });
    }
    catch (error) {
        console.error(`Failed to ask for ${field}:`, error);
        throw error;
    }
});
const showConfigKeyboard = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user)
            throw new Error('User not found.');
        const configField = `${user.currentBlockchain}_config`;
        const config = user[configField];
        const keyboard = createConfigKeyboard(config);
        const sentMessage = yield bot.sendMessage(chatId, 'Please select the configuration option to edit:', {
            reply_markup: keyboard,
        });
        user[`${user.currentBlockchain}_dashboard_message_id`] = sentMessage.message_id;
        yield user.save();
        bot.on('callback_query', (callbackQuery) => __awaiter(void 0, void 0, void 0, function* () {
            const { message, data } = callbackQuery;
            const chatId = message === null || message === void 0 ? void 0 : message.chat.id;
            const messageId = message === null || message === void 0 ? void 0 : message.message_id;
            if (!chatId || !data)
                return;
            const parsedData = JSON.parse(data);
            const command = parsedData.command;
            try {
                switch (command) {
                    case 'set_slippage_base':
                        const slippage = yield askForConfigInput(bot, chatId, 'Enter new slippage percentage:', 'slippage');
                        yield updateConfig(bot, chatId, { slippage: parseFloat(slippage) });
                        break;
                    case 'set_max_gas_price_base':
                        const maxGasPrice = yield askForConfigInput(bot, chatId, 'Enter new max gas price:', 'maxGasPrice');
                        yield updateConfig(bot, chatId, { maxGasPrice: parseFloat(maxGasPrice) });
                        break;
                    case 'set_max_gas_limit_base':
                        const maxGasLimit = yield askForConfigInput(bot, chatId, 'Enter new max gas limit:', 'maxGasLimit');
                        yield updateConfig(bot, chatId, { maxGasLimit: parseFloat(maxGasLimit) });
                        break;
                    case 'set_min_liquidity_base':
                        const minLiquidity = yield askForConfigInput(bot, chatId, 'Enter new min liquidity:', 'minLiquidity');
                        yield updateConfig(bot, chatId, { minLiquidity: parseFloat(minLiquidity) });
                        break;
                    case 'set_max_market_cap_base':
                        const maxMarketCap = yield askForConfigInput(bot, chatId, 'Enter new max market cap:', 'maxMarketCap');
                        yield updateConfig(bot, chatId, { maxMarketCap: parseFloat(maxMarketCap) });
                        break;
                    case 'set_max_liquidity_base':
                        const maxLiquidity = yield askForConfigInput(bot, chatId, 'Enter new max liquidity:', 'maxLiquidity');
                        yield updateConfig(bot, chatId, { maxLiquidity: parseFloat(maxLiquidity) });
                        break;
                    case 'toggle_auto_buy_base':
                        const newAutoBuyState = !parsedData.state;
                        yield updateConfig(bot, chatId, { autoBuy: newAutoBuyState });
                        break;
                    case 'toggle_auto_sell_base':
                        const newAutoSellState = !parsedData.state;
                        yield updateConfig(bot, chatId, { autoSell: newAutoSellState });
                        break;
                    case 'toggle_auto_approve_base':
                        const newAutoApproveState = !parsedData.state;
                        yield updateConfig(bot, chatId, { autoApprove: newAutoApproveState });
                        break;
                    case 'back_to_main_snipe_base':
                        // Handle navigation to the main snipe menu
                        break;
                    default:
                    //   await bot.sendMessage(chatId, 'Unknown command.');
                }
                // Acknowledge the callback query
                yield bot.answerCallbackQuery(callbackQuery.id);
            }
            catch (error) {
                console.error('Error handling callback query:', error);
                yield bot.sendMessage(chatId, '❌ An error occurred while processing your request.');
            }
        }));
    }
    catch (error) {
        console.error('Failed to show configuration keyboard:', error);
        yield bot.sendMessage(chatId, '❌ An error occurred while displaying the configuration keyboard.');
    }
});
exports.showConfigKeyboard = showConfigKeyboard;
