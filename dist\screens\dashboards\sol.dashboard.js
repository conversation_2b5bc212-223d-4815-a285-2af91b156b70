"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleSolDashboard = void 0;
const user_models_1 = __importDefault(require("../../models/user.models"));
const solscan_service_1 = require("../../services/solscan.service");
const handleSolDashboard = (bot, chat_id) => __awaiter(void 0, void 0, void 0, function* () {
    var _a;
    try {
        const user = yield user_models_1.default.findOne({ chat_id });
        if (!user) {
            yield bot.sendMessage(chat_id, 'User not found.');
            return;
        }
        const firstName = user.first_name || 'User';
        const solWallet = ((_a = user.sol_wallet) === null || _a === void 0 ? void 0 : _a.address) || 'Not set';
        const balance = solWallet !== 'Not set' ? yield (0, solscan_service_1.getSolBalance)(solWallet) : '0.0 sol';
        const welcomeMessage = `👋 Welcome, ${firstName}, to your sol Dashboard!
\n📍 **Address:** \`${solWallet}\`
\n🔗 **Blockchain:** Solana
\n💰 **Balance:** \`${balance} SOL\`
\n⚙️ **Settings:** [Antirug, etc.]`;
        const options = {
            parse_mode: 'Markdown',
            reply_markup: {
                inline_keyboard: [
                    [
                        { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_sol' }) }
                    ],
                    [
                        { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
                        { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_sol' }) }
                    ],
                    [
                        { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_sol' }) },
                        { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_sol_wallet' }) }
                    ],
                    [
                        {
                            text: "* Dismiss message",
                            callback_data: JSON.stringify({
                                command: "dismiss_message",
                            }),
                        },
                    ]
                ]
            }
        };
        const sentMessage = yield bot.sendMessage(chat_id, welcomeMessage, options);
        yield user_models_1.default.updateOne({ chat_id: chat_id }, {
            $set: {
                'sol_dashboard_message_id': sentMessage.message_id,
                'sol_dashboard_content': welcomeMessage,
                'sol_dashboard_markup': options.reply_markup
            }
        });
    }
    catch (error) {
        console.error('Error handling Sol dashboard:', error);
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        yield bot.sendMessage(chat_id, `An error occurred while loading the Sol dashboard: ${errorMessage}`);
    }
});
exports.handleSolDashboard = handleSolDashboard;
