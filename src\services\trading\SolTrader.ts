/**
 * Solana Trader Implementation
 *
 * This module implements the BlockchainTrader interface for the Solana network.
 */

import TelegramBot from 'node-telegram-bot-api';
import {
  Connection,
  PublicKey,
  Keypair,
  VersionedTransaction,
  TransactionInstruction,
  LAMPORTS_PER_SOL
} from '@solana/web3.js';
import {
  TOKEN_PROGRAM_ID,
  getMint
} from '@solana/spl-token';
import {
  BaseTrader,
  BlockchainType,
  TokenInfo,
  TradeOptions,
  TradeResult,
  WalletInfo
} from './TradingInterface';
import { logger } from '../logger/LoggerService';
import { generateSOLWallet } from '../wallet.service';
import { checkIfHoneypot } from '../honeypot/UnifiedHoneypotChecker';
import { getTokenInfoFromDexscreener } from '../dexscreener.service';
import bs58 from 'bs58';
import fetch from 'cross-fetch';

/**
 * Solana-specific configuration
 */
export const SOL_CONFIG = {
  rpcUrl: process.env.SOL_PROVIDER_URL || 'https://api.mainnet-beta.solana.com',
  adminPublicKey: process.env.SOL_ADMIN_PUBLIC_KEY || '',
  solanaAddress: 'So11111111111111111111111111111111111111112', // Native SOL token address
  explorerUrl: 'https://solscan.io',
  defaultSlippage: 100, // 1% (in basis points)
  jupiterApiUrl: 'https://quote-api.jup.ag/v6',
};

/**
 * Solana Trader implementation
 */
export class SolTrader extends BaseTrader {
  private connection: Connection;

  constructor(bot: TelegramBot) {
    super(bot);
    this.connection = new Connection(SOL_CONFIG.rpcUrl);
    logger.info('SOL Trader initialized', { rpcUrl: SOL_CONFIG.rpcUrl });
  }

  /**
   * Get the blockchain type
   */
  getBlockchainType(): BlockchainType {
    return BlockchainType.SOL;
  }

  /**
   * Buy tokens on Solana using Jupiter aggregator
   * @param tokenAddress The address of the token to buy
   * @param amount The amount of SOL to spend
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async buyTokens(
    tokenAddress: string,
    amount: string,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Buying tokens on Solana', {
      tokenAddress,
      amount,
      walletAddress,
      options
    });

    try {
      // Get the wallet's private key
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a keypair from the private key
      const secretKey = bs58.decode(privateKey);
      const wallet = Keypair.fromSecretKey(secretKey);

      // Convert amount to lamports
      const amountInLamports = BigInt(Math.round(parseFloat(amount) * LAMPORTS_PER_SOL));

      // Calculate fee amount (1% fee)
      const feeAmount = parseFloat(amount) * 0.01;
      const feeInLamports = BigInt(Math.round(feeAmount * LAMPORTS_PER_SOL));

      // Set up slippage
      const slippageBps = options?.slippage ? options.slippage * 100 : SOL_CONFIG.defaultSlippage;

      // Get quote from Jupiter
      const quoteData = await this.getJupiterQuote(
        SOL_CONFIG.solanaAddress, // Input mint (SOL)
        tokenAddress, // Output mint (token to buy)
        amountInLamports,
        slippageBps
      );

      // Get swap transaction
      const swapTransaction = await this.getJupiterSwapTransaction(
        quoteData,
        wallet.publicKey.toString()
      );

      // Deserialize the transaction
      const swapTransactionBuf = Buffer.from(swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(swapTransactionBuf);

      // Add fee transfer instruction if admin public key is set
      if (SOL_CONFIG.adminPublicKey) {
        // In a real implementation, we would add a fee transfer instruction here
        // For now, we'll just log it
        logger.info('Would add fee transfer instruction', {
          feeAmount,
          adminPublicKey: SOL_CONFIG.adminPublicKey
        });
      }

      // Sign the transaction
      transaction.sign([wallet]);

      // Send the transaction
      const txid = await this.executeTransaction(transaction);

      logger.info('SOL buy transaction successful', {
        transactionHash: txid,
        tokenAddress
      });

      // Log performance
      logger.logPerformance('SOL buyTokens', startTime, {
        tokenAddress,
        transactionHash: txid
      });

      return {
        success: true,
        transactionHash: txid,
        amountIn: amount,
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('SOL buy transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Sell tokens on Solana using Jupiter aggregator
   * @param tokenAddress The address of the token to sell
   * @param percentage The percentage of tokens to sell (0-100)
   * @param walletAddress The wallet address to use
   * @param options Additional trade options
   */
  async sellTokens(
    tokenAddress: string,
    percentage: number,
    walletAddress: string,
    options?: TradeOptions
  ): Promise<TradeResult> {
    const startTime = Date.now();
    logger.info('Selling tokens on Solana', {
      tokenAddress,
      percentage,
      walletAddress,
      options
    });

    try {
      // Validate percentage
      if (percentage <= 0 || percentage > 100) {
        throw new Error('Percentage must be between 1 and 100');
      }

      // Get the wallet's private key
      const privateKey = await this.getPrivateKey(walletAddress);
      if (!privateKey) {
        throw new Error('Private key not found for wallet');
      }

      // Create a keypair from the private key
      const secretKey = bs58.decode(privateKey);
      const wallet = Keypair.fromSecretKey(secretKey);

      // Get token balance
      const tokenBalanceStr = await this.getTokenBalance(tokenAddress, walletAddress);
      if (tokenBalanceStr === '0') {
        throw new Error('No token balance found');
      }

      // Get token decimals
      const tokenPublicKey = new PublicKey(tokenAddress);
      const tokenMint = await getMint(this.connection, tokenPublicKey);
      const decimals = tokenMint.decimals;

      // Calculate amount to sell based on percentage
      const tokenBalance = parseFloat(tokenBalanceStr);
      const amountToSell = BigInt(Math.floor(tokenBalance * Math.pow(10, decimals) * percentage / 100));

      if (amountToSell <= BigInt(0)) {
        throw new Error('No tokens to sell');
      }

      // Calculate fee amount (1% fee in tokens)
      const feeAmountInTokens = Number(amountToSell) * 0.01;
      const remainingTokens = amountToSell - BigInt(Math.floor(feeAmountInTokens));

      // Set up slippage
      const slippageBps = options?.slippage ? options.slippage * 100 : SOL_CONFIG.defaultSlippage;

      // Get quote from Jupiter
      const quoteData = await this.getJupiterQuote(
        tokenAddress, // Input mint (token to sell)
        SOL_CONFIG.solanaAddress, // Output mint (SOL)
        remainingTokens,
        slippageBps
      );

      // Get swap transaction
      const swapTransaction = await this.getJupiterSwapTransaction(
        quoteData,
        wallet.publicKey.toString()
      );

      // Deserialize the transaction
      const swapTransactionBuf = Buffer.from(swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(swapTransactionBuf);

      // Add fee transfer instruction if admin public key is set
      if (SOL_CONFIG.adminPublicKey) {
        // In a real implementation, we would add a fee transfer instruction here
        // For now, we'll just log it
        logger.info('Would add fee transfer instruction', {
          feeAmountInTokens,
          adminPublicKey: SOL_CONFIG.adminPublicKey
        });
      }

      // Sign the transaction
      transaction.sign([wallet]);

      // Send the transaction
      const txid = await this.executeTransaction(transaction);

      logger.info('SOL sell transaction successful', {
        transactionHash: txid,
        tokenAddress,
        percentage
      });

      // Log performance
      logger.logPerformance('SOL sellTokens', startTime, {
        tokenAddress,
        transactionHash: txid,
        percentage
      });

      return {
        success: true,
        transactionHash: txid,
        amountIn: amountToSell.toString(),
        timestamp: Date.now()
      };
    } catch (error: any) {
      logger.error('SOL sell transaction failed', {
        error: error.message,
        tokenAddress,
        walletAddress,
        percentage
      });

      return {
        success: false,
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get token information
   * @param tokenAddress The address of the token
   */
  async getTokenInfo(tokenAddress: string): Promise<TokenInfo> {
    try {
      // Get token mint info
      const mintInfo = await this.connection.getTokenSupply(new PublicKey(tokenAddress));

      // Get additional information from Dexscreener
      const dexInfo = await getTokenInfoFromDexscreener(tokenAddress, BlockchainType.SOL);

      // Check if token is a honeypot
      const honeypotInfo = await this.checkHoneypot(tokenAddress);

      return {
        name: dexInfo?.name || 'Unknown',
        symbol: dexInfo?.symbol || 'UNKNOWN',
        decimals: mintInfo.value.decimals,
        totalSupply: mintInfo.value.amount,
        address: tokenAddress,
        blockchain: BlockchainType.SOL,
        price: typeof dexInfo?.priceUsd === 'string' ? parseFloat(dexInfo.priceUsd) : (dexInfo?.priceUsd || 0),
        marketCap: typeof dexInfo?.marketCap === 'string' ? parseFloat(dexInfo.marketCap) : (dexInfo?.marketCap || 0),
        isHoneypot: honeypotInfo?.isHoneypot || false,
        honeypotInfo
      };
    } catch (error: any) {
      logger.error('Failed to get SOL token info', {
        error: error.message,
        tokenAddress
      });

      // Return minimal information
      return {
        name: 'Unknown',
        symbol: 'UNKNOWN',
        decimals: 9,
        address: tokenAddress,
        blockchain: BlockchainType.SOL
      };
    }
  }

  /**
   * Get wallet balance
   * @param walletAddress The wallet address
   */
  async getWalletBalance(walletAddress: string): Promise<string> {
    try {
      const publicKey = new PublicKey(walletAddress);
      const balance = await this.connection.getBalance(publicKey);
      return (balance / LAMPORTS_PER_SOL).toFixed(9);
    } catch (error: any) {
      logger.error('Failed to get SOL wallet balance', {
        error: error.message,
        walletAddress
      });
      return '0';
    }
  }

  /**
   * Check if a token is a honeypot
   * @param tokenAddress The address of the token
   */
  async checkHoneypot(tokenAddress: string): Promise<any> {
    try {
      return await checkIfHoneypot(tokenAddress, BlockchainType.SOL);
    } catch (error: any) {
      logger.error('Failed to check if SOL token is honeypot', {
        error: error.message,
        tokenAddress
      });
      return { isHoneypot: false, message: 'Failed to check' };
    }
  }

  /**
   * Generate a new SOL wallet
   */
  async generateWallet(): Promise<WalletInfo> {
    try {
      const wallet = generateSOLWallet();
      const balance = await this.getWalletBalance(wallet.address);

      return {
        ...wallet,
        balance,
        blockchain: BlockchainType.SOL
      };
    } catch (error: any) {
      logger.error('Failed to generate SOL wallet', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Get a wallet's private key (this would be implemented securely in production)
   * @param walletAddress The wallet address
   */
  private async getPrivateKey(walletAddress: string): Promise<string | null> {
    // In a real implementation, you would retrieve this securely from a database
    // For now, we'll return null and assume it's handled elsewhere
    return null;
  }

  /**
   * Get a quote from Jupiter
   * @param inputMint The input token mint address
   * @param outputMint The output token mint address
   * @param amount The amount to swap
   * @param slippageBps The slippage in basis points
   */
  private async getJupiterQuote(
    inputMint: string,
    outputMint: string,
    amount: bigint,
    slippageBps: number
  ): Promise<any> {
    const quoteResponse = await fetch(
      `${SOL_CONFIG.jupiterApiUrl}/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}`
    );

    const quoteData = await quoteResponse.json();
    if (!quoteData) {
      throw new Error('Failed to get quote data from Jupiter');
    }

    return quoteData;
  }

  /**
   * Get a swap transaction from Jupiter
   * @param quoteData The quote data from Jupiter
   * @param userPublicKey The user's public key
   */
  private async getJupiterSwapTransaction(quoteData: any, userPublicKey: string): Promise<string> {
    const swapResponse = await fetch(`${SOL_CONFIG.jupiterApiUrl}/swap`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        quoteResponse: quoteData,
        userPublicKey: userPublicKey,
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true,
        prioritizationFeeLamports: 2000000, // 0.002 SOL to prioritize the transaction
        autoMultiplier: 2,
      })
    });

    const swapData = await swapResponse.json();
    if (!swapData || !swapData.swapTransaction) {
      throw new Error('Failed to get swap transaction data from Jupiter');
    }

    return swapData.swapTransaction;
  }

  /**
   * Execute a transaction
   * @param transaction The transaction to execute
   */
  private async executeTransaction(transaction: VersionedTransaction): Promise<string> {
    const rawTransaction = transaction.serialize();
    const txid = await this.connection.sendRawTransaction(rawTransaction, {
      skipPreflight: true,
      maxRetries: 3,
    });

    // Wait for confirmation
    const confirmation = await this.connection.confirmTransaction(txid, 'confirmed');
    if (confirmation.value.err) {
      throw new Error(`Transaction failed: ${confirmation.value.err.toString()}`);
    }

    return txid;
  }

  /**
   * Get token balance
   * @param tokenAddress The token address
   * @param walletAddress The wallet address
   */
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string> {
    try {
      const publicKey = new PublicKey(walletAddress);
      const tokenPublicKey = new PublicKey(tokenAddress);

      // Find the token account
      const tokenAccounts = await this.connection.getParsedTokenAccountsByOwner(
        publicKey,
        { mint: tokenPublicKey }
      );

      if (tokenAccounts.value.length === 0) {
        return '0';
      }

      const tokenAccount = tokenAccounts.value[0];
      const parsedInfo = tokenAccount.account.data.parsed.info;
      const amount = Number(parsedInfo.tokenAmount.amount);
      const decimals = parsedInfo.tokenAmount.decimals;

      // Format the balance as a string with the correct number of decimals
      return (amount / Math.pow(10, decimals)).toString();
    } catch (error) {
      logger.error('Failed to get token balance', {
        error: (error as Error).message,
        tokenAddress,
        walletAddress
      });
      return '0';
    }
  }
}
