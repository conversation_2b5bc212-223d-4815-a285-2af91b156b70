/**
 * Provider Service
 *
 * This service manages blockchain providers with fallback URLs and retry logic.
 */

import { ethers } from 'ethers';
import { logger } from '../logger/LoggerService';
import { cacheService } from '../cache/RedisService';

// Provider status cache keys
const PROVIDER_STATUS_CACHE_PREFIX = 'provider:status:';
const PROVIDER_CACHE_TTL = 300; // 5 minutes

// Fallback URLs for different blockchains
const FALLBACK_URLS = {
  bsc: [
    'https://bsc-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3',
    'https://bsc-dataseed1.defibit.io/',
    'https://bsc-dataseed1.ninicoin.io/',
    'https://bsc-dataseed2.defibit.io/',
    'https://bsc-dataseed3.defibit.io/',
    'https://bsc-dataseed4.defibit.io/',
    'https://rpc.ankr.com/bsc'
  ],
  eth: [
    'https://eth.llamarpc.com',
    'https://mainnet.infura.io/v3/********************************',
    'https://rpc.ankr.com/eth',
    'https://ethereum.publicnode.com',
    'https://eth-mainnet.public.blastapi.io'
  ],
  base: [
    'https://mainnet.base.org',
    'https://base-mainnet.g.alchemy.com/v2/6O-PggabiAzJuhB3Qxw7vrO5eTpEH5rx/',
    'https://base.blockpi.network/v1/rpc/public',
    'https://base-rpc.publicnode.com',
    'https://1rpc.io/base'
  ],
  sol: [
    'https://api.mainnet-beta.solana.com',
    'https://solana-mainnet.g.alchemy.com/v2/demo',
    'https://rpc.ankr.com/solana',
    'https://solana-api.projectserum.com'
  ]
};

// Chain IDs for EVM chains
const CHAIN_IDS = {
  eth: 1,
  bsc: 56,
  base: 8453
};

// Provider interface
interface ProviderInfo {
  provider: ethers.providers.JsonRpcProvider;
  url: string;
  isHealthy: boolean;
  lastChecked: Date;
  failCount: number;
}

/**
 * Provider Manager class
 */
export class ProviderManager {
  private providers: Map<string, ProviderInfo[]> = new Map();
  private currentProviders: Map<string, ProviderInfo> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeProviders();
    this.startHealthCheck();
  }

  /**
   * Initialize providers for all supported blockchains
   */
  private initializeProviders(): void {
    // Initialize BSC providers
    this.initializeChainProviders('bsc', process.env.BSC_PROVIDER_URL);

    // Initialize ETH providers
    this.initializeChainProviders('eth', process.env.ETH_PROVIDER_URL);

    // Initialize Base providers
    this.initializeChainProviders('base', process.env.BASE_PROVIDER_URL);

    logger.info('Provider Manager initialized', {
      chains: Array.from(this.providers.keys())
    });
  }

  /**
   * Initialize providers for a specific blockchain
   * @param chain The blockchain name
   * @param primaryUrl The primary URL from environment variables
   */
  private initializeChainProviders(chain: string, primaryUrl?: string): void {
    const providers: ProviderInfo[] = [];
    const fallbacks = FALLBACK_URLS[chain as keyof typeof FALLBACK_URLS] || [];
    const chainId = CHAIN_IDS[chain as keyof typeof CHAIN_IDS];

    // Add primary URL if provided
    if (primaryUrl) {
      const provider = this.createProvider(primaryUrl, chainId);
      providers.push({
        provider,
        url: primaryUrl,
        isHealthy: true,
        lastChecked: new Date(),
        failCount: 0
      });
    }

    // Add fallback URLs
    for (const url of fallbacks) {
      // Skip if this URL is already added as primary
      if (url === primaryUrl) continue;

      const provider = this.createProvider(url, chainId);
      providers.push({
        provider,
        url,
        isHealthy: true,
        lastChecked: new Date(),
        failCount: 0
      });
    }

    this.providers.set(chain, providers);

    // Set current provider to the first one
    if (providers.length > 0) {
      this.currentProviders.set(chain, providers[0]);
    }
  }

  /**
   * Create a provider with error handling
   * @param url The RPC URL
   * @param chainId Optional chain ID
   * @returns The provider instance
   */
  private createProvider(url: string, chainId?: number): ethers.providers.JsonRpcProvider {
    const provider = new ethers.providers.JsonRpcProvider(url, chainId);

    // Add error handling
    provider.on('error', (error) => {
      logger.warn(`Provider error for ${url}:`, {
        error: error.message,
        url
      });
    });

    return provider;
  }

  /**
   * Start health check interval
   */
  private startHealthCheck(): void {
    // Check every 2 minutes
    this.healthCheckInterval = setInterval(() => {
      this.checkAllProviders();
    }, 2 * 60 * 1000);
  }

  /**
   * Check health of all providers
   */
  private async checkAllProviders(): Promise<void> {
    for (const [chain, providers] of this.providers.entries()) {
      await this.checkProvidersForChain(chain, providers);
    }
  }

  /**
   * Check health of providers for a specific chain
   * @param chain The blockchain name
   * @param providers The providers to check
   */
  private async checkProvidersForChain(chain: string, providers: ProviderInfo[]): Promise<void> {
    let foundHealthy = false;

    for (const providerInfo of providers) {
      const isHealthy = await this.checkProviderHealth(providerInfo.provider, providerInfo.url);

      // Update provider info
      providerInfo.isHealthy = isHealthy;
      providerInfo.lastChecked = new Date();

      if (isHealthy) {
        providerInfo.failCount = 0;
        foundHealthy = true;

        // If current provider is unhealthy, switch to this one
        const currentProvider = this.currentProviders.get(chain);
        if (currentProvider && !currentProvider.isHealthy) {
          this.currentProviders.set(chain, providerInfo);
          logger.info(`Switched to healthy provider for ${chain}`, {
            url: providerInfo.url
          });
        }
      } else {
        providerInfo.failCount++;
      }
    }

    // If no healthy provider found, log warning
    if (!foundHealthy) {
      logger.warn(`No healthy provider found for ${chain}`);
    }

    // Update cache with provider status
    await cacheService.set(
      `${PROVIDER_STATUS_CACHE_PREFIX}${chain}`,
      {
        chain,
        providers: providers.map(p => ({
          url: p.url,
          isHealthy: p.isHealthy,
          failCount: p.failCount,
          lastChecked: p.lastChecked
        })),
        currentProvider: this.currentProviders.get(chain)?.url
      },
      PROVIDER_CACHE_TTL
    );
  }

  /**
   * Check health of a provider
   * @param provider The provider to check
   * @param url The provider URL
   * @returns True if healthy, false otherwise
   */
  private async checkProviderHealth(
    provider: ethers.providers.JsonRpcProvider,
    url: string
  ): Promise<boolean> {
    try {
      // Try to get network info
      const network = await provider.getNetwork();
      return network.chainId > 0;
    } catch (error) {
      logger.warn(`Provider health check failed for ${url}`, {
        error: (error as Error).message,
        url
      });
      return false;
    }
  }

  /**
   * Get the current provider for a blockchain
   * @param chain The blockchain name
   * @returns The provider or null if not available
   */
  public getProvider(chain: string): ethers.providers.JsonRpcProvider | null {
    const providerInfo = this.currentProviders.get(chain);
    return providerInfo ? providerInfo.provider : null;
  }

  /**
   * Get all providers for a blockchain
   * @param chain The blockchain name
   * @returns Array of providers
   */
  public getAllProviders(chain: string): ethers.providers.JsonRpcProvider[] {
    const providers = this.providers.get(chain) || [];
    return providers.map(p => p.provider);
  }

  /**
   * Shutdown the provider manager
   */
  public shutdown(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }
}

// Create singleton instance
export const providerManager = new ProviderManager();
