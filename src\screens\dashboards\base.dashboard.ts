import TelegramBot, { SendMessageOptions } from 'node-telegram-bot-api';
import { ethers } from 'ethers';
import User from '../../models/user.models';

export const getBaseBalance = async (address: string): Promise<string> => {
  const providerUrl = process.env.BASE_PROVIDER_URL;

  if (!providerUrl) {
    throw new Error('Provider is not defined in the .env file');
  }

  const providerbase = new ethers.providers.JsonRpcProvider(providerUrl);
  const balanceBASE = await providerbase.getBalance(address);
  return parseFloat(ethers.utils.formatEther(balanceBASE)).toFixed(6);
};


export const handleBaseDashboard = async (bot: TelegramBot, chat_id: number) => {
  const user = await User.findOne({ chat_id });

  if (!user) {
    bot.sendMessage(chat_id, 'User not found.');
    return;
  }

  const firstName = user.first_name || 'User';
  const baseWallet = user.base_wallet?.address || 'Not set';
  const balance = baseWallet !== 'Not set' ? await getBaseBalance(baseWallet) : '0.0 BNB';

  const welcomeMessage = `👋 Welcome, ${firstName}, to your BASE Dashboard!
\n📍 **Address:** \`${baseWallet}\`
\n🔗 **Blockchain:** base
\n💰 **Balance:** \`${balance} BASE\`
\n⚙️ **Settings:** [Antirug, etc.]`;

  const options: SendMessageOptions = {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '🔍 Scan Contract', callback_data: JSON.stringify({ command: 'scan_contract_base' }) }
        ],
        [
          { text: '⚙️ Config', callback_data: JSON.stringify({ command: 'show_config' }) },
          { text: '📊 Active Trades', callback_data: JSON.stringify({ command: 'active_trades_base' }) }
        ],
        [
          { text: '🔄 Refresh Wallet', callback_data: JSON.stringify({ command: 'refresh_wallet_base' }) },
          { text: '➕ Generate New Wallet', callback_data: JSON.stringify({ command: 'generate_new_base_wallet' }) }
        ],
        [
          {
            text: "* Dismiss message",
            callback_data: JSON.stringify({
              command: "dismiss_message",
            }),
          },
        ]
      ]
    }
  };

  const sentMessage = await bot.sendMessage(chat_id, welcomeMessage, options);

  await User.updateOne({ chat_id: chat_id }, {
    $set: {
      'base_dashboard_message_id': sentMessage.message_id,
      'base_dashboard_content': welcomeMessage,
      'base_dashboard_markup': options.reply_markup
    }
  });
};
