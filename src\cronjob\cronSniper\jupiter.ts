import {
    Connection,
    PublicKey,
    Keypair,
    VersionedTransaction,
    SystemProgram,
    TransactionInstruction,
    sendAndConfirmRawTransaction,
    LAMPORTS_PER_SOL,
    AddressLookupTableAccount,
    TransactionMessage,
  } from '@solana/web3.js';
  import {
    getOrCreateAssociatedTokenAccount,
    createTransferInstruction,
    TOKEN_PROGRAM_ID,
  } from '@solana/spl-token';
  import fetch from 'cross-fetch';
  import bs58 from 'bs58';
  import Snipe from '../../models/snipe.model'; 
  import User from '../../models/user.models'; 
  import Trade from '../../models/trade.model'; 
  import { getCurrentContractAddress } from '../../globalState';
  import dotenv from 'dotenv';
dotenv.config();


  const RPC_ENDPOINT = process.env.SOL_PROVIDER_URL || '';
  const ADMIN_PUBLIC_KEY_STRING = process.env.SOL_ADMIN_PUBLIC_KEY || '';
  const SOLANA_ADDRESS = 'So11111111111111111111111111111111111111112';
  
  if (!RPC_ENDPOINT || !ADMIN_PUBLIC_KEY_STRING) {
    throw new Error('Environment variables SOL_PROVIDER_URL and ADMIN_PUBLIC_KEY must be set');
  }
  
  const connection = new Connection(RPC_ENDPOINT);
  

  
  async function getQuote(inputMint: string, outputMint: string, amount: bigint, slippageBps: number) {
    const quoteResponse = await fetch(`https://quote-api.jup.ag/v6/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}`);
    const quoteData = await quoteResponse.json();
    if (!quoteData) {
      throw new Error('Failed to get quote data');
    }
    return quoteData;
  }
  
  async function getSwapTransaction(quoteData: any, userPublicKey: string) {
    const swapResponse = await fetch('https://quote-api.jup.ag/v6/swap', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        quoteResponse: quoteData,
        userPublicKey: userPublicKey,
        wrapAndUnwrapSol: true,
        dynamicComputeUnitLimit: true,
        prioritizationFeeLamports: 2000000, // Set to 0.002 SOL to bribe the miners
        autoMultiplier: 2,
      })
    });
    const swapData = await swapResponse.json();
    if (!swapData || !swapData.swapTransaction) {
      throw new Error('Failed to get swap transaction data');
    }
    return swapData.swapTransaction;
  }
  
  async function executeTransaction(transaction: VersionedTransaction, connection: Connection): Promise<string> {
    let tryAgain = true;
    let txid: string | undefined;
    const maxTries = 10;
    let attempts = 0;
  
    while (tryAgain && attempts < maxTries) {
      try {
        attempts++;
  

        const rawTransaction = transaction.serialize();
        txid = await connection.sendRawTransaction(rawTransaction, {
          skipPreflight: true,
          maxRetries: 2
        });
  
        // Wait for a short period before confirming the transaction
        await new Promise(r => setTimeout(r, 1000));
  
        const result = await connection.getSignatureStatus(txid, {
          searchTransactionHistory: true,
        });
  
        if (result?.value?.err === null) {
          tryAgain = false;
        } else if (attempts >= maxTries) {
          tryAgain = false;
          throw new Error('Max retries exceeded');
        }
      } catch (error) {
        console.error('Error during transaction execution:', error);
        if (attempts >= maxTries) {
          throw new Error('Max retries exceeded');
        }
      }
    }
  
    if (!txid) {
      throw new Error('Transaction ID is undefined');
    }
  
    await connection.confirmTransaction(txid);
  
    return txid;
  }
  

  export async function buySolTokens(chatId: number, contractAddress: string): Promise<string> {
    try {

      const user = await User.findOne({ chat_id: chatId }).exec();
      if (!user) {
        throw new Error('User not found');
      }
  
      const snipe = await Snipe.findOne({
        chatId: chatId,
        contractAddress: contractAddress,
        blockchain: 'sol' 
      }).exec();
  
      if (!snipe) {
        throw new Error('Snipe configuration not found');
      }
  
      const { private_key, address } = user.sol_wallet; 
      const solCurrentTokenName = user.solCurrentTokenName; 
  
      const spendSol = snipe.config.spend_sol; 
      const slippageSol = snipe.config.slippage_sol; 
  
      if (spendSol === undefined || spendSol === null || isNaN(spendSol)) {
        throw new Error('Invalid spend_sol value');
      }
  
      if (slippageSol === undefined || slippageSol === null || isNaN(slippageSol)) {
        throw new Error('Invalid slippage_sol value');
      }
  
      const inputMint = SOLANA_ADDRESS; 
      const outputMint = contractAddress;
      if (!outputMint) {
        throw new Error('Output mint address is not defined');
      }
  
      const slippageBps = slippageSol * 100;
      const secretKey = bs58.decode(private_key);
      const wallet = Keypair.fromSecretKey(secretKey);
      const solAmountInLamports = BigInt(Math.round(spendSol * 1e9)); 
  
      const feeAmount = spendSol * 0.01; // 1% fee
      const feeInLamports = BigInt(Math.round(feeAmount * 1e9));
  
      const quoteData = await getQuote(inputMint, outputMint, solAmountInLamports, slippageBps);
      const swapTransaction = await getSwapTransaction(quoteData, wallet.publicKey.toString());
  
      const swapTransactionBuf = Buffer.from(swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(swapTransactionBuf);
  
      const feeTransferInstruction = SystemProgram.transfer({
        fromPubkey: wallet.publicKey,
        toPubkey: new PublicKey(ADMIN_PUBLIC_KEY_STRING),
        lamports: feeInLamports,
      });
  
      const addressLookupTableAccounts = await Promise.all(
        transaction.message.addressTableLookups.map(async (lookup) => {
          const accountInfo = await connection.getAccountInfo(lookup.accountKey);
          if (!accountInfo) {
            throw new Error(`Account info not found for key: ${lookup.accountKey.toBase58()}`);
          }
          return new AddressLookupTableAccount({
            key: lookup.accountKey,
            state: AddressLookupTableAccount.deserialize(accountInfo.data),
          });
        })
      );
  
      let message = TransactionMessage.decompile(transaction.message, { addressLookupTableAccounts });
      message.instructions.push(feeTransferInstruction);
  
      transaction.message = message.compileToV0Message(addressLookupTableAccounts);
  
      transaction.sign([wallet]);
  
      const txid = await executeTransaction(transaction, connection);
  
      const existingTrade = await Trade.findOne({
        userId: user._id,
        contractAddress: outputMint,
        isSold: false
      }).exec();
  
      if (existingTrade) {
        existingTrade.buyAmount += spendSol;
        await existingTrade.save();
        // console.log('Updated existing trade with new buy amount');
      } else {
        const newTrade = new Trade({
          userId: user._id,
          chatId: chatId,
          walletId: user.sol_wallet.address,
          contractAddress: outputMint,
          contractName: solCurrentTokenName || '---',
          buyAmount: spendSol,
          sellAmount: 0,
          takeProfit: 0,
          stopLoss: 0,
          isSold: false
        });
  
        await newTrade.save();
        // console.log('Created new trade entry');
      }
  
      await User.updateOne(
        { _id: user._id },
        { $push: { trades: existingTrade ? existingTrade._id : user._id } }
      );
  
      // console.log('User trades updated successfully');
  
      return txid;
    } catch (error) {
      console.error('Error during token purchase:', error);
      throw error;
    }
  }