/**
 * Blockchain Services Module
 *
 * This module exports all blockchain-specific services.
 */

// Export blockchain services
export * from '../trading/index';
export * from '../token/TokenInfoService';
export * from '../honeypot/UnifiedHoneypotChecker';

// Export blockchain-specific services as namespaces
import * as bscscanService from './bscscan.service';
import * as basescanService from './basescan.service';
import * as ethscanService from './ethscan.service';
import * as solscanService from './solscan.service';

export const bscscan = bscscanService;
export const basescan = basescanService;
export const ethscan = ethscanService;
export const solscan = solscanService;

/**
 * Initialize all blockchain services
 */
export function initializeBlockchainServices(): void {
  // Initialize blockchain services
  const { logger } = require('../logger/LoggerService');
  logger.info('Initializing blockchain services...');

  // Initialize provider manager
  try {
    const { providerManager } = require('./ProviderService');
    logger.info('Provider Manager initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize Provider Manager', {
      error: (error as Error).message
    });
  }

  // Log initialization complete
  logger.info('All blockchain services initialized successfully');
}
