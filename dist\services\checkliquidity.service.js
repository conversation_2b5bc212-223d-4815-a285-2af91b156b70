"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLiquidityInfoFromDexscreener = void 0;
const axios_1 = __importDefault(require("axios"));
// Function to get token info from Dexscreener
const getLiquidityInfoFromDexscreener = (contractAddress) => __awaiter(void 0, void 0, void 0, function* () {
    const url = `https://api.dexscreener.com/latest/dex/tokens/${contractAddress}`;
    try {
        const response = yield axios_1.default.get(url);
        // Check if the pairs array is null or empty
        if (!response.data.pairs || response.data.pairs.length === 0) {
            console.log(`No liquidity found for the token ${contractAddress}.`);
            return {
                hasLiquidity: false,
                message: `No liquidity found for the token ${contractAddress}.`
            };
        }
        const latestPair = response.data.pairs[0];
        const { baseToken, priceNative, priceUsd, chainId, priceChange, dexId, totalSupply, liquidity } = latestPair;
        const symbol = baseToken.symbol;
        const name = baseToken.name || 'Unknown Token';
        const pairAddress = latestPair.pairAddress || 'Not available';
        const marketCap = latestPair.marketCap || 'Not available';
        // console.log(`Liquidity found for the token ${contractAddress}:`);
        // console.log(`Symbol: ${symbol}`);
        // console.log(`Name: ${name}`);
        // console.log(`Price (Native): ${priceNative}`);
        // console.log(`Price (USD): ${priceUsd}`);
        // console.log(`Chain ID: ${chainId}`);
        // console.log(`Price Change: ${priceChange}`);
        // console.log(`Dex ID: ${dexId}`);
        // console.log(`Total Supply: ${totalSupply}`);
        // console.log(`Pair Address: ${pairAddress}`);
        // console.log(`Market Cap: ${marketCap}`);
        // console.log(`Liquidity: ${liquidity}`);
        return {
            hasLiquidity: true,
            symbol,
            name,
            priceNative,
            priceUsd,
            chainId,
            priceChange,
            dexId,
            totalSupply,
            pairAddress,
            marketCap,
            address: baseToken.address
        };
    }
    catch (error) {
        console.error('Error fetching token info from Dexscreener:', error);
        throw new Error('Error fetching token info from Dexscreener. Please try again later.');
    }
});
exports.getLiquidityInfoFromDexscreener = getLiquidityInfoFromDexscreener;
