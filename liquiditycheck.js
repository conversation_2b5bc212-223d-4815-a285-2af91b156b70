const axios = require('axios');

// Function to get token info from Dexscreener
const getTokenInfoFromDexscreener = async (contractAddress) => {
  const url = `https://api.dexscreener.com/latest/dex/tokens/${contractAddress}`;

  try {
    const response = await axios.get(url);

    // Check if the pairs array is null or empty
    if (!response.data.pairs || response.data.pairs.length === 0) {
      console.log(`No liquidity found for the token ${contractAddress}.`);
      return {
        liquidity: false,
        message: `No liquidity found for the token ${contractAddress}.`
      };
    }

    const latestPair = response.data.pairs[0];

    const {
      baseToken,
      priceNative,
      priceUsd,
      chainId,
      priceChange,
      dexId,
      totalSupply,
      liquidity
    } = latestPair;

    const symbol = baseToken.symbol;
    const name = baseToken.name || 'Unknown Token';
    const pairAddress = latestPair.pairAddress || 'Not available';
    const marketCap = latestPair.marketCap || 'Not available';

    console.log(`Liquidity found for the token ${contractAddress}:`);
    console.log(`Symbol: ${symbol}`);
    console.log(`Name: ${name}`);
    console.log(`Price (Native): ${priceNative}`);
    console.log(`Price (USD): ${priceUsd}`);
    console.log(`Chain ID: ${chainId}`);
    console.log(`Price Change: ${priceChange}`);
    console.log(`Dex ID: ${dexId}`);
    console.log(`Total Supply: ${totalSupply}`);
    console.log(`Pair Address: ${pairAddress}`);
    console.log(`Market Cap: ${marketCap}`);
    console.log(`Liquidity: ${liquidity}`);

    return {
      liquidity: true,
      symbol,
      name,
      priceNative,
      priceUsd,
      chainId,
      priceChange,
      dexId,
      totalSupply,
      pairAddress,
      marketCap,
      liquidity,
      address: baseToken.address
    };
  } catch (error) {
    console.error('Error fetching token info from Dexscreener:', error);
    throw new Error('Error fetching token info from Dexscreener. Please try again later.');
  }
};

// Example usage
const tokenAddress = '0xfd0a0a42f38b9fe58f249067435f967c272cd6b9'; // Replace with the token address you want to check

getTokenInfoFromDexscreener(tokenAddress)
  .then(result => console.log('Token liquidity check result:', result))
  .catch(error => console.error('Failed to check token liquidity:', error));
