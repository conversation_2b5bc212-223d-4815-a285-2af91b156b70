import mongoose, { Document, Schema } from 'mongoose';

export interface Trade extends Document {
  chatId: number;
  blockchain: string;
  contractAddress: string;
  contractName: string;
  isSold: boolean;
  isInitialized: 'pending' | 'ongoing';
  config: {
    takeProfit: number;
    stopLoss: number;
    spendAmount: number;
  };
}

const tradeSchema = new Schema<Trade>({
  chatId: { type: Number, required: true },
  blockchain: { type: String, required: true },
  contractAddress: { type: String },
  contractName: { type: String, default: '' },
  isSold: { type: Boolean, default: false },
  isInitialized: { type: String, enum: ['pending', 'ongoing'], default: 'pending' },
  config: {
    takeProfit: { type: Number, default: 0 },
    stopLoss: { type: Number, default: 0 },
    spendAmount: { type: Number, default: 0 },
  },
});

const AutoTradeModel = mongoose.model<Trade>('AutoTrade', tradeSchema);



export async function updateAutoTradeAsSold(_id: unknown, contractAddress: string, p0: number, p1: string): Promise<void> {
  await AutoTradeModel.findOneAndUpdate(
    { contractAddress },
    { isSold: true }
  ).exec();
}
export default AutoTradeModel;
