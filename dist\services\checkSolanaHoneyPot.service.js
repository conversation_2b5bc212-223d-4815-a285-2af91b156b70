"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkIfHoneypot = checkIfHoneypot;
const axios_1 = __importDefault(require("axios"));
// Initialize state variables
let honeypotState = {
    risk: '',
    buyTax: '0',
    sellTax: '0',
    transferTax: '0',
    riskDescriptions: 'No specific risks identified',
};
let honeypotCheckTimestamp = null;
function checkIfHoneypot(address) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c, _d, _e, _f;
        const url = `https://api.rugcheck.xyz/v1/tokens/${address}/report/summary`;
        try {
            const response = yield axios_1.default.get(url);
            const data = response.data;
            // Extract risk descriptions and check for "danger" level
            let riskDescriptions = [];
            let dangerDetected = false;
            if (data.risks && data.risks.length > 0) {
                riskDescriptions = data.risks.map(risk => {
                    if (risk.level === 'danger') {
                        dangerDetected = true;
                    }
                    return `${risk.name}: ${risk.description} (${risk.level})`;
                });
            }
            // Add special warning if danger level is found
            if (dangerDetected) {
                riskDescriptions.unshift('HoneyPot detected, RUN THE FUCK AWAY');
            }
            // Check if simulationResult and its properties are defined
            const buyTax = (_b = (_a = data.simulationResult) === null || _a === void 0 ? void 0 : _a.buyTax) !== null && _b !== void 0 ? _b : '0';
            const sellTax = (_d = (_c = data.simulationResult) === null || _c === void 0 ? void 0 : _c.sellTax) !== null && _d !== void 0 ? _d : '0';
            const transferTax = (_f = (_e = data.simulationResult) === null || _e === void 0 ? void 0 : _e.transferTax) !== null && _f !== void 0 ? _f : '0';
            // Update honeypotState with risk, tax info, and risk descriptions
            honeypotState = {
                risk: data.score,
                buyTax: buyTax,
                sellTax: sellTax,
                transferTax: transferTax,
                riskDescriptions: riskDescriptions.join('\n') || 'No specific risks identified',
            };
            // Log the updated honeypotState
            console.log('Honeypot state updated:', honeypotState);
            // Update the timestamp after a successful API call
            honeypotCheckTimestamp = Date.now();
            return honeypotState; // Return the updated honeypotState
        }
        catch (error) {
            console.error('Error fetching honeypot data:', error);
            throw error; // Rethrow the error to be handled by the caller
        }
    });
}
