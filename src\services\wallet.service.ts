import { Keypair } from '@solana/web3.js';
import { ethers } from 'ethers';
import bs58 from 'bs58';

// Types for wallet generation
interface WalletResult {
  blockchain: string;
  network: string;
  address: string;
  privateKey: string;
  rpcUrl: string;
  mnemonic?: string;
}

export const generateETHWallet = (): WalletResult => {
  const wallet = ethers.Wallet.createRandom();
  return {
    blockchain: 'eth',
    network: 'mainnet',
    address: wallet.address,
    privateKey: wallet.privateKey,
    rpcUrl: 'https://mainnet.infura.io/v3/********************************',
    mnemonic: wallet.mnemonic.phrase,
  };
};

export const generateBSCWallet = (): WalletResult => {
  const wallet = ethers.Wallet.createRandom();
  return {
    blockchain: 'bsc',
    network: 'mainnet',
    address: wallet.address,
    privateKey: wallet.privateKey,
    rpcUrl: 'https://bsc-dataseed.binance.org/',
    mnemonic: wallet.mnemonic.phrase,
  };
};

export const generateSOLWallet = (): WalletResult => {
  const keyPair = Keypair.generate();
  return {
    blockchain: 'sol',
    network: 'mainnet',
    address: keyPair.publicKey.toString(),
    privateKey: bs58.encode(keyPair.secretKey),
    rpcUrl: 'https://api.mainnet-beta.solana.com',
  };
};

export const generateBASEWallet = (): WalletResult => {
  try {
    const wallet = ethers.Wallet.createRandom();
    return {
      blockchain: 'base',
      network: 'mainnet',
      address: wallet.address,
      privateKey: wallet.privateKey,
      rpcUrl: 'https://mainnet.base.org/',
      mnemonic: wallet.mnemonic.phrase,
    };
  } catch (error) {
    throw new Error('Failed to generate BASE wallet. Consult BASE documentation.');
  }
};
