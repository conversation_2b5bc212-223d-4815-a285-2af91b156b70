/**
 * Trading Service Module
 * 
 * This module exports the trading interfaces and implementations
 * for all supported blockchains.
 */

import TelegramBot from 'node-telegram-bot-api';
import { 
  BlockchainTrader, 
  BlockchainType, 
  TraderFactory 
} from './TradingInterface';
import { BscTrader } from './BscTrader';
import { EthTrader } from './EthTrader';
import { SolTrader } from './SolTrader';
import { BaseNetworkTrader } from './BaseTrader';
import { logger } from '../logger/LoggerService';

/**
 * Initialize all traders and register them with the factory
 * @param bot The Telegram bot instance
 */
export function initializeTraders(bot: TelegramBot): void {
  try {
    // Create trader instances
    const bscTrader = new BscTrader(bot);
    const ethTrader = new EthTrader(bot);
    const solTrader = new SolTrader(bot);
    const baseTrader = new BaseNetworkTrader(bot);
    
    // Register traders with the factory
    TraderFactory.registerTrader(bscTrader);
    TraderFactory.registerTrader(ethTrader);
    TraderFactory.registerTrader(solTrader);
    TraderFactory.registerTrader(baseTrader);
    
    logger.info('All blockchain traders initialized and registered');
  } catch (error) {
    logger.error('Failed to initialize traders', { 
      error: (error as Error).message 
    });
    throw error;
  }
}

/**
 * Get a trader for a specific blockchain
 * @param blockchain The blockchain type
 * @returns The blockchain trader
 */
export function getTrader(blockchain: BlockchainType): BlockchainTrader {
  try {
    return TraderFactory.getTrader(blockchain);
  } catch (error) {
    logger.error('Failed to get trader', { 
      blockchain,
      error: (error as Error).message 
    });
    throw error;
  }
}

// Export all trading-related types and classes
export * from './TradingInterface';
export * from './BscTrader';
export * from './EthTrader';
export * from './SolTrader';
export * from './BaseTrader';
