"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.buyBaseTokens = buyBaseTokens;
exports.sellBaseTokens = sellBaseTokens;
const ethers_1 = require("ethers");
const axios_1 = __importDefault(require("axios"));
const user_models_1 = __importDefault(require("../models/user.models"));
const trade_model_1 = __importDefault(require("../models/trade.model"));
const trade_model_2 = require("../models/trade.model");
const adminAddress = process.env.EVM_ADMIN_ADDRESS || '';
const providerUrl = process.env.BASE_PROVIDER_URL;
if (!adminAddress) {
    throw new Error('ADMIN_ADDRESS environment variable is not set');
}
if (!providerUrl) {
    throw new Error('Provider environment variable is not set');
}
const apiKey = 'a3e4332a-4ed8-4059-90fe-8bec73496649';
const headers = {
    '0x-api-key': apiKey,
};
function getSwapQuote(url) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            console.log('Requesting swap quote...');
            const response = yield axios_1.default.get(url, { headers });
            console.log('Swap Quote Response:', response.data);
            return response.data;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                console.error('Axios Error:', error.response ? error.response.data : error.message);
            }
            else {
                console.error('Error:', error.message);
            }
            throw error;
        }
    });
}
const abi = [
    "function decimals() view returns (uint8)"
];
function getTokenDecimals(tokenAddress, provider) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const tokenContract = new ethers_1.ethers.Contract(tokenAddress, abi, provider);
            const decimals = yield tokenContract.decimals();
            return decimals;
        }
        catch (error) {
            console.error('Error fetching token decimals:', error);
            throw error;
        }
    });
}
function buyBaseTokens(chatId, value) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log("Starting token purchase on baseSwap");
        try {
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user) {
                throw new Error('User not found');
            }
            yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { currentBlockchain: 'base' } });
            const currentBlockchain = user.currentBlockchain;
            if (currentBlockchain !== 'base') {
                throw new Error(`Current blockchain is ${currentBlockchain}, not base.`);
            }
            const baseConfig = user.base_config;
            if (!baseConfig) {
                throw new Error('Base configuration is not set for the user');
            }
            const { private_key, address } = user.base_wallet;
            const baseCurrentContractAddress = user.baseCurrentContractAddress;
            const baseCurrentTokenName = user.baseCurrentTokenName;
            if (!baseCurrentContractAddress) {
                throw new Error('Current contract address for base is not set');
            }
            const buyToken = baseCurrentContractAddress;
            const provider = new ethers_1.providers.JsonRpcProvider(providerUrl);
            const wallet = new ethers_1.Wallet(private_key, provider);
            const sellAmountWei = ethers_1.ethers.utils.parseEther(value);
            console.log(`Sell Amount (in wei): ${sellAmountWei.toString()}`);
            const sellToken = '******************************************';
            const feeRecipient = adminAddress;
            const buyTokenPercentageFee = 0.08;
            const slippagePercentage = 0.1;
            const queryParams = `buyToken=${buyToken}&sellToken=${sellToken}&sellAmount=${sellAmountWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;
            const url = `https://base.api.0x.org/swap/v1/quote?${queryParams}`;
            const quote = yield getSwapQuote(url);
            const buyTx = {
                to: quote.to,
                data: quote.data,
                value: ethers_1.ethers.BigNumber.from(quote.value),
                gasLimit: ethers_1.ethers.BigNumber.from(500000),
                gasPrice: ethers_1.ethers.utils.parseUnits('0.02', 'gwei'),
            };
            console.log('Signing and sending the buy transaction...');
            const buyTxResponse = yield wallet.sendTransaction(buyTx);
            console.log(`Buy Transaction Hash: ${buyTxResponse.hash}`);
            const receipt = yield buyTxResponse.wait();
            console.log(`Buy Transaction Mined! Block Number: ${receipt.blockNumber}`);
            const existingTrade = yield trade_model_1.default.findOne({
                userId: user._id,
                contractAddress: baseCurrentContractAddress,
                isSold: false,
            }).exec();
            if (existingTrade) {
                existingTrade.buyAmount += parseFloat(value);
                yield existingTrade.save();
                console.log('Updated existing trade with new buy amount');
            }
            else {
                const newTrade = new trade_model_1.default({
                    userId: user._id,
                    chatId: chatId,
                    walletId: user.base_wallet.address,
                    contractAddress: baseCurrentContractAddress,
                    contractName: baseCurrentTokenName,
                    buyAmount: parseFloat(value),
                    sellAmount: 0,
                    takeProfit: 0,
                    stopLoss: 0,
                    isSold: false,
                    blockchain: 'base'
                });
                yield newTrade.save();
                console.log('Created new trade entry');
            }
            yield user_models_1.default.updateOne({ _id: user._id }, { $push: { [`trades.${currentBlockchain}`]: existingTrade ? existingTrade._id : user._id } });
            console.log('User trades updated successfully');
            return receipt;
        }
        catch (error) {
            console.error('Error purchasing tokens:', error);
            throw error;
        }
    });
}
const erc20Abi = [
    "function decimals() view returns (uint8)",
    "function allowance(address owner, address spender) view returns (uint256)",
    "function approve(address spender, uint256 amount) returns (bool)"
];
// INFINITE APPROVAL
//   async function checkAndApproveAllowance(tokenContract: ethers.Contract, ownerAddress: string, spenderAddress: string, amountWei: ethers.BigNumber) {
//     const allowance = await tokenContract.allowance(ownerAddress, spenderAddress);
//     if (allowance.lt(amountWei)) {
//       console.log('Approving token allowance...');
//       const approveTx = await tokenContract.approve(spenderAddress, ethers.constants.MaxUint256);
//       await approveTx.wait();
//       console.log('Allowance approved');
//     } else {
//       console.log('Sufficient allowance already exists');
//     }
//   }
// AMOUNT BASED APPROVAL
function checkAndApproveAllowance(tokenContract, ownerAddress, spenderAddress, amountWei) {
    return __awaiter(this, void 0, void 0, function* () {
        const allowance = yield tokenContract.allowance(ownerAddress, spenderAddress);
        if (allowance.lt(amountWei)) {
            console.log('Approving token allowance...');
            const approveTx = yield tokenContract.approve(spenderAddress, amountWei);
            yield approveTx.wait();
            console.log('Allowance approved');
        }
        else {
            console.log('Sufficient allowance already exists');
        }
    });
}
function sellBaseTokens(chatId, value) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
            if (!user)
                throw new Error('User not found');
            const baseConfig = user.base_config;
            if (!baseConfig)
                throw new Error('Base configuration is not set for the user');
            const { private_key, address } = user.base_wallet;
            const baseCurrentContractAddress = user.baseCurrentContractAddress;
            if (!baseCurrentContractAddress)
                throw new Error('Current contract address for base is not set');
            const sellToken = baseCurrentContractAddress;
            const provider = new ethers_1.ethers.providers.JsonRpcProvider(providerUrl);
            const wallet = new ethers_1.ethers.Wallet(private_key, provider);
            const decimals = yield getTokenDecimals(sellToken, provider);
            const sellAmountWei = ethers_1.ethers.utils.parseUnits(value, decimals);
            const tokenContract = new ethers_1.ethers.Contract(sellToken, erc20Abi, wallet);
            const spenderAddress = "******************************************";
            yield checkAndApproveAllowance(tokenContract, address, spenderAddress, sellAmountWei);
            const feeRecipient = adminAddress;
            const buyTokenPercentageFee = 0.08;
            const slippagePercentage = 0.1;
            const queryParams = `buyToken=******************************************&sellToken=${sellToken}&sellAmount=${sellAmountWei}&slippagePercentage=${slippagePercentage}&takerAddress=${address}&feeRecipient=${feeRecipient}&buyTokenPercentageFee=${buyTokenPercentageFee}`;
            const url = `https://base.api.0x.org/swap/v1/quote?${queryParams}`;
            console.log('Requesting swap quote...');
            const response = yield axios_1.default.get(url, { headers });
            console.log('Swap Quote Response:', response.data);
            const { to: routerAddress, data: encodedData, value: quoteValue, gas, gasPrice } = response.data;
            const tx = {
                to: routerAddress,
                data: encodedData,
                value: quoteValue ? ethers_1.ethers.BigNumber.from(quoteValue) : ethers_1.ethers.BigNumber.from(0),
                gasLimit: ethers_1.ethers.BigNumber.from(500000),
                gasPrice: ethers_1.ethers.utils.parseUnits('0.02', 'gwei'),
            };
            console.log('Signing and sending the transaction...');
            const txResponse = yield wallet.sendTransaction(tx);
            console.log(`Transaction Hash: ${txResponse.hash}`);
            const receipt = yield txResponse.wait();
            console.log(`Transaction Mined! Block Number: ${receipt.blockNumber}`);
            yield (0, trade_model_2.updateTradeAsSold)(user._id, baseCurrentContractAddress, parseFloat(value), 'base');
            return receipt;
        }
        catch (error) {
            console.error('Error selling tokens:', error);
            throw error;
        }
    });
}
