"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTokenDetails = exports.sellBaseTokenPercentage = exports.handleBaseSellAll = exports.handleSpendBase = exports.handleBaseChart = exports.handleBaseRefresh = exports.askForBaseToken = exports.askForBaseContractAddress = exports.handleGenerateNewBaseWallet = exports.mainbaseMenu = exports.updatebaseMessageWithSellOptions = exports.createbaseKeyboard = exports.getBaseSellOptionsKeyboard = exports.handleBASEScanContract = void 0;
exports.fetchAndDisplayActiveBaseTrades = fetchAndDisplayActiveBaseTrades;
exports.displayCurrentBaseTrade = displayCurrentBaseTrade;
const user_models_1 = __importDefault(require("../models/user.models"));
const trade_model_1 = __importDefault(require("../models/trade.model"));
const checkEVMHoneyPot_service_1 = require("../services/checkEVMHoneyPot.service");
const dexscreener_service_1 = require("../services/dexscreener.service");
const ERC20ABI_json_1 = __importDefault(require("../abis/ERC20ABI.json"));
const ethers_1 = require("ethers");
const base_dashboard_1 = require("../screens/dashboards/base.dashboard");
const wallet_service_1 = require("./wallet.service");
const botConvo_1 = require("../botConvo");
const botConvo_2 = require("../botConvo");
const globalState_1 = require("../globalState");
const baseuniswap_trader_1 = require("../executor/baseuniswap.trader");
let localState = {};
let messageIdStore = {};
// Create a provider instance
const providerUrl = process.env.BASE_PROVIDER_URL;
const provider = new ethers_1.ethers.providers.JsonRpcProvider(providerUrl);
if (!providerUrl) {
    throw new Error('PROVIDER is not defined in the environment variables.');
}
// Get total supply
const getTotalSupply = (tokenAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Create a contract instance
        const tokenContract = new ethers_1.ethers.Contract(tokenAddress, ERC20ABI_json_1.default, provider);
        // Call the decimals function
        const decimals = yield tokenContract.decimals();
        // Call the totalSupply function
        const totalSupply = yield tokenContract.totalSupply();
        // Convert the total supply from a BigNumber to a more readable format
        const formattedTotalSupply = ethers_1.ethers.utils.formatUnits(totalSupply, decimals);
        return formattedTotalSupply;
    }
    catch (error) {
        console.error('Error fetching total supply:', error);
        return null;
    }
});
// Handle BASE scan contract
const handleBASEScanContract = (bot, chat_id, userAddress) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        if (!userAddress) {
            throw new Error('Invalid contract address.');
        }
        const previousContractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (previousContractAddress && localState[previousContractAddress]) {
            delete localState[previousContractAddress];
        }
        localState[userAddress] = localState[userAddress] || {};
        (0, globalState_1.setCurrentContractAddress)(userAddress);
        // Check if the contract is a honeypot
        const honeypotResult = yield (0, checkEVMHoneyPot_service_1.checkIfHoneypot)(userAddress);
        // Get token info from Dexscreener
        const tokenInfo = yield (0, dexscreener_service_1.getTokenInfoFromDexscreener)(userAddress);
        // Compute additional data
        const totalSupply = yield getTotalSupply(userAddress);
        if (totalSupply === null) {
            throw new Error('Failed to fetch total supply.');
        }
        const totalSupplyNumber = Number(totalSupply);
        const priceUsdNumber = Number(tokenInfo.priceUsd);
        const marketCap = priceUsdNumber * totalSupplyNumber;
        // Utility function to format numbers for display
        const formatNumber = (num) => {
            if (num >= 1000000000000) {
                return (num / 1000000000000).toFixed(2) + 'T'; // Trillions
            }
            else if (num >= 1000000000) {
                return (num / 1000000000).toFixed(2) + 'B'; // Billions
            }
            else if (num >= 1000000) {
                return (num / 1000000).toFixed(2) + 'M'; // Millions
            }
            else if (num >= 1000) {
                return (num / 1000).toFixed(2) + 'K'; // Thousands
            }
            else {
                return num.toFixed(2); // Less than a thousand
            }
        };
        const formattedTotalSupply = formatNumber(totalSupplyNumber);
        const formattedMarketCap = formatNumber(marketCap);
        const formattedLiquidity = formatNumber(Number(tokenInfo.liquidity.usd));
        const priceChangeEmoji = tokenInfo.priceChange.m5 > 0 ? '🟢🟢🟢🟢🟢🟢' : '🔴🔴🔴🔴🔴🔴';
        const roundedBuyTax = Math.ceil(parseFloat(honeypotResult.buyTax));
        const roundedSellTax = Math.ceil(parseFloat(honeypotResult.sellTax));
        const resultMessage = `
🪙 ${tokenInfo.symbol} (${tokenInfo.name}) || 📈 ${tokenInfo.priceChange.m5 > 0 ? '+' : ''}${tokenInfo.priceChange.m5}%  || 🏦 ${tokenInfo.dexId} || ${tokenInfo.chainId}

${priceChangeEmoji}  Price Change: ${tokenInfo.priceChange.m5 > 0 ? '+' : ''}${tokenInfo.priceChange.m5}%

CA: ${tokenInfo.address}
LP: ${tokenInfo.pairAddress}

💼 TOTAL SUPPLY: ${formattedTotalSupply} ${tokenInfo.symbol}
🏷️ MC: $${formattedMarketCap}
💧 LIQUIDITY: $${formattedLiquidity}
💵 PRICE: $${tokenInfo.priceUsd}

🔍 Honeypot/Rugpull Risk: ${honeypotResult.risk}
📉 Buy Tax: ${roundedBuyTax}%
📈 Sell Tax: ${roundedSellTax}%

⚠️⚠️⚠️⚠️ ${honeypotResult.flagDescriptions}
    `;
        const user = yield user_models_1.default.findOne({ chat_id }).exec();
        if (user) {
            user.baseCurrentTokenName = tokenInfo.name;
            user.baseCurrentContractAddress = userAddress;
            yield user.save();
        }
        localState[userAddress] = {
            totalSupply: totalSupplyNumber, // Store raw number
            marketCap: marketCap, // Store raw number
            checkHoneypot: honeypotResult,
            tokenInfo,
            resultMessage
        };
        const previousMessageId = messageIdStore[chat_id];
        if (previousMessageId) {
            yield bot.deleteMessage(chat_id, previousMessageId);
        }
        const sentMessage = yield bot.sendMessage(chat_id, resultMessage, {
            parse_mode: 'Markdown',
            reply_markup: (0, exports.createbaseKeyboard)()
        });
        messageIdStore[chat_id] = sentMessage.message_id;
        return sentMessage.message_id;
    }
    catch (error) {
        console.error('Error scanning contract:', error);
        yield bot.sendMessage(chat_id, 'There was an error scanning the contract. Please try again later.');
    }
});
exports.handleBASEScanContract = handleBASEScanContract;
// Create sell options keyboard
const getBaseSellOptionsKeyboard = () => __awaiter(void 0, void 0, void 0, function* () {
    return [
        [
            { text: 'Sell All', callback_data: JSON.stringify({ command: 'sell_all_base' }) },
            { text: 'Sell 25%', callback_data: JSON.stringify({ command: 'sell_25_base' }) }
        ],
        [
            { text: 'Sell 50%', callback_data: JSON.stringify({ command: 'sell_50_base' }) },
            { text: 'Sell 75%', callback_data: JSON.stringify({ command: 'sell_75_base' }) }
        ],
        [
            { text: 'Back', callback_data: JSON.stringify({ command: 'back_base' }) }
        ]
    ];
});
exports.getBaseSellOptionsKeyboard = getBaseSellOptionsKeyboard;
const createbaseKeyboard = () => {
    return {
        inline_keyboard: [
            [
                { text: '◀️ Previous', callback_data: JSON.stringify({ command: 'previous_base' }) },
                { text: '▶️ Next', callback_data: JSON.stringify({ command: 'next_base' }) }
            ],
            [
                { text: '🔄 Refresh', callback_data: JSON.stringify({ command: 'refresh_base' }) },
                { text: '💸 Sell', callback_data: JSON.stringify({ command: 'sell_base' }) }
            ],
            [
                { text: '📈 Chart', callback_data: JSON.stringify({ command: 'chart_base' }) },
                { text: '💸 Spend X Base', callback_data: JSON.stringify({ command: 'spend_base' }) }
            ],
            [
                {
                    text: "* Dismiss message",
                    callback_data: JSON.stringify({
                        command: "dismiss_message",
                    }),
                },
            ]
            // [
            //   { text: 'Advanced Trade', callback_data: JSON.stringify({ command: 'snipetrade_dashboard_base' }) }
            // ]
        ]
    };
};
exports.createbaseKeyboard = createbaseKeyboard;
// Update message with sell options
const updatebaseMessageWithSellOptions = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const sellOptions = yield (0, exports.getBaseSellOptionsKeyboard)();
        // Fetch the current contract address from the global state
        const contractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (!contractAddress) {
            console.error('No contract address found in global state.');
            return;
        }
        // Fetch token details using the contract address
        const tokenDetails = (0, exports.getTokenDetails)(contractAddress);
        if (!tokenDetails || !tokenDetails.resultMessage) {
            console.error('No result message found for the token address.');
            return;
        }
        // Update the message with the new sell options
        yield bot.editMessageText(tokenDetails.resultMessage, {
            chat_id: chatId,
            message_id: messageId,
            parse_mode: 'Markdown',
            reply_markup: {
                inline_keyboard: sellOptions
            }
        });
    }
    catch (error) {
        console.error('Failed to update sell options:', error);
    }
});
exports.updatebaseMessageWithSellOptions = updatebaseMessageWithSellOptions;
// export const updateBaseMessageWithTradeOptions = async (
//   bot: TelegramBot, 
//   chatId: number, 
//   messageId: number
// ): Promise<void> => {
//   try {
//     const tradeOptions = createBaseTradeKeyboard();
//     // Fetch the current contract address from the global state
//     const contractAddress = getCurrentContractAddress();
//     if (!contractAddress) {
//       console.error('No contract address found in global state.');
//       return;
//     }
//     await bot.editMessageReplyMarkup(
//       { inline_keyboard: tradeOptions.inline_keyboard },
//       { chat_id: chatId, message_id: messageId }
//     );
//   } catch (error) {
//     console.error('Failed to update trade options:', error);
//   }
// };
const mainbaseMenu = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Create the base keyboard options
        const baseOptions = (0, exports.createbaseKeyboard)();
        // Fetch the current contract address from the global state
        const contractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (!contractAddress) {
            console.error('No contract address found in global state.');
            return;
        }
        // Fetch token details using the contract address
        const tokenDetails = (0, exports.getTokenDetails)(contractAddress);
        if (!tokenDetails || !tokenDetails.resultMessage) {
            console.error('No result message found for the token address.');
            return;
        }
        // Update the message with the new base options
        yield bot.editMessageText(tokenDetails.resultMessage, {
            chat_id: chatId,
            message_id: messageId,
            parse_mode: 'Markdown',
            reply_markup: {
                inline_keyboard: baseOptions.inline_keyboard
            }
        });
    }
    catch (error) {
        console.error('Failed to update base menu:', error);
        yield bot.sendMessage(chatId, 'Failed to update the menu. Please try again later.');
    }
});
exports.mainbaseMenu = mainbaseMenu;
const handleGenerateNewBaseWallet = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId });
        if (!user) {
            yield bot.sendMessage(chatId, 'User not found.');
            return;
        }
        const newWallet = (0, wallet_service_1.generateBASEWallet)();
        yield user_models_1.default.updateOne({ chat_id: chatId }, { $set: { 'base_wallet.address': newWallet.address, 'base_wallet.private_key': newWallet.privateKey } });
        // Send confirmation message
        yield bot.sendMessage(chatId, `✅ You clicked "Generate New Wallet" in the base blockchain.\nNew wallet address: \`\`\`${newWallet.address}\`\`\`\nNew wallet Private Key: \`\`\`${newWallet.privateKey}\`\`\``);
        yield (0, base_dashboard_1.handleBaseDashboard)(bot, chatId);
    }
    catch (error) {
        console.error('Failed to generate new wallet:', error);
        yield bot.sendMessage(chatId, 'There was an error generating a new wallet. Please try again later.');
    }
});
exports.handleGenerateNewBaseWallet = handleGenerateNewBaseWallet;
// Message ID storage
let promptMessageIdStore = {};
const askForBaseContractAddress = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Save previous message IDs if any
        if (promptMessageIdStore[chatId]) {
            const ids = promptMessageIdStore[chatId];
            if (ids.promptId) {
                try {
                    yield bot.deleteMessage(chatId, ids.promptId);
                }
                catch (error) {
                    console.error('Failed to delete previous prompt message:', error);
                }
            }
            if (ids.replyId) {
                try {
                    yield bot.deleteMessage(chatId, ids.replyId);
                }
                catch (error) {
                    console.error('Failed to delete previous reply message:', error);
                }
            }
        }
        // Send message asking for contract address
        const sentMessage = yield bot.sendMessage(chatId, botConvo_1.INPUT_BASE_CONTRACT_ADDRESS, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        // Update the message ID store
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log('Received contract address:', msg.text); // Log the received text
                    // Save the reply message ID
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error('Invalid contract address.'));
                }
            }));
        });
    }
    catch (error) {
        console.error('Failed to ask for contract address:', error);
        throw error;
    }
});
exports.askForBaseContractAddress = askForBaseContractAddress;
const askForBaseToken = (bot, chatId, messageId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        // Save previous message IDs if any
        if (promptMessageIdStore[chatId]) {
            const ids = promptMessageIdStore[chatId];
            if (ids.promptId) {
                try {
                    yield bot.deleteMessage(chatId, ids.promptId);
                }
                catch (error) {
                    console.error('Failed to delete previous prompt message:', error);
                }
            }
            if (ids.replyId) {
                try {
                    yield bot.deleteMessage(chatId, ids.replyId);
                }
                catch (error) {
                    console.error('Failed to delete previous reply message:', error);
                }
            }
        }
        // Send message asking for BSC token
        const sentMessage = yield bot.sendMessage(chatId, botConvo_1.INPUT_BASE_CONTRACT_ADDRESS_TOSNIPE, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        // Update the message ID store
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log('Received Base token:', msg.text); // Log the received text
                    // Save the reply message ID
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error('Invalid BSC token.'));
                }
            }));
        });
    }
    catch (error) {
        console.error('Failed to ask for BSC token:', error);
        throw error;
    }
});
exports.askForBaseToken = askForBaseToken;
let tradesState = []; // Store the fetched trades
let currentIndex = 0; // Track the current trade index
function fetchAndDisplayActiveBaseTrades(bot, chatId, blockchain) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Find active trades for the specific user based on chatId and blockchain
            const tradesState = yield trade_model_1.default.find({ chatId, isSold: false, blockchain }).exec();
            let currentIndex = 0;
            if (tradesState.length === 0) {
                yield bot.sendMessage(chatId, 'No active trades found!');
                return;
            }
            // Display the current trade information
            yield displayCurrentBaseTrade(bot, chatId, tradesState, currentIndex);
        }
        catch (error) {
            console.error('Error fetching trades:', error);
            yield bot.sendMessage(chatId, 'An error occurred while fetching trades.');
        }
    });
}
function displayCurrentBaseTrade(bot, chatId, tradesState, index, messageId) {
    return __awaiter(this, void 0, void 0, function* () {
        const trade = tradesState[index];
        const message = `
<b>Contract:</b> ${trade.contractName} (${trade.contractAddress})
<b>Wallet:</b> ${trade.walletId}
<b>Buy Amount:</b> ${trade.buyAmount}  <b>Sell Amount:</b> ${trade.sellAmount}
<b>Sold:</b> ${trade.isSold ? 'Yes' : 'No'}
  `;
        // Update user details
        yield user_models_1.default.findOneAndUpdate({ chat_id: chatId }, {
            baseCurrentContractAddress: trade.contractAddress,
            baseCurrentTokenName: trade.contractName,
        });
        // Update global state
        (0, globalState_1.setCurrentContractAddress)(trade.contractAddress);
        const options = {
            parse_mode: "HTML",
            disable_web_page_preview: true,
            reply_markup: {
                inline_keyboard: [
                    [
                        {
                            text: "Sell 25%",
                            callback_data: JSON.stringify({ command: "sell_25_base" }),
                        },
                        {
                            text: "Sell 50%",
                            callback_data: JSON.stringify({ command: "sell_50_base" }),
                        },
                    ],
                    [
                        {
                            text: "Sell 75%",
                            callback_data: JSON.stringify({ command: "sell_75_base" }),
                        },
                        {
                            text: "Sell All",
                            callback_data: JSON.stringify({ command: "sell_all_base" }),
                        },
                    ],
                    [
                        {
                            text: "Previous",
                            callback_data: JSON.stringify({ command: "previous_base" }),
                        },
                        {
                            text: "Next",
                            callback_data: JSON.stringify({ command: "next_base" }),
                        },
                    ],
                    [
                        {
                            text: "* Dismiss message",
                            callback_data: JSON.stringify({
                                command: "dismiss_message",
                            }),
                        },
                    ],
                ],
            },
        };
        if (messageId) {
            yield bot.editMessageText(message, Object.assign({ chat_id: chatId, message_id: messageId }, options));
        }
        else {
            yield bot.sendMessage(chatId, message, options);
        }
    });
}
const handleBaseRefresh = (bot, chatId, callbackQuery) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const currentContractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (!currentContractAddress) {
            throw new Error('No contract address is currently set.');
        }
        yield (0, exports.handleBASEScanContract)(bot, chatId, currentContractAddress);
        // If a callbackQuery is provided, show an alert
        if (callbackQuery) {
            const callbackQueryId = callbackQuery.id;
            yield bot.answerCallbackQuery(callbackQueryId, {
                text: "Token Refresh Successfully!",
                show_alert: true
            });
        }
    }
    catch (error) {
        console.error('Failed to refresh contract details:', error);
        yield bot.sendMessage(chatId, 'Failed to refresh contract details. Please try again later.');
    }
});
exports.handleBaseRefresh = handleBaseRefresh;
const handleBaseChart = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const currentContractAddress = (0, globalState_1.getCurrentContractAddress)();
        if (currentContractAddress) {
            const dexscreenerUrl = `https://dexscreener.com/base/${currentContractAddress}`;
            const chartMessage = yield bot.sendMessage(chatId, `<a href="${dexscreenerUrl}">Click here to view the chart on Dexscreener</a>`, { parse_mode: 'HTML' });
            promptMessageIdStore[chatId] = { promptId: chartMessage.message_id };
        }
        else {
            const errorMessage = yield bot.sendMessage(chatId, 'Error: Current contract address is not set.');
            promptMessageIdStore[chatId] = { promptId: errorMessage.message_id };
        }
    }
    catch (error) {
        console.error('Failed to handle chart action:', error);
    }
});
exports.handleBaseChart = handleBaseChart;
const handleSpendBase = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const sentMessage = yield bot.sendMessage(chatId, botConvo_2.SET_BASE_SPEND, {
            parse_mode: 'HTML',
            reply_markup: {
                force_reply: true,
            },
        });
        promptMessageIdStore[chatId] = { promptId: sentMessage.message_id };
        return new Promise((resolve, reject) => {
            bot.onReplyToMessage(chatId, sentMessage.message_id, (msg) => __awaiter(void 0, void 0, void 0, function* () {
                if (msg.text) {
                    console.log('Received buy amount:', msg.text);
                    promptMessageIdStore[chatId] = Object.assign(Object.assign({}, promptMessageIdStore[chatId]), { replyId: msg.message_id });
                    resolve(msg.text);
                }
                else {
                    reject(new Error('Invalid buy amount.'));
                }
            }));
        });
    }
    catch (error) {
        console.error('Error handling spend base:', error);
        throw new Error('Failed to handle spend base action.');
    }
});
exports.handleSpendBase = handleSpendBase;
// export async function getbaseTokenBalance(tokenAddress: string, userAddress: string): Promise<string> {
//   const tokenContract = new ethers.Contract(
//       tokenAddress,
//       ['function balanceOf(address) view returns (uint)', 'function decimals() view returns (uint)'],
//       provider
//   );
//   const balance = await tokenContract.balanceOf(userAddress);
//   const tokenDecimals = await tokenContract.decimals();
//   const balanceWithDecimals = ethers.utils.formatUnits(balance, tokenDecimals);
//   console.log(balanceWithDecimals);
//   return balanceWithDecimals;
// }
const handleBaseSellAll = (bot, chatId) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }
        const { address: userAddress } = user.base_wallet;
        const tokenAddress = user.baseCurrentContractAddress || '';
        if (!tokenAddress) {
            throw new Error('No contract address found for user');
        }
        // Get token balance
        const tokenBalanceString = yield getbaseTokenBalance(tokenAddress, userAddress);
        const tokenBalanceNumber = parseFloat(tokenBalanceString);
        if (isNaN(tokenBalanceNumber) || tokenBalanceNumber <= 0) {
            throw new Error('Invalid token balance');
        }
        const receipt = yield (0, baseuniswap_trader_1.sellBaseTokens)(chatId, tokenBalanceString);
        if (receipt && receipt.transactionHash && receipt.blockNumber) {
            const { transactionHash, blockNumber } = receipt;
            yield bot.sendMessage(chatId, `✅ Successfully sold all your tokens:\n\n` +
                `🔗 Transaction Hash: ${transactionHash}\n` +
                `🧱 Block Number: ${blockNumber}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${transactionHash}\nTo see your transaction`);
        }
        else {
            yield bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
        }
    }
    catch (error) {
        console.error('Error selling all tokens:', error);
        // Type guard for `Error` type
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        yield bot.sendMessage(chatId, `An error occurred while selling all tokens: ${errorMessage}`);
    }
});
exports.handleBaseSellAll = handleBaseSellAll;
function getbaseTokenBalance(tokenAddress, userAddress) {
    return __awaiter(this, void 0, void 0, function* () {
        const tokenContract = new ethers_1.ethers.Contract(tokenAddress, [
            'function balanceOf(address owner) view returns (uint256)',
            'function decimals() view returns (uint8)'
        ], provider);
        // Fetch balance and decimals
        const [balance, decimals] = yield Promise.all([
            tokenContract.balanceOf(userAddress),
            tokenContract.decimals()
        ]);
        // Convert balance to a human-readable format
        const formattedBalance = ethers_1.ethers.utils.formatUnits(balance, decimals);
        return formattedBalance;
    });
}
const sellBaseTokenPercentage = (bot, chatId, percentage) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const user = yield user_models_1.default.findOne({ chat_id: chatId }).exec();
        if (!user) {
            throw new Error('User not found');
        }
        const { address: userAddress } = user.base_wallet;
        const tokenAddress = user.baseCurrentContractAddress;
        if (!tokenAddress) {
            throw new Error('No contract address found for user');
        }
        // Ensure tokenBalance is converted to a number
        const tokenBalanceString = yield getbaseTokenBalance(tokenAddress, userAddress);
        const tokenBalance = parseFloat(tokenBalanceString); // Convert to number
        if (isNaN(tokenBalance) || tokenBalance <= 0) {
            throw new Error('Insufficient token balance');
        }
        const amountToSell = tokenBalance * (percentage / 100);
        if (amountToSell <= 0) {
            throw new Error('Invalid percentage value');
        }
        console.log('Amount to sell:', amountToSell);
        const receipt = yield (0, baseuniswap_trader_1.sellBaseTokens)(chatId, amountToSell.toString());
        if (receipt && receipt.transactionHash && receipt.blockNumber) {
            const { transactionHash, blockNumber } = receipt;
            yield bot.sendMessage(chatId, `✅ Successfully sold ${percentage}% of your tokens:\n\n` +
                `🔗 Transaction Hash: ${transactionHash}\n` +
                `🧱 Block Number: ${blockNumber}\n\n` +
                `👁‍🗨 Navigate to:\nhttps://basescan.org/tx/${transactionHash}\nTo see your transaction`);
        }
        else {
            yield bot.sendMessage(chatId, `⚠️ Transaction failed. Please check your wallet and try again.`);
        }
    }
    catch (error) {
        console.error('Error selling tokens:', error);
        // Type guard for `Error` type
        const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
        yield bot.sendMessage(chatId, `An error occurred while selling tokens: ${errorMessage}`);
    }
});
exports.sellBaseTokenPercentage = sellBaseTokenPercentage;
// Retrieve token details by contract address
const getTokenDetails = (contractAddress) => {
    return localState[contractAddress];
};
exports.getTokenDetails = getTokenDetails;
