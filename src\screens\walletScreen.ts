import TelegramBot from 'node-telegram-bot-api';
import User from '../models/user.models';


const walletHandler = async (bot: TelegramBot, msg: TelegramBot.Message) => {
  const { id: chat_id } = msg.chat;

  try {
    // Check if the user already exists in the database
    const user = await User.findOne({ chat_id });

    if (user) {
      const caption = `👋 Select a wallet!\n\n`;

      await bot.sendMessage(chat_id, caption, {
        parse_mode: 'HTML',
        disable_web_page_preview: true,
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: 'BSC',
                callback_data: JSON.stringify({ command: 'view_bsc' }),
              },
              {
                text: 'BASE',
                callback_data: JSON.stringify({ command: 'view_base' }),
              },
            ],
            [
              {
                text: 'SOL',
                callback_data: JSON.stringify({ command: 'view_sol' }),
              },
              {
                text: 'ETH',
                callback_data: JSON.stringify({ command: 'view_eth' }),
              },
            ],
            [
              {
                text: '* Dismiss message',
                callback_data: JSON.stringify({ command: 'dismiss_message' }),
              },
            ],
          ],
        },
      });
    }
  } catch (error) {
    console.error('Error handling wallet selection:', error);
    await bot.sendMessage(chat_id, 'An error occurred while processing your request. Please try again later.');
  }
};

export default walletHandler;
