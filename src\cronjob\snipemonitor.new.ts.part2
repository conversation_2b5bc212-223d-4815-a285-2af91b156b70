/**
 * Update tokens (check liquidity and execute snipes)
 * @param bot Telegram bot instance
 */
export const updateTokens = async (bot: TelegramBot): Promise<void> => {
  const startTime = Date.now();
  logger.info('Started checking tokens for liquidity');

  try {
    // Find all configured snipes that haven't been bought yet
    const snipes = await Snipe.find({
      'config.isConfigured': true,
      'config.isBought': false,
      $or: [
        { 'config.isExecuted_bsc': false },
        { 'config.isExecuted_sol': false },
        { 'config.isExecuted_eth': false },
        { 'config.isExecuted_base': false },
      ],
    }).lean();

    if (snipes.length === 0) {
      logger.debug('No snipes to process');
      return;
    }

    logger.info(`Found ${snipes.length} snipes to process`);

    // Group snipes by blockchain for better processing
    const snipesByBlockchain = snipes.reduce<Record<string, ISnipe[]>>((acc, snipe) => {
      const blockchain = snipe.blockchain;
      if (!acc[blockchain]) {
        acc[blockchain] = [];
      }
      acc[blockchain].push(snipe);
      return acc;
    }, {});

    // Process each blockchain's snipes
    for (const [blockchain, blockchainSnipes] of Object.entries(snipesByBlockchain)) {
      logger.info(`Processing ${blockchainSnipes.length} snipes for ${blockchain}`);

      // Get unique token addresses for this blockchain
      const tokenAddresses = [...new Set(blockchainSnipes.map(snipe => snipe.contractAddress))];

      // Process in batches
      for (let i = 0; i < tokenAddresses.length; i += BATCH_SIZE) {
        const batch = tokenAddresses.slice(i, i + BATCH_SIZE);
        logger.debug(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(tokenAddresses.length / BATCH_SIZE)} for ${blockchain}`);

        // Check liquidity for each token in the batch
        const liquidityResults = await Promise.all(
          batch.map(address => checkLiquidity(address, blockchain))
        );

        // Process results
        for (let j = 0; j < batch.length; j++) {
          const address = batch[j];
          const data = liquidityResults[j];

          // Find all snipes for this token address
          const tokenSnipes = blockchainSnipes.filter(s => s.contractAddress === address);

          // Update all snipes with liquidity info
          for (const snipe of tokenSnipes) {
            // Update the snipe document with liquidity information
            await Snipe.updateOne(
              { _id: snipe._id },
              {
                $set: {
                  hasLiquidity: data.hasLiquidity,
                  lastPriceUpdate: new Date()
                }
              }
            );

            // Execute the snipe if it has liquidity and hasn't been executed yet
            const executedKey = `isExecuted_${blockchain}`;
            if (data.hasLiquidity && !snipe.config[executedKey as keyof ISnipeConfig]) {
              logger.info(`Liquidity found for token ${address} on ${blockchain}, executing snipe`);

              // Execute the snipe
              executeSnipe(snipe, bot).catch(error => {
                logger.error(`Failed to execute snipe for ${address}`, {
                  error: (error as Error).message,
                  blockchain,
                  address
                });
              });

              // Notify users that we found liquidity and are attempting to snipe
              await sendMessageToUser(
                bot,
                snipe.chatId,
                `🔍 Liquidity detected for ${address} on ${blockchain.toUpperCase()}! Attempting to snipe...`
              );

              // Emit socket event
              io.emit('liquidityFound', {
                tokenAddress: address,
                blockchain,
                timestamp: new Date().toISOString()
              });
            } else if (!data.hasLiquidity) {
              logger.debug(`No liquidity found for token ${address} on ${blockchain}`);
            }
          }
        }

        // Add a small delay between batches to avoid rate limiting
        if (i + BATCH_SIZE < tokenAddresses.length) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    // Log performance metrics
    const duration = Date.now() - startTime;
    logger.info(`Completed token update in ${duration}ms`, {
      snipeCount: snipes.length,
      durationMs: duration
    });
  } catch (error) {
    logger.error('Error updating tokens', {
      error: (error as Error).message,
      stack: (error as Error).stack
    });
  }
};

/**
 * Start periodic snipe updates
 * @param bot Telegram bot instance
 * @returns The interval ID
 */
export const startPeriodicSnipeUpdates = (bot: TelegramBot): ReturnType<typeof setInterval> => {
  logger.info(`Starting periodic snipe updates (every ${UPDATE_INTERVAL / 1000} seconds)`);
  return setInterval(() => updateTokens(bot), UPDATE_INTERVAL);
};

/**
 * Initialize the Socket.IO server
 * @param port Server port
 * @param bot Telegram bot instance
 */
export const initializeSocketServer = (port: number, bot: TelegramBot): void => {
  // Set up Socket.IO event handlers
  io.on('connection', (socket: any) => {
    logger.info('New client connected', { socketId: socket.id });

    // Send initial data
    sendSnipeData(socket);

    // Handle disconnect
    socket.on('disconnect', () => {
      logger.info('Client disconnected', { socketId: socket.id });
    });

    // Handle manual snipe request
    socket.on('execute_snipe', async (data: { snipeId: string }) => {
      try {
        const snipe = await Snipe.findById(data.snipeId);
        if (!snipe) {
          socket.emit('error', { message: 'Snipe not found' });
          return;
        }

        // Execute the snipe
        executeSnipe(snipe, bot).then(result => {
          socket.emit('snipe_executed', {
            snipeId: data.snipeId,
            result
          });
        }).catch(error => {
          socket.emit('error', { message: (error as Error).message });
        });

        socket.emit('snipe_queued', {
          snipeId: data.snipeId,
          message: 'Snipe has been queued for execution'
        });

        logger.info(`Manual snipe queued for ${snipe.contractAddress}`, {
          snipeId: data.snipeId,
          blockchain: snipe.blockchain
        });
      } catch (error) {
        logger.error('Error executing manual snipe', {
          error: (error as Error).message,
          snipeId: data.snipeId
        });
        socket.emit('error', { message: (error as Error).message });
      }
    });

    // Add health check endpoint
    socket.on('health_check', async () => {
      try {
        const health = {
          status: 'healthy',
          uptime: process.uptime(),
          timestamp: new Date().toISOString()
        };

        socket.emit('health_status', health);
      } catch (error) {
        logger.error('Error sending health status', {
          error: (error as Error).message
        });
        socket.emit('error', { message: (error as Error).message });
      }
    });
  });

  // Start the server
  server.listen(port, () => {
    logger.info(`Socket.IO server is running on port ${port}`);
  });

  // Start periodic updates
  startPeriodicSnipeUpdates(bot);

  // Register shutdown handler
  process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    await gracefulShutdown();
  });

  process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    await gracefulShutdown();
  });
};
