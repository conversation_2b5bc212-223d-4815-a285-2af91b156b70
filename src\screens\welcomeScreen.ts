import TelegramBot from 'node-telegram-bot-api';
import User from '../models/user.models';
import Trade from '../models/trade.model';
import { generateBSCWallet, generateBASEWallet, generateETHWallet, generateSOLWallet } from '../services/wallet.service';

const MAX_RETRIES = 5;

const newUserHandler = async (bot: TelegramBot, msg: TelegramBot.Message) => {
    const { username, id: chat_id, first_name, last_name } = msg.chat;
  
    let retries = 0;
    let user: any = null;
  
    // Check if the user already exists in the database
    user = await User.findOne({ chat_id });

    if (user) {
        const caption =
          `👋 Welcome back to EasyBot!\n\n` +
          `Here are your wallet addresses:\n\n` +
          `<b>SOL Wallet Address:</b> <code>${user.sol_wallet.address}</code>\n` +
          `<b>BSC Wallet Address:</b> <code>${user.bsc_wallet.address}</code>\n` +
          `<b>BASE Wallet Address:</b> <code>${user.base_wallet.address}</code>\n` +
          `<b>ETH Wallet Address:</b> <code>${user.eth_wallet.address}</code>\n\n` +
          `<b>Fire /privatekeys to view</b>\n\n` 
         
          ;
  
        await bot.sendMessage(chat_id, caption, {
          parse_mode: "HTML",
          disable_web_page_preview: true,
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: "BSC",
                  callback_data: JSON.stringify({ command: "view_bsc" }),
                },
                {
                  text: "BASE",
                  callback_data: JSON.stringify({ command: "view_base" }),
                },
              ],
              [
                {
                  text: "SOL",
                  callback_data: JSON.stringify({ command: "view_sol" }),
                },
                {
                  text: "ETH",
                  callback_data: JSON.stringify({ command: "view_eth" }),
                },
              ],
              [
                {
                  text: "* Dismiss message",
                  callback_data: JSON.stringify({
                    command: "dismiss_message",
                  }),
                },
              ],
            ],
          },
        });
        return;
    }

    // Generate new wallets and create a new user
    let newUser: any;
    do {
        const solWallet = generateSOLWallet();
        const bscWallet = generateBSCWallet();
        const baseWallet = generateBASEWallet();
        const ethWallet = generateETHWallet();
    
        newUser = new User({
          chat_id,
          first_name,
          last_name,
          username,
          sol_wallet: {
            address: solWallet.address,
            private_key: solWallet.privateKey,
          },
          bsc_wallet: {
            address: bscWallet.address,
            private_key: bscWallet.privateKey,
          },
          base_wallet: {
            address: baseWallet.address,
            private_key: baseWallet.privateKey,
          },
          eth_wallet: {
            address: ethWallet.address,
            private_key: ethWallet.privateKey,
          },
          trades: {
            bsc: [],
            sol: [],
            eth: [],
            base: [],
          },
        });
    
        user = await newUser.save();
    
        // Create default trades and associate them with the user’s wallets
        for (const walletType of ['bsc', 'sol', 'eth', 'base']) {
          const walletId = user[`${walletType}_wallet`].address;
          const newTrade = new Trade({
            userId: user._id,
            walletId: walletId,
            contractName: "Empty",
            buyAmount: 0,
            sellAmount: 0,
            takeProfit: 0,
            stopLoss: 0,
          });
    
          await newTrade.save();
          await User.updateOne({ _id: user._id }, {
            $push: {
              [`trades.${walletType}`]: newTrade._id,
            },
          });
        }
    
        const caption =
          `👋 Welcome to EasyBot!\n\n` +
          `Here are your wallet addresses:\n\n` +
          `<b>SOL Wallet Address:</b> <code>${user.sol_wallet.address}</code>\n` +
          `<b>BSC Wallet Address:</b> <code>${user.bsc_wallet.address}</code>\n` +
          `<b>BASE Wallet Address:</b> <code>${user.base_wallet.address}</code>\n` +
          `<b>ETH Wallet Address:</b> <code>${user.eth_wallet.address}</code>\n\n` +
          `<b>Save these private keys below:</b>\n\n` +
          `<tg-spoiler>SOL: <code>${user.sol_wallet.private_key}</code></tg-spoiler>\n` +
          `<tg-spoiler>BSC: <code>${user.bsc_wallet.private_key}</code></tg-spoiler>\n` +
          `<tg-spoiler>BASE: <code>${user.base_wallet.private_key}</code></tg-spoiler>\n` +
          `<tg-spoiler>ETH: <code>${user.eth_wallet.private_key}</code></tg-spoiler>\n\n` +
          `<b>To get started, please read our <a href="https://docs.io">docs</a></b>`;
    
        await bot.sendMessage(chat_id, caption, {
          parse_mode: "HTML",
          disable_web_page_preview: true,
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: "BSC",
                  callback_data: JSON.stringify({ command: "view_bsc" }),
                },
                {
                  text: "BASE",
                  callback_data: JSON.stringify({ command: "view_base" }),
                },
              ],
              [
                {
                  text: "SOL",
                  callback_data: JSON.stringify({ command: "view_sol" }),
                },
                {
                  text: "ETH",
                  callback_data: JSON.stringify({ command: "view_eth" }),
                },
              ],
              [
                {
                  text: "* Dismiss message",
                  callback_data: JSON.stringify({
                    command: "dismiss_message",
                  }),
                },
              ],
            ],
          },
        });
    } while (user === null && retries++ < MAX_RETRIES);
  
    if (user === null) {
      await bot.sendMessage(chat_id, "Failed to create your wallet. Please try again later.");
    }
};

export default newUserHandler;
