/**
 * Cache Service Module
 *
 * This module provides a caching interface that can be implemented by different
 * cache providers. Currently, it uses an in-memory cache, but it's designed to
 * easily switch to Redis or other cache providers in the future.
 */

import { logger } from '../logger/LoggerService';

export interface CacheService {
  get<T = any>(key: string): Promise<T | null>;
  set<T = any>(key: string, value: T, ttlSeconds?: number): Promise<void>;
  del(key: string): Promise<void>;
  exists(key: string): Promise<boolean>;
  flushAll(): Promise<void>;
  getMulti<T = any>(keys: string[]): Promise<(T | null)[]>;
  setMulti<T = any>(items: Array<{ key: string; value: T; ttlSeconds?: number }>): Promise<void>;
  delMulti(keys: string[]): Promise<void>;
}

/**
 * In-memory cache implementation
 * Efficient for development and small-scale deployments
 */
class InMemoryCache implements CacheService {
  private cache: Map<string, { value: any; expiry: number | null }> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Periodically clean up expired items (every 5 minutes)
    this.cleanupInterval = setInterval(() => this.cleanupExpiredItems(), 5 * 60 * 1000);
  }

  private cleanupExpiredItems(): void {
    const now = Date.now();
    let expiredCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (item.expiry && item.expiry < now) {
        this.cache.delete(key);
        expiredCount++;
      }
    }

    if (expiredCount > 0) {
      logger.debug(`Cache cleanup: removed ${expiredCount} expired items`);
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    const item = this.cache.get(key);
    if (!item) return null;

    // Check if expired
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return item.value as T;
  }

  async set<T = any>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    const expiry = ttlSeconds ? Date.now() + ttlSeconds * 1000 : null;
    this.cache.set(key, { value, expiry });
  }

  async del(key: string): Promise<void> {
    this.cache.delete(key);
  }

  async exists(key: string): Promise<boolean> {
    const item = this.cache.get(key);
    if (!item) return false;

    // Check if expired
    if (item.expiry && item.expiry < Date.now()) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  async flushAll(): Promise<void> {
    this.cache.clear();
  }

  async getMulti<T = any>(keys: string[]): Promise<(T | null)[]> {
    return Promise.all(keys.map(key => this.get<T>(key)));
  }

  async setMulti<T = any>(items: Array<{ key: string; value: T; ttlSeconds?: number }>): Promise<void> {
    for (const item of items) {
      await this.set(item.key, item.value, item.ttlSeconds);
    }
  }

  async delMulti(keys: string[]): Promise<void> {
    for (const key of keys) {
      this.cache.delete(key);
    }
  }

  // Clean up resources when the application shuts down
  shutdown(): void {
    clearInterval(this.cleanupInterval);
  }
}

/**
 * Redis cache implementation (placeholder)
 * Will be fully implemented when Redis is needed
 */
class RedisCache implements CacheService {
  // Connection details would go here
  private client: any = null;

  constructor() {
    logger.info('Redis cache initialized (placeholder)');
  }

  async get<T = any>(key: string): Promise<T | null> {
    logger.warn('Redis cache is not fully implemented yet');
    return null;
  }

  async set<T = any>(key: string, value: T, ttlSeconds?: number): Promise<void> {
    logger.warn('Redis cache is not fully implemented yet');
  }

  async del(key: string): Promise<void> {
    logger.warn('Redis cache is not fully implemented yet');
  }

  async exists(key: string): Promise<boolean> {
    logger.warn('Redis cache is not fully implemented yet');
    return false;
  }

  async flushAll(): Promise<void> {
    logger.warn('Redis cache is not fully implemented yet');
  }

  async getMulti<T = any>(keys: string[]): Promise<(T | null)[]> {
    logger.warn('Redis cache is not fully implemented yet');
    return keys.map(() => null);
  }

  async setMulti<T = any>(items: Array<{ key: string; value: T; ttlSeconds?: number }>): Promise<void> {
    logger.warn('Redis cache is not fully implemented yet');
  }

  async delMulti(keys: string[]): Promise<void> {
    logger.warn('Redis cache is not fully implemented yet');
  }
}

/**
 * Factory to get the appropriate cache implementation
 * This allows for easy switching between cache providers
 */
export class CacheFactory {
  private static instance: CacheService;

  static getCache(): CacheService {
    if (!this.instance) {
      // Check if Redis is enabled (will be set in environment variables)
      const useRedis = process.env.USE_REDIS === 'true';

      if (useRedis) {
        logger.info('Redis cache is enabled in config but not fully implemented yet. Using in-memory cache instead.');
        this.instance = new InMemoryCache();
      } else {
        logger.info('Using in-memory cache');
        this.instance = new InMemoryCache();
      }
    }

    return this.instance;
  }

  static shutdown(): void {
    if (this.instance instanceof InMemoryCache) {
      (this.instance as InMemoryCache).shutdown();
    }
  }
}

// Export a singleton instance
export const cacheService = CacheFactory.getCache();
