/**
 * Services Module
 *
 * This module exports all services used in the application.
 * Services are organized into the following categories:
 *
 * - Core: Essential services like logging and caching
 * - Blockchain: Services for interacting with blockchains
 * - Trading: Services for trading tokens
 * - Token: Services for token information
 * - Utils: Utility services
 */

// Export core services
export { logger } from './logger/LoggerService';
export { cacheService, CacheFactory } from './cache/RedisService';

// Export blockchain services
export * from './blockchain/index';

// Export trading services
export * from './trading/TradingInterface';
export * from './trading/index';

// Export token services
export * from './token/TokenInfoService';

// Export honeypot services
export * from './honeypot/UnifiedHoneypotChecker';

// Export utility services
export * from './utils/index';

// Export callback query handler
export * from './callbackquery';

/**
 * Initialize all services
 * This function should be called at application startup
 * @param bot The Telegram bot instance (optional)
 */
export function initializeServices(bot?: any): void {
  // Import services
  import('./logger/LoggerService').then(({ logger }) => {
    logger.info('Initializing services...');

    // Initialize cache
    import('./cache/RedisService').then(({ cacheService }) => {
      logger.info('Cache service initialized');

      // Initialize blockchain services
      import('./blockchain/index').then(({ initializeBlockchainServices }) => {
        initializeBlockchainServices();

        // Initialize trading services if bot is provided
        if (bot) {
          import('./trading/index').then(({ initializeTraders }) => {
            initializeTraders(bot);
            logger.info('Trading services initialized');
          });
        }

        // Initialize utility services
        import('./utils/index').then(({ initializeUtilityServices }) => {
          initializeUtilityServices();

          // Log initialization complete
          logger.info('All services initialized successfully');
        });
      });
    });
  });
}

/**
 * Shutdown all services
 * This function should be called before application shutdown
 */
export function shutdownServices(): void {
  // Import services for shutdown
  import('./logger/LoggerService').then(({ logger }) => {
    logger.info('Shutting down services...');

    // Shutdown cache
    import('./cache/RedisService').then(({ CacheFactory }) => {
      CacheFactory.shutdown();

      // Shutdown logger last
      logger.info('All services shut down successfully');
      logger.shutdown();
    });
  });
}
