/**
 * Wallet Balance Model
 *
 * This model tracks wallet balances across different blockchains
 * and provides real-time notifications when balances change.
 */

import mongoose, { Document } from 'mongoose';
import { logger } from '../services/logger/LoggerService';
import TelegramBot from 'node-telegram-bot-api';
import { BotService } from '../services/bot/BotService';
import { sendToUser, isUserConnected } from '../utils/socket.utils';

// Define the blockchain types
export enum BlockchainType {
  BSC = 'bsc',
  ETH = 'eth',
  SOL = 'sol',
  BASE = 'base'
}

// Define the transaction type
export enum TransactionType {
  INCOMING = 'incoming',
  OUTGOING = 'outgoing',
  UNKNOWN = 'unknown'
}

// Define the wallet balance interface
export interface IWalletBalance extends Document {
  userId: mongoose.Schema.Types.ObjectId;
  chatId: number;
  walletAddress: string;
  blockchain: BlockchainType;
  balance: string;
  previousBalance: string;
  lastUpdated: Date;
  lastTransaction?: {
    hash: string;
    type: TransactionType;
    amount: string;
    timestamp: Date;
  };
}

// Create the schema
const WalletBalanceSchema = new mongoose.Schema<IWalletBalance>({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  chatId: {
    type: Number,
    required: true
  },
  walletAddress: {
    type: String,
    required: true
  },
  blockchain: {
    type: String,
    required: true,
    enum: Object.values(BlockchainType)
  },
  balance: {
    type: String,
    required: true,
    default: '0'
  },
  previousBalance: {
    type: String,
    default: '0'
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  lastTransaction: {
    hash: String,
    type: {
      type: String,
      enum: Object.values(TransactionType)
    },
    amount: String,
    timestamp: Date
  }
});

// Create indexes for faster queries
WalletBalanceSchema.index({ walletAddress: 1, blockchain: 1 }, { unique: true });
WalletBalanceSchema.index({ userId: 1 });
WalletBalanceSchema.index({ chatId: 1 });

// Define static methods interface
interface WalletBalanceModel extends mongoose.Model<IWalletBalance> {
  updateBalance(
    walletAddress: string,
    blockchain: BlockchainType,
    newBalance: string,
    txHash?: string,
    txType?: TransactionType
  ): Promise<IWalletBalance | null>;

  sendBalanceAlert(
    bot: TelegramBot,
    walletBalance: IWalletBalance
  ): Promise<void>;
}

// Static methods
WalletBalanceSchema.statics.updateBalance = async function(
  walletAddress: string,
  blockchain: BlockchainType,
  newBalance: string,
  txHash?: string,
  txType?: TransactionType
): Promise<IWalletBalance | null> {
  try {
    // Find the wallet balance record
    const walletBalance = await this.findOne({ walletAddress, blockchain });

    if (!walletBalance) {
      logger.warn(`No wallet balance record found for ${walletAddress} on ${blockchain}`);
      return null;
    }

    // Calculate the difference
    const oldBalance = walletBalance.balance;
    const difference = (parseFloat(newBalance) - parseFloat(oldBalance)).toString();

    // Update the record
    walletBalance.previousBalance = oldBalance;
    walletBalance.balance = newBalance;
    walletBalance.lastUpdated = new Date();

    // Add transaction info if available
    if (txHash) {
      walletBalance.lastTransaction = {
        hash: txHash,
        type: txType || TransactionType.UNKNOWN,
        amount: Math.abs(parseFloat(difference)).toString(),
        timestamp: new Date()
      };
    }

    await walletBalance.save();
    return walletBalance;
  } catch (error) {
    logger.error(`Error updating wallet balance for ${walletAddress} on ${blockchain}`, {
      error: (error as Error).message,
      walletAddress,
      blockchain,
      newBalance
    });
    return null;
  }
};



// Method to send balance alert
WalletBalanceSchema.statics.sendBalanceAlert = async function(
  bot: TelegramBot,
  walletBalance: IWalletBalance
): Promise<void> {
  try {
    const botService = new BotService(bot);

    // Format the message
    const formattedOldBalance = parseFloat(walletBalance.previousBalance).toFixed(6);
    const formattedNewBalance = parseFloat(walletBalance.balance).toFixed(6);
    const difference = (parseFloat(walletBalance.balance) - parseFloat(walletBalance.previousBalance)).toFixed(6);
    const isIncrease = parseFloat(difference) > 0;

    // Skip if there's no significant change
    if (Math.abs(parseFloat(difference)) < 0.000001) {
      return;
    }

    // Create a message with emoji indicators
    const message =
      `${isIncrease ? '📈 Balance Increased' : '📉 Balance Decreased'}\n\n` +
      `Wallet: ${walletBalance.walletAddress.substring(0, 6)}...${walletBalance.walletAddress.substring(walletBalance.walletAddress.length - 4)}\n` +
      `Network: ${walletBalance.blockchain.toUpperCase()}\n` +
      `Previous: ${formattedOldBalance}\n` +
      `Current: ${formattedNewBalance}\n` +
      `Change: ${isIncrease ? '+' : ''}${difference}`;

    // Add transaction link if available
    let explorerUrl = '';
    if (walletBalance.lastTransaction?.hash) {
      const txHash = walletBalance.lastTransaction.hash;
      switch (walletBalance.blockchain) {
        case BlockchainType.BSC: explorerUrl = `https://bscscan.com/tx/${txHash}`; break;
        case BlockchainType.ETH: explorerUrl = `https://etherscan.io/tx/${txHash}`; break;
        case BlockchainType.BASE: explorerUrl = `https://basescan.org/tx/${txHash}`; break;
        case BlockchainType.SOL: explorerUrl = `https://solscan.io/tx/${txHash}`; break;
      }
    }

    // Create inline keyboard with transaction link
    const keyboard = walletBalance.lastTransaction?.hash ? {
      inline_keyboard: [
        [{ text: '🔍 View Transaction', url: explorerUrl }],
        [{ text: '❌ Dismiss', callback_data: JSON.stringify({ command: 'dismiss_message' }) }]
      ]
    } : undefined;

    // Send as a silent notification if it's a small change
    const options: any = {
      parse_mode: 'HTML',
      disable_notification: Math.abs(parseFloat(difference)) < 0.01, // Silent for small changes
      reply_markup: keyboard
    };

    // Send Telegram message
    await botService.sendMessage(walletBalance.chatId, message, options);

    // Also send via Socket.IO if the user is connected
    if (isUserConnected(walletBalance.chatId)) {
      // Prepare data for Socket.IO
      const socketData = {
        walletAddress: walletBalance.walletAddress,
        blockchain: walletBalance.blockchain,
        previousBalance: formattedOldBalance,
        currentBalance: formattedNewBalance,
        difference: difference,
        isIncrease: isIncrease,
        timestamp: new Date().toISOString(),
        transaction: walletBalance.lastTransaction ? {
          hash: walletBalance.lastTransaction.hash,
          type: walletBalance.lastTransaction.type,
          amount: walletBalance.lastTransaction.amount,
          explorerUrl: explorerUrl
        } : null
      };

      // Send to user's socket connections
      sendToUser(walletBalance.chatId, socketData, 'walletBalanceUpdate');
    }
  } catch (error) {
    logger.error(`Error sending balance alert for ${walletBalance.walletAddress}`, {
      error: (error as Error).message,
      walletAddress: walletBalance.walletAddress,
      blockchain: walletBalance.blockchain,
      chatId: walletBalance.chatId
    });
  }
};

// Create and export the model
const WalletBalance = mongoose.model<IWalletBalance, WalletBalanceModel>('WalletBalance', WalletBalanceSchema);
export default WalletBalance;
